/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.6.2 (2020-12-08)
 */
!function(){"use strict";var x=function(){return(x=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};function c(n,e){var t={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.indexOf(r)<0&&(t[r]=n[r]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(n);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(n,r[o])&&(t[r[o]]=n[r[o]]);return t}function u(){for(var n=0,e=0,t=arguments.length;e<t;e++)n+=arguments[e].length;for(var r=Array(n),o=0,e=0;e<t;e++)for(var i=arguments[e],u=0,c=i.length;u<c;u++,o++)r[o]=i[u];return r}var O=function(){},i=function(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))}},y=function(n){return function(){return n}},v=function(n){return n};function b(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}var n,e,t,r,o,a,m=function(e){return function(n){return!e(n)}},f=function(n){return function(){throw new Error(n)}},s=function(n){return n()},l=y(!1),w=y(!0),d=function(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}},g=function(){return p},p=(n=function(n){return n.isNone()},{fold:function(n,e){return n()},is:l,isSome:l,isNone:w,getOr:t=function(n){return n},getOrThunk:e=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:y(null),getOrUndefined:y(undefined),or:t,orThunk:e,map:g,each:O,bind:g,exists:l,forall:w,filter:g,equals:n,equals_:n,toArray:function(){return[]},toString:y("none()")}),h=function(t){var n=y(t),e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:w,isNone:l,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return h(n(t))},each:function(n){n(t)},bind:r,exists:r,forall:r,filter:function(n){return n(t)?o:p},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(l,function(n){return e(t,n)})}};return o},T={some:h,none:g,from:function(n){return null===n||n===undefined?p:h(n)}},S=function(r){return function(n){return t=typeof(e=n),(null===e?"null":"object"==t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t)===r;var e,t}},k=function(e){return function(n){return typeof n===e}},E=S("string"),C=S("object"),M=S("array"),D=k("boolean"),_=function(n){return!(null===(e=n)||e===undefined);var e},I=k("function"),F=k("number"),R=Array.prototype.slice,V=Array.prototype.indexOf,B=Array.prototype.push,A=function(n,e){return t=n,r=e,-1<V.call(t,r);var t,r},j=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1},N=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o)}return r},P=function(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}},H=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r)&&t.push(i)}return t},z=function(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--){e(n[t],t)}}(n,function(n){t=e(t,n)}),t},L=function(n,e,t){return P(n,function(n){t=e(t,n)}),t},G=function(n,e){return function(n,e,t){for(var r=0,o=n.length;r<o;r++){var i=n[r];if(e(i,r))return T.some(i);if(t(i,r))break}return T.none()}(n,e,l)},$=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return T.some(t)}return T.none()},U=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!M(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);B.apply(e,n[t])}return e},W=function(n,e){return U(N(n,e))},X=function(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0},q=function(n){var e=R.call(n,0);return e.reverse(),e},Y=function(n,e){return H(n,function(n){return!A(e,n)})},K=function(n){return[n]},J=function(n){return e=n,(t=0)<=t&&t<e.length?T.some(e[t]):T.none();var e,t},Q=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return T.none()},Z=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return en(r(1),r(2))},nn=function(){return en(0,0)},en=function(n,e){return{major:n,minor:e}},tn={nu:en,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?nn():Z(n,t)},unknown:nn},rn=function(n,e){var t=String(e).toLowerCase();return G(n,function(n){return n.search(t)})},on=function(n,t){return rn(n,t).map(function(n){var e=tn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},un=function(n,t){return rn(n,t).map(function(n){var e=tn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},cn=function(n,o){return n.replace(/\$\{([^{}]*)\}/g,function(n,e){var t,r=o[e];return"string"==(t=typeof r)||"number"==t?r.toString():n})},an=function(n,e){return-1!==n.indexOf(e)},fn=(r=/^\s+|\s+$/g,function(n){return n.replace(r,"")}),sn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ln=function(e){return function(n){return an(n,e)}},dn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return an(n,"edge/")&&an(n,"chrome")&&an(n,"safari")&&an(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,sn],search:function(n){return an(n,"chrome")&&!an(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return an(n,"msie")||an(n,"trident")}},{name:"Opera",versionRegexes:[sn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ln("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ln("firefox")},{name:"Safari",versionRegexes:[sn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(an(n,"safari")||an(n,"mobile/"))&&an(n,"applewebkit")}}],mn=[{name:"Windows",search:ln("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return an(n,"iphone")||an(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ln("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:ln("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ln("linux"),versionRegexes:[]},{name:"Solaris",search:ln("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ln("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:ln("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],gn={browsers:y(dn),oses:y(mn)},pn="Firefox",hn=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isEdge:r("Edge"),isChrome:r("Chrome"),isIE:r("IE"),isOpera:r("Opera"),isFirefox:r(pn),isSafari:r("Safari")}},vn={unknown:function(){return hn({current:undefined,version:tn.unknown()})},nu:hn,edge:y("Edge"),chrome:y("Chrome"),ie:y("IE"),opera:y("Opera"),firefox:y(pn),safari:y("Safari")},yn="Windows",bn="Android",xn="Solaris",wn="FreeBSD",Sn="ChromeOS",On=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isWindows:r(yn),isiOS:r("iOS"),isAndroid:r(bn),isOSX:r("OSX"),isLinux:r("Linux"),isSolaris:r(xn),isFreeBSD:r(wn),isChromeOS:r(Sn)}},Tn={unknown:function(){return On({current:undefined,version:tn.unknown()})},nu:On,windows:y(yn),ios:y("iOS"),android:y(bn),linux:y("Linux"),osx:y("OSX"),solaris:y(xn),freebsd:y(wn),chromeos:y(Sn)},kn=function(n,e){var t,r,o,i,u,c,a,f,s,l,d,m,g=gn.browsers(),p=gn.oses(),h=on(g,n).fold(vn.unknown,vn.nu),v=un(p,n).fold(Tn.unknown,Tn.nu);return{browser:h,os:v,deviceType:(r=h,o=n,i=e,u=(t=v).isiOS()&&!0===/ipad/i.test(o),c=t.isiOS()&&!u,a=t.isiOS()||t.isAndroid(),f=a||i("(pointer:coarse)"),s=u||!c&&a&&i("(min-device-width:768px)"),l=c||a&&!s,d=r.isSafari()&&t.isiOS()&&!1===/safari/i.test(o),m=!l&&!s&&!d,{isiPad:y(u),isiPhone:y(c),isTablet:y(s),isPhone:y(l),isTouch:y(f),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:y(d),isDesktop:y(m)})}},En=function(n){return window.matchMedia(n).matches},Cn=d(function(){return kn(navigator.userAgent,En)}),Mn=function(){return Cn()},Dn=y,_n=Dn("touchstart"),In=Dn("touchmove"),Fn=Dn("touchend"),Rn=Dn("mousedown"),Vn=Dn("mousemove"),Bn=Dn("mouseup"),An=Dn("mouseover"),jn=Dn("keydown"),Nn=Dn("keyup"),Pn=Dn("input"),Hn=Dn("change"),zn=Dn("click"),Ln=Dn("transitionend"),Gn=Dn("selectstart"),$n=function(n){return y("alloy."+n)},Un={tap:$n("tap")},Wn=$n("focus"),Xn=$n("blur.post"),qn=$n("paste.post"),Yn=$n("receive"),Kn=$n("execute"),Jn=$n("focus.item"),Qn=Un.tap,Zn=$n("longpress"),ne=$n("system.init"),ee=$n("system.attached"),te=$n("system.detached"),re=$n("focusmanager.shifted"),oe=$n("highlight"),ie=$n("dehighlight"),ue=function(n,e){se(n,n.element,e,{})},ce=function(n,e,t){se(n,n.element,e,t)},ae=function(n){ue(n,Kn())},fe=function(n,e,t){se(n,e,t,{})},se=function(n,e,t,r){var o=x({target:e},r);n.getSystem().triggerEvent(t,e,o)},le=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:n}},de={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return le(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return le(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return le(t)},fromDom:le,fromPoint:function(n,e,t){return T.from(n.dom.elementFromPoint(e,t)).map(le)}},me=function(n,e){var t=n.dom;if(1!==t.nodeType)return!1;var r=t;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},ge=function(n){return 1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType||0===n.childElementCount},pe=function(n,e){var t=e===undefined?document:e.dom;return ge(t)?[]:N(t.querySelectorAll(n),de.fromDom)},he=function(n,e){var t=e===undefined?document:e.dom;return ge(t)?T.none():T.from(t.querySelector(n)).map(de.fromDom)},ve=function(n,e){return n.dom===e.dom},ye=("undefined"!=typeof window||Function("return this;")(),function(n){return n.dom.nodeName.toLowerCase()}),be=function(e){return function(n){return n.dom.nodeType===e}},xe=be(1),we=be(3),Se=be(9),Oe=be(11),Te=function(n){return de.fromDom(n.dom.ownerDocument)},ke=function(n){return Se(n)?n:Te(n)},Ee=function(n){return T.from(n.dom.parentNode).map(de.fromDom)},Ce=function(n){return N(n.dom.childNodes,de.fromDom)},Me=function(n,e){var t=n.dom.childNodes;return T.from(t[e]).map(de.fromDom)},De=function(e,t){Ee(e).each(function(n){n.dom.insertBefore(t.dom,e.dom)})},_e=function(n,e){var t;(t=n,T.from(t.dom.nextSibling).map(de.fromDom)).fold(function(){Ee(n).each(function(n){Fe(n,e)})},function(n){De(n,e)})},Ie=function(e,t){Me(e,0).fold(function(){Fe(e,t)},function(n){e.dom.insertBefore(t.dom,n.dom)})},Fe=function(n,e){n.dom.appendChild(e.dom)},Re=function(e,n){P(n,function(n){Fe(e,n)})},Ve=function(n){n.dom.textContent="",P(Ce(n),function(n){Be(n)})},Be=function(n){var e=n.dom;null!==e.parentNode&&e.parentNode.removeChild(e)},Ae=I(Element.prototype.attachShadow)&&I(Node.prototype.getRootNode),je=y(Ae),Ne=Ae?function(n){return de.fromDom(n.dom.getRootNode())}:ke,Pe=function(n){var e=Ne(n);return Oe(e)?T.some(e):T.none()},He=function(n){return de.fromDom(n.dom.host)},ze=function(n){return _(n.dom.shadowRoot)},Le=function(n){var e=we(n)?n.dom.parentNode:n.dom;if(e===undefined||null===e||null===e.ownerDocument)return!1;var t,r,o=e.ownerDocument;return Pe(de.fromDom(e)).fold(function(){return o.body.contains(e)},(t=Le,r=He,function(n){return t(r(n))}))},Ge=function(){return $e(de.fromDom(document))},$e=function(n){var e=n.dom.body;if(null===e||e===undefined)throw new Error("Body is not available yet");return de.fromDom(e)},Ue=function(n){ue(n,te());var e=n.components();P(e,Ue)},We=function(n){var e=n.components();P(e,We),ue(n,ee())},Xe=function(n,e){Fe(n.element,e.element)},qe=function(e,n){var t,r=e.components();P((t=e).components(),function(n){return Be(n.element)}),Ve(t.element),t.syncComponents();var o=Y(r,n);P(o,function(n){Ue(n),e.getSystem().removeFromWorld(n)}),P(n,function(n){n.getSystem().isConnected()?Xe(e,n):(e.getSystem().addToWorld(n),Xe(e,n),Le(e.element)&&We(n)),e.syncComponents()})},Ye=function(n,e,t){n.getSystem().addToWorld(e),t(n.element,e.element),Le(n.element)&&We(e),n.syncComponents()},Ke=function(e){var n,t=Ee(e.element).bind(function(n){return e.getSystem().getByDom(n).toOptional()});Ue(n=e),Be(n.element),n.getSystem().removeFromWorld(n),t.each(function(n){n.syncComponents()})},Je=function(n,e,t){t(n,e.element);var r=Ce(e.element);P(r,function(n){e.getByDom(n).each(We)})},Qe=Object.keys,Ze=Object.hasOwnProperty,nt=function(n,e){for(var t=Qe(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i)}},et=function(n,t){return tt(n,function(n,e){return{k:e,v:t(n,e)}})},tt=function(n,r){var o={};return nt(n,function(n,e){var t=r(n,e);o[t.k]=t.v}),o},rt=function(n,e){var t,r,o,i,u={};return t=e,i=u,r=function(n,e){i[e]=n},o=O,nt(n,function(n,e){(t(n,e)?r:o)(n,e)}),u},ot=function(n,t){var r=[];return nt(n,function(n,e){r.push(t(n,e))}),r},it=function(n,e){return ut(n,e)?T.from(n[e]):T.none()},ut=function(n,e){return Ze.call(n,e)},ct=function(n,e){return ut(n,e)&&n[e]!==undefined&&null!==n[e]},at=function(n,e,t){if(!(E(t)||D(t)||F(t)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},ft=function(n,e,t){at(n.dom,e,t)},st=function(n,e){var t=n.dom;nt(e,function(n,e){at(t,e,n)})},lt=function(n,e){var t=n.dom.getAttribute(e);return null===t?undefined:t},dt=function(n,e){return T.from(lt(n,e))},mt=function(n,e){var t=n.dom;return!(!t||!t.hasAttribute)&&t.hasAttribute(e)},gt=function(n,e){n.dom.removeAttribute(e)},pt=function(n,e){var t=lt(n,e);return t===undefined||""===t?[]:t.split(" ")},ht=function(n){return n.dom.classList!==undefined},vt=function(n,e){return o=e,i=pt(t=n,r="class").concat([o]),ft(t,r,i.join(" ")),!0;var t,r,o,i},yt=function(n,e){return o=e,0<(i=H(pt(t=n,r="class"),function(n){return n!==o})).length?ft(t,r,i.join(" ")):gt(t,r),!1;var t,r,o,i},bt=function(n,e){ht(n)?n.dom.classList.add(e):vt(n,e)},xt=function(n){0===(ht(n)?n.dom.classList:pt(n,"class")).length&&gt(n,"class")},wt=function(n,e){ht(n)?n.dom.classList.remove(e):yt(n,e),xt(n)},St=function(n,e){return ht(n)&&n.dom.classList.contains(e)},Ot=function(n,e,t){wt(n,t),bt(n,e)},Tt=/* */Object.freeze({__proto__:null,toAlpha:function(n,e,t){Ot(n.element,e.alpha,e.omega)},toOmega:function(n,e,t){Ot(n.element,e.omega,e.alpha)},isAlpha:function(n,e,t){return St(n.element,e.alpha)},isOmega:function(n,e,t){return St(n.element,e.omega)},clear:function(n,e,t){wt(n.element,e.alpha),wt(n.element,e.omega)}}),kt=function(t){return{is:function(n){return t===n},isValue:w,isError:l,getOr:y(t),getOrThunk:y(t),getOrDie:y(t),or:function(n){return kt(t)},orThunk:function(n){return kt(t)},fold:function(n,e){return e(t)},map:function(n){return kt(n(t))},mapError:function(n){return kt(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOptional:function(){return T.some(t)}}},Et=function(t){return{is:l,isValue:l,isError:w,getOr:v,getOrThunk:function(n){return n()},getOrDie:function(){return f(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return Et(t)},mapError:function(n){return Et(n(t))},each:O,bind:function(n){return Et(t)},exists:l,forall:w,toOptional:T.none}},Ct={value:kt,error:Et,fromOption:function(n,e){return n.fold(function(){return Et(e)},kt)}};(a=o=o||{})[a.Error=0]="Error",a[a.Value=1]="Value";var Mt,Dt,_t,It,Ft,Rt=function(n,e,t){return n.stype===o.Error?e(n.serror):t(n.svalue)},Vt=function(n){return{stype:o.Value,svalue:n}},Bt=function(n){return{stype:o.Error,serror:n}},At=function(n){return n.fold(Bt,Vt)},jt=function(n){return Rt(n,Ct.error,Ct.value)},Nt=Vt,Pt=function(n){var e=[],t=[];return P(n,function(n){Rt(n,function(n){return t.push(n)},function(n){return e.push(n)})}),{values:e,errors:t}},Ht=Bt,zt=function(n,e){return n.stype===o.Value?e(n.svalue):n},Lt=function(n,e){return n.stype===o.Error?e(n.serror):n},Gt=function(n,e){return n.stype===o.Value?{stype:o.Value,svalue:e(n.svalue)}:n},$t=function(n,e){return n.stype===o.Error?{stype:o.Error,serror:e(n.serror)}:n},Ut=function(u){if(!M(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return P(u,function(n,r){var e=Qe(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],i=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!M(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=Qe(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!X(c,function(n){return A(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},Wt=Object.prototype.hasOwnProperty,Xt=function(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o=n[r];for(var i in o)Wt.call(o,i)&&(t[i]=u(t[i],o[i]))}return t}},qt=Xt(function(n,e){return C(n)&&C(e)?qt(n,e):e}),Yt=Xt(function(n,e){return e}),Kt=Ut([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Jt=function(n){return Kt.defaultedThunk(y(n))},Qt=Kt.strict,Zt=Kt.asOption,nr=Kt.defaultedThunk,er=(Kt.asDefaultedOptionThunk,Kt.mergeWithThunk),tr=(Ut([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n,e){var t={};return t[n]=e,t}),rr=function(n,e){return t=e,r={},nt(n,function(n,e){A(t,e)||(r[e]=n)}),r;var t,r},or=tr,ir=function(n){return e={},P(n,function(n){e[n.key]=n.value}),e;var e},ur=function(n,e){var t,r,o,i,u,c=(t=[],r=[],P(n,function(n){n.fold(function(n){t.push(n)},function(n){r.push(n)})}),{errors:t,values:r});return 0<c.errors.length?(u=c.errors,Ct.error(U(u))):(i=e,0===(o=c.values).length?Ct.value(i):Ct.value(qt(i,Yt.apply(undefined,o))))},cr=function(n){return i(Ht,U)(n)},ar=function(n,e){var t,r,o=Pt(n);return 0<o.errors.length?cr(o.errors):(t=o.values,r=e,0<t.length?Nt(qt(r,Yt.apply(undefined,t))):Nt(r))},fr=function(n){var e=Pt(n);return 0<e.errors.length?cr(e.errors):Nt(e.values)},sr=function(n){return C(n)&&100<Qe(n).length?" removed due to size":JSON.stringify(n,null,2)},lr=function(n,e){return Ht([{path:n,getErrorInfo:e}])},dr=Ut([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),mr=function(t,r,o){return it(r,o).fold(function(){return n=o,e=r,lr(t,function(){return'Could not find valid *strict* value for "'+n+'" in '+sr(e)});var n,e},Nt)},gr=function(n,e,t){var r=it(n,e).fold(function(){return t(n)},v);return Nt(r)},pr=function(c,a,n,f){return n.fold(function(o,t,n,r){var i=function(n){var e=r.extract(c.concat([o]),f,n);return Gt(e,function(n){return tr(t,f(n))})},u=function(n){return n.fold(function(){var n=tr(t,f(T.none()));return Nt(n)},function(n){var e=r.extract(c.concat([o]),f,n);return Gt(e,function(n){return tr(t,f(T.some(n)))})})};return n.fold(function(){return zt(mr(c,a,o),i)},function(n){return zt(gr(a,o,n),i)},function(){return zt(Nt(it(a,o)),u)},function(n){return zt((t=n,r=it(e=a,o).map(function(n){return!0===n?t(e):n}),Nt(r)),u);var e,t,r},function(n){var e=n(a),t=Gt(gr(a,o,y({})),function(n){return qt(e,n)});return zt(t,i)})},function(n,e){var t=e(a);return Nt(tr(n,f(t)))})},hr=function(r){return{extract:function(t,n,e){return Lt(r(e,n),function(n){return e=n,lr(t,function(){return e});var e})},toString:function(){return"val"}}},vr=function(n){var u=yr(n),c=z(n,function(e,n){return n.fold(function(n){return qt(e,or(n,!0))},y(e))},{});return{extract:function(n,e,t){var r,o=D(t)?[]:Qe(rt(t,function(n){return n!==undefined&&null!==n})),i=H(o,function(n){return!ct(c,n)});return 0===i.length?u.extract(n,e,t):(r=i,lr(n,function(){return"There are unsupported fields: ["+r.join(", ")+"] specified"}))},toString:u.toString}},yr=function(c){return{extract:function(n,e,t){return r=n,o=t,i=e,u=N(c,function(n){return pr(r,o,n,i)}),ar(u,{});var r,o,i,u},toString:function(){return"obj{\n"+N(c,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"}}},br=function(t,i){var u=function(n,e){return o=hr(t),function(t,r,n){var e=N(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return fr(e)}(n,v,e);var o};return{extract:function(t,r,o){var n=Qe(o),e=u(t,n);return zt(e,function(n){var e=N(n,function(n){return dr.field(n,n,Qt(),i)});return yr(e).extract(t,r,o)})},toString:function(){return"setOf("+i.toString()+")"}}},xr=y(hr(Nt)),wr=dr.state,Sr=dr.field,Or=function(t,e,r,o,i){return it(o,i).fold(function(){return n=o,e=i,lr(t,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+sr(n)});var n,e},function(n){return n.extract(t.concat(["branch: "+i]),e,r)})},Tr=function(o,i){return{extract:function(e,t,r){return it(r,o).fold(function(){return n=o,lr(e,function(){return'Choice schema did not contain choice key: "'+n+'"'});var n},function(n){return Or(e,t,r,i,n)})},toString:function(){return"chooseOn("+o+"). Possible values: "+Qe(i)}}},kr=hr(Nt),Er=function(e){return hr(function(n){return e(n).fold(Ht,Nt)})},Cr=function(e,n){return br(function(n){return At(e(n))},n)},Mr=function(n,e,t){return jt((r=n,o=v,i=t,u=e.extract([r],o,i),$t(u,function(n){return{input:i,errors:n}})));var r,o,i,u},Dr=function(n){return n.fold(function(n){throw new Error(Ir(n))},v)},_r=function(n,e,t){return Dr(Mr(n,e,t))},Ir=function(n){return"Errors: \n"+(e=n.errors,t=10<e.length?e.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):e,N(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()}).join("\n"))+"\n\nInput object: "+sr(n.input);var e,t},Fr=function(n,e){return Tr(n,et(e,yr))},Rr=y(kr),Vr=(Mt=I,Dt="function",hr(function(n){var e=typeof n;return Mt(n)?Nt(n):Ht("Expected type: "+Dt+" but got: "+e)})),Br=function(n){return Sr(n,n,Qt(),xr())},Ar=function(n,e){return Sr(n,n,Qt(),e)},jr=function(n,e){return Sr(n,n,Qt(),yr(e))},Nr=function(n){return Sr(n,n,Zt(),xr())},Pr=function(n,e){return Sr(n,n,Zt(),e)},Hr=function(n,e){return Pr(n,yr(e))},zr=function(n,e){return Pr(n,vr(e))},Lr=function(n,e){return Sr(n,n,Jt(e),xr())},Gr=function(n,e,t){return Sr(n,n,Jt(e),t)},$r=function(n,e){return wr(n,e)},Ur=[Br("alpha"),Br("omega")],Wr=function(n){return I(n)?n:l},Xr=function(n,e,t){var r=e(n),o=Wr(t);return r.orThunk(function(){return o(n)?T.none():function(n,e,t){for(var r=n.dom,o=Wr(t);r.parentNode;){r=r.parentNode;var i=de.fromDom(r),u=e(i);if(u.isSome())return u;if(o(i))break}return T.none()}(n,e,o)})},qr=function(n,e){return ve(n.element,e.event.target)},Yr=function(n){if(!ct(n,"can")&&!ct(n,"abort")&&!ct(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return _r("Extracting event.handler",vr([Lr("can",w),Lr("abort",l),Lr("run",O)]),n)},Kr=function(t){var e,r,o,i,n=(r=function(n){return n.can},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return L(e,function(n,e){return n&&r(e).apply(undefined,t)},!0)}),u=(o=e=t,i=function(n){return n.abort},function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return L(o,function(n,e){return n||i(e).apply(undefined,t)},!1)});return Yr({can:n,abort:u,run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];P(t,function(n){n.run.apply(undefined,e)})}})},Jr=ir,Qr=function(n,e){return{key:n,value:Yr({abort:e})}},Zr=function(n,e){return{key:n,value:Yr({run:e})}},no=function(n,t,r){return{key:n,value:Yr({run:function(n,e){t.apply(undefined,[n,e].concat(r))}})}},eo=function(n){return function(t){return{key:n,value:Yr({run:function(n,e){qr(n,e)&&t(n,e)}})}}},to=function(u,e){return Zr(u,function(n,i){n.getSystem().getByUid(e).each(function(n){var e,t,r,o;t=(e=n).element,r=u,o=i,e.getSystem().triggerEvent(r,t,o.event)})})},ro=function(n,e,t){var r=e.partUids[t];return to(n,r)},oo=function(n){return Zr(n,function(n,e){e.cut()})},io=eo(ee()),uo=eo(te()),co=eo(ne()),ao=(_t=Kn(),function(n){return Zr(_t,n)}),fo=function(n){return N(n,function(n){return r=e="/*",o=(t=n).length-e.length,""===r||t.length>=r.length&&t.substr(o,o+r.length)===r?n.substring(0,n.length-"/*".length):n;var e,t,r,o})},so=function(n,e){var t=n.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:fo(i)}},n},lo=function(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}},mo=function(t,r,o){return co(function(n,e){o(n,t,r)})},go=function(o,i,u){var n,e,t,r,c,a;return n=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var r=[t].concat(n);return t.config({name:y(o)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+o+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(r,1);return i.apply(undefined,[t,n.config,n.state].concat(e))})},e=u,t=i.toString(),r=t.indexOf(")")+1,c=t.indexOf("("),a=t.substring(c+1,r-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:e,parameters:fo(a.slice(0,1).concat(a.slice(3)))}},n},po=function(n){return{key:n,value:undefined}},ho=function(t,n,r,o,e,i,u){var c=function(n){return ct(n,r)?n[r]():T.none()},a=et(e,function(n,e){return go(r,n,e)}),f=et(i,so),s=x(x(x({},f),a),{revoke:b(po,r),config:function(n){var e=_r(r+"-config",t,n);return{key:r,value:{config:e,me:s,configAsRaw:d(function(){return _r(r+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return c(n).bind(function(e){return it(o,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr(lo({}))},name:function(){return r},handlers:function(n){return c(n).map(function(n){return it(o,"events").getOr(function(){return{}})(n.config,n.state)}).getOr({})}});return s},vo={init:function(){return yo({readState:function(){return"No State required"}})}},yo=function(n){return n},bo=ir,xo=vr([Br("fields"),Br("name"),Lr("active",{}),Lr("apis",{}),Lr("state",vo),Lr("extra",{})]),wo=function(n){var e,t,r,o,i,u,c,a,f=_r("Creating behaviour: "+n.name,xo,n);return e=f.fields,t=f.name,r=f.active,o=f.apis,i=f.extra,u=f.state,c=vr(e),a=Hr(t,[zr("config",e)]),ho(c,a,t,r,o,i,u)},So=vr([Br("branchKey"),Br("branches"),Br("name"),Lr("active",{}),Lr("apis",{}),Lr("state",vo),Lr("extra",{})]),Oo=y(undefined),To=wo({fields:Ur,name:"swapping",apis:Tt}),ko=function(n){var e=n;return{get:function(){return e},set:function(n){e=n}}},Eo=function(n){return n.dom.focus()},Co=function(n){return n.dom.blur()},Mo=function(n){return void 0===n&&(n=de.fromDom(document)),T.from(n.dom.activeElement).map(de.fromDom)},Do=function(e){return Mo(Ne(e)).filter(function(n){return e.dom.contains(n.dom)})},_o=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Io=tinymce.util.Tools.resolve("tinymce.ThemeManager"),Fo=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],Ro=["undo","bold","italic","link","image","bullist","styleselect"],Vo="formatChanged",Bo="orientationChanged",Ao="dropupDismissed",jo=function(n){return n.dom.innerHTML},No=function(n,e){var t,r,o=Te(n).dom,i=de.fromDom(o.createDocumentFragment()),u=(t=e,(r=(o||document).createElement("div")).innerHTML=t,Ce(de.fromDom(r)));Re(i,u),Ve(n),Fe(n,i)},Po=function(n){return e=n,t=!1,de.fromDom(e.dom.cloneNode(t));var e,t},Ho=function(n){var e,t,r,o=Po(n);return e=o,t=de.fromTag("div"),r=de.fromDom(e.dom.cloneNode(!0)),Fe(t,r),jo(t)},zo=/* */Object.freeze({__proto__:null,events:function(a){return Jr([Zr(Yn(),function(o,n){var e,t,i=a.channels,r=Qe(i),u=n,c=(e=r,(t=u).universal?e:H(e,function(n){return A(t.channels,n)}));P(c,function(n){var e=i[n],t=e.schema,r=_r("channel["+n+"] data\nReceiver: "+Ho(o.element),t,u.data);e.onReceive(o,r)})})])}}),Lo="unknown";(Ft=It=It||{})[Ft.STOP=0]="STOP",Ft[Ft.NORMAL=1]="NORMAL",Ft[Ft.LOGGING=2]="LOGGING";var Go,$o=ko({}),Uo=["alloy/data/Fields","alloy/debugging/Debugging"],Wo=function(e,n,t){var r,o,i,u;switch(it($o.get(),e).orThunk(function(){var n=Qe($o.get());return Q(n,function(n){return-1<e.indexOf(n)?T.some($o.get()[n]):T.none()})}).getOr(It.NORMAL)){case It.NORMAL:return t(Xo());case It.LOGGING:var c=(r=e,o=n,i=[],u=(new Date).getTime(),{logEventCut:function(n,e,t){i.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){i.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){i.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){i.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){i.push({outcome:"response",purpose:t,target:e})},write:function(){var n=(new Date).getTime();A(["mousemove","mouseover","mouseout",ne()],r)||console.log(r,{event:r,time:n-u,target:o.dom,sequence:N(i,function(n){return A(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+Ho(n.target)+")":n.outcome})})}}),a=t(c);return c.write(),a;case It.STOP:return!0}},Xo=y({logEventCut:O,logEventStopped:O,logNoParent:O,logEventNoHandlers:O,logEventResponse:O,write:O}),qo=y([Br("menu"),Br("selectedMenu")]),Yo=y([Br("item"),Br("selectedItem")]),Ko=(y(yr(Yo().concat(qo()))),y(yr(Yo()))),Jo=jr("initSize",[Br("numColumns"),Br("numRows")]),Qo=function(n,e,t){!function(){var n=new Error;if(n.stack===undefined)return;var e=n.stack.split("\n");G(e,function(e){return 0<e.indexOf("alloy")&&!j(Uo,function(n){return-1<e.indexOf(n)})}).getOr(Lo)}();return Sr(e,e,t,Er(function(t){return Ct.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(undefined,n)})}))},Zo=function(n){return Qo(0,n,Jt(O))},ni=function(n){return Qo(0,n,Jt(T.none))},ei=function(n){return Qo(0,n,Qt())},ti=function(n){return Qo(0,n,Qt())},ri=function(n,e){return $r(n,y(e))},oi=function(n){return $r(n,v)},ii=y(Jo),ui=[Ar("channels",Cr(Ct.value,vr([ei("onReceive"),Lr("schema",Rr())])))],ci=wo({fields:ui,name:"receiving",active:zo}),ai=function(n,e,t){var r=e.aria;r.update(n,r,t.get())},fi=function(e,n,t){n.toggleClass.each(function(n){(t.get()?bt:wt)(e.element,n)})},si=function(n,e,t){mi(n,e,t,!t.get())},li=function(n,e,t){t.set(!0),fi(n,e,t),ai(n,e,t)},di=function(n,e,t){t.set(!1),fi(n,e,t),ai(n,e,t)},mi=function(n,e,t,r){(r?li:di)(n,e,t)},gi=function(n,e,t){mi(n,e,t,e.selected)},pi=/* */Object.freeze({__proto__:null,onLoad:gi,toggle:si,isOn:function(n,e,t){return t.get()},on:li,off:di,set:mi}),hi=/* */Object.freeze({__proto__:null,exhibit:function(){return lo({})},events:function(n,e){var t,r,o,i=(t=n,r=e,o=si,ao(function(n){o(n,t,r)})),u=mo(n,e,gi);return Jr(U([n.toggleOnExecute?[i]:[],[u]]))}}),vi=function(n,e,t){ft(n.element,"aria-expanded",t)},yi=[Lr("selected",!1),Nr("toggleClass"),Lr("toggleOnExecute",!0),Gr("aria",{mode:"none"},Fr("mode",{pressed:[Lr("syncWithExpanded",!1),ri("update",function(n,e,t){ft(n.element,"aria-pressed",t),e.syncWithExpanded&&vi(n,e,t)})],checked:[ri("update",function(n,e,t){ft(n.element,"aria-checked",t)})],expanded:[ri("update",vi)],selected:[ri("update",function(n,e,t){ft(n.element,"aria-selected",t)})],none:[ri("update",O)]}))],bi=wo({fields:yi,name:"toggling",active:hi,apis:pi,state:(Go=!1,{init:function(){var e=ko(Go);return{get:function(){return e.get()},set:function(n){return e.set(n)},clear:function(){return e.set(Go)},readState:function(){return e.get()}}}})}),xi=function(t,r){return ci.config({channels:or(Vo,{onReceive:function(n,e){e.command===t&&r(n,e.state)}})})},wi=function(n){return ci.config({channels:or(Bo,{onReceive:n})})},Si=function(n,e){return{key:n,value:{onReceive:e}}},Oi="tinymce-mobile",Ti=function(n){return Oi+"-"+n},ki=function(){var n=function(n,e){e.stop(),ae(n)};return[Zr(zn(),n),Zr(Qn(),n),oo(_n()),oo(Rn())]},Ei=function(n,e){e.ignore||(Eo(n.element),e.onFocus(n))},Ci=/* */Object.freeze({__proto__:null,focus:Ei,blur:function(n,e){e.ignore||Co(n.element)},isFocused:function(n){return e=n.element,t=Ne(e).dom,e.dom===t.activeElement;var e,t}}),Mi=/* */Object.freeze({__proto__:null,exhibit:function(n,e){var t=e.ignore?{}:{attributes:{tabindex:"-1"}};return lo(t)},events:function(t){return Jr([Zr(Wn(),function(n,e){Ei(n,t),e.stop()})].concat(t.stopMousedown?[Zr(Rn(),function(n,e){e.event.prevent()})]:[]))}}),Di=[Zo("onFocus"),Lr("stopMousedown",!1),Lr("ignore",!1)],_i=wo({fields:Di,name:"focusing",active:Mi,apis:Ci}),Ii=function(n){return n.style!==undefined&&I(n.style.getPropertyValue)},Fi=function(n,e,t){if(!E(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);Ii(n)&&n.style.setProperty(e,t)},Ri=function(n,e,t){var r=n.dom;Fi(r,e,t)},Vi=function(n,e){var t=n.dom;nt(e,function(n,e){Fi(t,e,n)})},Bi=function(n,e){var t=n.dom,r=window.getComputedStyle(t).getPropertyValue(e);return""!==r||Le(n)?r:Ai(t,e)},Ai=function(n,e){return Ii(n)?n.style.getPropertyValue(e):""},ji=function(n,e){var t=n.dom,r=Ai(t,e);return T.from(r).filter(function(n){return 0<n.length})},Ni=function(n,e){var t,r,o=n.dom;r=e,Ii(t=o)&&t.style.removeProperty(r),dt(n,"style").map(fn).is("")&&gt(n,"style")},Pi=function(n){return n.dom.offsetWidth};function Hi(r,o){var n=function(n){var e=o(n);if(e<=0||null===e){var t=Bi(n,r);return parseFloat(t)||0}return e},i=function(o,n){return L(n,function(n,e){var t=Bi(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)};return{set:function(n,e){if(!F(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom;Ii(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var r=i(n,t);return r<e?e-r:0}}}var zi=Hi("height",function(n){var e=n.dom;return Le(n)?e.getBoundingClientRect().height:e.offsetHeight}),Li=function(n){return zi.get(n)},Gi=function(n,e,t){return H(function(n,e){for(var t=I(e)?e:l,r=n.dom,o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=de.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}(n,t),e)},$i=function(n,e){return H(Ee(t=n).map(Ce).map(function(n){return H(n,function(n){return!ve(t,n)})}).getOr([]),e);var t},Ui=function(n,e){return pe(e,n)};function Wi(n,e,t,r,o){return n(t,r)?T.some(t):I(o)&&o(t)?T.none():e(t,r,o)}var Xi,qi,Yi=function(n,e,t){for(var r=n.dom,o=I(t)?t:l;r.parentNode;){r=r.parentNode;var i=de.fromDom(r);if(e(i))return T.some(i);if(o(i))break}return T.none()},Ki=function(n){return he(n)},Ji=function(n,e,t){return Yi(n,function(n){return me(n,e)},t)},Qi=function(n,e){return he(e,n)},Zi=function(n,e,t){return Wi(me,Ji,n,e,t)},nu=[8],eu=[9],tu=[13],ru=[27],ou=[32],iu=[37],uu=[38],cu=[39],au=[40],fu=function(n,e,t){var r=q(n.slice(0,e)),o=q(n.slice(e+1));return G(r.concat(o),t)},su=function(n,e,t){var r=q(n.slice(0,e));return G(r,t)},lu=function(n,e,t){var r=n.slice(0,e),o=n.slice(e+1);return G(o.concat(r),t)},du=function(n,e,t){var r=n.slice(e+1);return G(r,t)},mu=function(t){return function(n){var e=n.raw;return A(t,e.which)}},gu=function(n){return function(e){return X(n,function(n){return n(e)})}},pu=function(n){return!0===n.raw.shiftKey},hu=function(n){return!0===n.raw.ctrlKey},vu=m(pu),yu=function(n,e){return{matches:n,classification:e}},bu=function(n,e,t,r){var o=n+e;return r<o?t:o<t?r:o},xu=function(n,e,t){return Math.min(Math.max(n,e),t)},wu=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},Su=function(t,r,n,o){var e=Ui(t.element,"."+r.highlightClass);P(e,function(e){j(o,function(n){return n.element===e})||(wt(e,r.highlightClass),t.getSystem().getByDom(e).each(function(n){r.onDehighlight(t,n),ue(n,ie())}))})},Ou=function(n,e,t,r){Su(n,e,0,[r]),Tu(n,e,t,r)||(bt(r.element,e.highlightClass),e.onHighlight(n,r),ue(r,oe()))},Tu=function(n,e,t,r){return St(r.element,e.highlightClass)},ku=function(n,e,t,r){var o=Ui(n.element,"."+e.itemClass);return T.from(o[r]).fold(function(){return Ct.error("No element found with index "+r)},n.getSystem().getByDom)},Eu=function(e,n,t){return Qi(e.element,"."+n.itemClass).bind(function(n){return e.getSystem().getByDom(n).toOptional()})},Cu=function(e,n,t){var r=Ui(e.element,"."+n.itemClass);return(0<r.length?T.some(r[r.length-1]):T.none()).bind(function(n){return e.getSystem().getByDom(n).toOptional()})},Mu=function(t,e,n,r){var o=Ui(t.element,"."+e.itemClass);return $(o,function(n){return St(n,e.highlightClass)}).bind(function(n){var e=bu(n,r,0,o.length-1);return t.getSystem().getByDom(o[e]).toOptional()})},Du=function(e,n,t){var r=Ui(e.element,"."+n.itemClass);return wu(N(r,function(n){return e.getSystem().getByDom(n).toOptional()}))},_u=/* */Object.freeze({__proto__:null,dehighlightAll:function(n,e,t){return Su(n,e,0,[])},dehighlight:function(n,e,t,r){Tu(n,e,t,r)&&(wt(r.element,e.highlightClass),e.onDehighlight(n,r),ue(r,ie()))},highlight:Ou,highlightFirst:function(e,t,r){Eu(e,t).each(function(n){Ou(e,t,r,n)})},highlightLast:function(e,t,r){Cu(e,t).each(function(n){Ou(e,t,r,n)})},highlightAt:function(e,t,r,n){ku(e,t,r,n).fold(function(n){throw new Error(n)},function(n){Ou(e,t,r,n)})},highlightBy:function(e,t,r,n){var o=Du(e,t);G(o,n).each(function(n){Ou(e,t,r,n)})},isHighlighted:Tu,getHighlighted:function(e,n,t){return Qi(e.element,"."+n.highlightClass).bind(function(n){return e.getSystem().getByDom(n).toOptional()})},getFirst:Eu,getLast:Cu,getPrevious:function(n,e,t){return Mu(n,e,0,-1)},getNext:function(n,e,t){return Mu(n,e,0,1)},getCandidates:Du}),Iu=[Br("highlightClass"),Br("itemClass"),Zo("onHighlight"),Zo("onDehighlight")],Fu=wo({fields:Iu,name:"highlighting",apis:_u}),Ru=function(n,e,t){e.exists(function(e){return t.exists(function(n){return ve(n,e)})})||ce(n,re(),{prevFocus:e,newFocus:t})},Vu=function(){var o=function(n){return Do(n.element)};return{get:o,set:function(n,e){var t=o(n);n.getSystem().triggerFocus(e,n.element);var r=o(n);Ru(n,t,r)}}};(qi=Xi=Xi||{}).OnFocusMode="onFocus",qi.OnEnterOrSpaceMode="onEnterOrSpace",qi.OnApiMode="onApi";var Bu,Au,ju,Nu,Pu,Hu,zu,Lu,Gu,$u,Uu=function(n,e,t,r,c){var a=function(e,t,n,r,o){var i,u,c=n(e,t,r,o);return i=c,u=t.event,G(i,function(n){return n.matches(u)}).map(function(n){return n.classification}).bind(function(n){return n(e,t,r,o)})},o={schema:function(){return n.concat([Lr("focusManager",Vu()),Gr("focusInside","onFocus",Er(function(n){return A(["onFocus","onEnterOrSpace","onApi"],n)?Ct.value(n):Ct.error("Invalid value for focusInside")})),ri("handler",o),ri("state",e),ri("sendFocusIn",c)])},processKey:a,toEvents:function(i,u){var n=i.focusInside!==Xi.OnFocusMode?T.none():c(i).map(function(t){return Zr(Wn(),function(n,e){t(n,i,u),e.stop()})}),e=[Zr(jn(),function(r,o){a(r,o,t,i,u).fold(function(){var e,t,n;e=r,t=o,n=mu(ou.concat(tu))(t.event),i.focusInside===Xi.OnEnterOrSpaceMode&&n&&qr(e,t)&&c(i).each(function(n){n(e,i,u),t.stop()})},function(n){o.stop()})}),Zr(Nn(),function(n,e){a(n,e,r,i,u).each(function(n){e.stop()})})];return Jr(n.toArray().concat(e))}};return o},Wu=function(n){var e=[Nr("onEscape"),Nr("onEnter"),Lr("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),Lr("firstTabstop",0),Lr("useTabstopAt",w),Nr("visibilitySelector")].concat([n]),u=function(n,e){var t=n.visibilitySelector.bind(function(n){return Zi(e,n)}).getOr(e);return 0<Li(t)},t=function(e,t,n){var r,o,i;r=t,o=Ui(e.element,r.selector),i=H(o,function(n){return u(r,n)}),T.from(i[r.firstTabstop]).each(function(n){t.focusManager.set(e,n)})},c=function(e,n,t,r,o){return o(n,t,function(n){return u(e=r,t=n)&&e.useTabstopAt(t);var e,t}).fold(function(){return r.cyclic?T.some(!0):T.none()},function(n){return r.focusManager.set(e,n),T.some(!0)})},o=function(e,n,t,r){var o,i,u=Ui(e.element,t.selector);return o=e,(i=t).focusManager.get(o).bind(function(n){return Zi(n,i.selector)}).bind(function(n){return $(u,b(ve,n)).bind(function(n){return c(e,u,n,t,r)})})},r=y([yu(gu([pu,mu(eu)]),function(n,e,t){var r=t.cyclic?fu:su;return o(n,0,t,r)}),yu(mu(eu),function(n,e,t){var r=t.cyclic?lu:du;return o(n,0,t,r)}),yu(mu(ru),function(e,t,n){return n.onEscape.bind(function(n){return n(e,t)})}),yu(gu([vu,mu(tu)]),function(e,t,n){return n.onEnter.bind(function(n){return n(e,t)})})]),i=y([]);return Uu(e,vo.init,r,i,function(){return T.some(t)})},Xu=Wu($r("cyclic",l)),qu=Wu($r("cyclic",w)),Yu=function(n){return"input"===ye(n)&&"radio"!==lt(n,"type")||"textarea"===ye(n)},Ku=function(n,e,t){return Yu(t)&&mu(ou)(e.event)?T.none():(fe(n,t,Kn()),T.some(!0))},Ju=function(n,e){return T.some(!0)},Qu=[Lr("execute",Ku),Lr("useSpace",!1),Lr("useEnter",!0),Lr("useControlEnter",!1),Lr("useDown",!1)],Zu=function(n,e,t){return t.execute(n,e,n.element)},nc=Uu(Qu,vo.init,function(n,e,t,r){var o=t.useSpace&&!Yu(n.element)?ou:[],i=t.useEnter?tu:[],u=t.useDown?au:[],c=o.concat(i).concat(u);return[yu(mu(c),Zu)].concat(t.useControlEnter?[yu(gu([hu,mu(tu)]),Zu)]:[])},function(n,e,t,r){return t.useSpace&&!Yu(n.element)?[yu(mu(ou),Ju)]:[]},function(){return T.none()}),ec=function(){var t=ko(T.none());return yo({readState:function(){return t.get().map(function(n){return{numRows:String(n.numRows),numColumns:String(n.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,e){t.set(T.some({numRows:n,numColumns:e}))},getNumRows:function(){return t.get().map(function(n){return n.numRows})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns})}})},tc=/* */Object.freeze({__proto__:null,flatgrid:ec,init:function(n){return n.state(n)}}),rc=function(e,t){return function(n){return"rtl"===oc(n)?t:e}},oc=function(n){return"rtl"===Bi(n,"direction")?"rtl":"ltr"},ic=function(i){return function(n,e,t,r){var o=i(n.element);return fc(o,n,e,t,r)}},uc=function(n,e){var t=rc(n,e);return ic(t)},cc=function(n,e){var t=rc(e,n);return ic(t)},ac=function(o){return function(n,e,t,r){return fc(o,n,e,t,r)}},fc=function(e,t,n,r,o){return r.focusManager.get(t).bind(function(n){return e(t.element,n,r,o)}).map(function(n){return r.focusManager.set(t,n),!0})},sc=ac,lc=ac,dc=ac,mc=function(n){return!((e=n.dom).offsetWidth<=0&&e.offsetHeight<=0);var e},gc=function(n,e,t){var r,o=Ui(n,t),i=H(o,mc);return $(r=i,function(n){return ve(n,e)}).map(function(n){return{index:n,candidates:r}})},pc=function(n,e){return $(n,function(n){return ve(e,n)})},hc=function(t,n,r,e){return e(Math.floor(n/r),n%r).bind(function(n){var e=n.row*r+n.column;return 0<=e&&e<t.length?T.some(t[e]):T.none()})},vc=function(o,n,i,u,c){return hc(o,n,u,function(n,e){var t=n===i-1?o.length-n*u:u,r=bu(e,c,0,t-1);return T.some({row:n,column:r})})},yc=function(i,n,u,c,a){return hc(i,n,c,function(n,e){var t=bu(n,a,0,u-1),r=t===u-1?i.length-t*c:c,o=xu(e,0,r-1);return T.some({row:t,column:o})})},bc=[Br("selector"),Lr("execute",Ku),ni("onEscape"),Lr("captureTab",!1),ii()],xc=function(e,t,n){Qi(e.element,t.selector).each(function(n){t.focusManager.set(e,n)})},wc=function(o){return function(n,e,t,r){return gc(n,e,t.selector).bind(function(n){return o(n.candidates,n.index,r.getNumRows().getOr(t.initSize.numRows),r.getNumColumns().getOr(t.initSize.numColumns))})}},Sc=function(n,e,t){return t.captureTab?T.some(!0):T.none()},Oc=wc(function(n,e,t,r){return vc(n,e,t,r,-1)}),Tc=wc(function(n,e,t,r){return vc(n,e,t,r,1)}),kc=wc(function(n,e,t,r){return yc(n,e,t,r,-1)}),Ec=wc(function(n,e,t,r){return yc(n,e,t,r,1)}),Cc=y([yu(mu(iu),uc(Oc,Tc)),yu(mu(cu),cc(Oc,Tc)),yu(mu(uu),sc(kc)),yu(mu(au),lc(Ec)),yu(gu([pu,mu(eu)]),Sc),yu(gu([vu,mu(eu)]),Sc),yu(mu(ru),function(n,e,t){return t.onEscape(n,e)}),yu(mu(ou.concat(tu)),function(e,t,r,n){return o=e,(i=r).focusManager.get(o).bind(function(n){return Zi(n,i.selector)}).bind(function(n){return r.execute(e,t,n)});var o,i})]),Mc=y([yu(mu(ou),Ju)]),Dc=Uu(bc,ec,Cc,Mc,function(){return T.some(xc)}),_c=function(n,e,t,i){var u=function(n,e,t){var r,o=bu(e,i,0,t.length-1);return o===n?T.none():(r=t[o],"button"===ye(r)&&"disabled"===lt(r,"disabled")?u(n,o,t):T.from(t[o]))};return gc(n,t,e).bind(function(n){var e=n.index,t=n.candidates;return u(e,e,t)})},Ic=[Br("selector"),Lr("getInitial",T.none),Lr("execute",Ku),ni("onEscape"),Lr("executeOnMove",!1),Lr("allowVertical",!0)],Fc=function(e,t,r){return n=e,(o=r).focusManager.get(n).bind(function(n){return Zi(n,o.selector)}).bind(function(n){return r.execute(e,t,n)});var n,o},Rc=function(e,t,n){t.getInitial(e).orThunk(function(){return Qi(e.element,t.selector)}).each(function(n){t.focusManager.set(e,n)})},Vc=function(n,e,t){return _c(n,t.selector,e,-1)},Bc=function(n,e,t){return _c(n,t.selector,e,1)},Ac=function(o){return function(n,e,t,r){return o(n,e,t,r).bind(function(){return t.executeOnMove?Fc(n,e,t):T.some(!0)})}},jc=function(n,e,t){return t.onEscape(n,e)},Nc=y([yu(mu(ou),Ju)]),Pc=Uu(Ic,vo.init,function(n,e,t,r){var o=iu.concat(t.allowVertical?uu:[]),i=cu.concat(t.allowVertical?au:[]);return[yu(mu(o),Ac(uc(Vc,Bc))),yu(mu(i),Ac(cc(Vc,Bc))),yu(mu(tu),Fc),yu(mu(ou),Fc),yu(mu(ru),jc)]},Nc,function(){return T.some(Rc)}),Hc=function(n,e,t){return T.from(n[e]).bind(function(n){return T.from(n[t]).map(function(n){return{rowIndex:e,columnIndex:t,cell:n}})})},zc=function(n,e,t,r){var o=n[e].length,i=bu(t,r,0,o-1);return Hc(n,e,i)},Lc=function(n,e,t,r){var o=bu(t,r,0,n.length-1),i=n[o].length,u=xu(e,0,i-1);return Hc(n,o,u)},Gc=function(n,e,t,r){var o=n[e].length,i=xu(t+r,0,o-1);return Hc(n,e,i)},$c=function(n,e,t,r){var o=xu(t+r,0,n.length-1),i=n[o].length,u=xu(e,0,i-1);return Hc(n,o,u)},Uc=[jr("selectors",[Br("row"),Br("cell")]),Lr("cycles",!0),Lr("previousSelector",T.none),Lr("execute",Ku)],Wc=function(e,t,n){t.previousSelector(e).orThunk(function(){var n=t.selectors;return Qi(e.element,n.cell)}).each(function(n){t.focusManager.set(e,n)})},Xc=function(n,e){return function(t,r,i){var u=i.cycles?n:e;return Zi(r,i.selectors.row).bind(function(n){var e=Ui(n,i.selectors.cell);return pc(e,r).bind(function(r){var o=Ui(t,i.selectors.row);return pc(o,n).bind(function(n){var e,t=(e=i,N(o,function(n){return Ui(n,e.selectors.cell)}));return u(t,n,r).map(function(n){return n.cell})})})})}},qc=Xc(function(n,e,t){return zc(n,e,t,-1)},function(n,e,t){return Gc(n,e,t,-1)}),Yc=Xc(function(n,e,t){return zc(n,e,t,1)},function(n,e,t){return Gc(n,e,t,1)}),Kc=Xc(function(n,e,t){return Lc(n,t,e,-1)},function(n,e,t){return $c(n,t,e,-1)}),Jc=Xc(function(n,e,t){return Lc(n,t,e,1)},function(n,e,t){return $c(n,t,e,1)}),Qc=y([yu(mu(iu),uc(qc,Yc)),yu(mu(cu),cc(qc,Yc)),yu(mu(uu),sc(Kc)),yu(mu(au),lc(Jc)),yu(mu(ou.concat(tu)),function(e,t,r){return Do(e.element).bind(function(n){return r.execute(e,t,n)})})]),Zc=y([yu(mu(ou),Ju)]),na=Uu(Uc,vo.init,Qc,Zc,function(){return T.some(Wc)}),ea=[Br("selector"),Lr("execute",Ku),Lr("moveOnTab",!1)],ta=function(e,t,r){return r.focusManager.get(e).bind(function(n){return r.execute(e,t,n)})},ra=function(e,t,n){Qi(e.element,t.selector).each(function(n){t.focusManager.set(e,n)})},oa=function(n,e,t){return _c(n,t.selector,e,-1)},ia=function(n,e,t){return _c(n,t.selector,e,1)},ua=y([yu(mu(uu),dc(oa)),yu(mu(au),dc(ia)),yu(gu([pu,mu(eu)]),function(n,e,t,r){return t.moveOnTab?dc(oa)(n,e,t,r):T.none()}),yu(gu([vu,mu(eu)]),function(n,e,t,r){return t.moveOnTab?dc(ia)(n,e,t,r):T.none()}),yu(mu(tu),ta),yu(mu(ou),ta)]),ca=y([yu(mu(ou),Ju)]),aa=Uu(ea,vo.init,ua,ca,function(){return T.some(ra)}),fa=[ni("onSpace"),ni("onEnter"),ni("onShiftEnter"),ni("onLeft"),ni("onRight"),ni("onTab"),ni("onShiftTab"),ni("onUp"),ni("onDown"),ni("onEscape"),Lr("stopSpaceKeyup",!1),Nr("focusIn")],sa=Uu(fa,vo.init,function(n,e,t){return[yu(mu(ou),t.onSpace),yu(gu([vu,mu(tu)]),t.onEnter),yu(gu([pu,mu(tu)]),t.onShiftEnter),yu(gu([pu,mu(eu)]),t.onShiftTab),yu(gu([vu,mu(eu)]),t.onTab),yu(mu(uu),t.onUp),yu(mu(au),t.onDown),yu(mu(iu),t.onLeft),yu(mu(cu),t.onRight),yu(mu(ou),t.onSpace),yu(mu(ru),t.onEscape)]},function(n,e,t){return t.stopSpaceKeyup?[yu(mu(ou),Ju)]:[]},function(n){return n.focusIn}),la=Xu.schema(),da=qu.schema(),ma=Pc.schema(),ga=Dc.schema(),pa=na.schema(),ha=nc.schema(),va=aa.schema(),ya=sa.schema(),ba=($u=_r("Creating behaviour: "+(Bu={branchKey:"mode",branches:/* */Object.freeze({__proto__:null,acyclic:la,cyclic:da,flow:ma,flatgrid:ga,matrix:pa,execution:ha,menu:va,special:ya}),name:"keying",active:{events:function(n,e){return n.handler.toEvents(n,e)}},apis:{focusIn:function(e,t,r){t.sendFocusIn(t).fold(function(){e.getSystem().triggerFocus(e.element,e.element)},function(n){n(e,t,r)})},setGridSize:function(n,e,t,r,o){ct(t,"setGridSize")?t.setGridSize(r,o):console.error("Layout does not support setGridSize")}},state:tc}).name,So,Bu),Au=Fr($u.branchKey,$u.branches),ju=$u.name,Nu=$u.active,Pu=$u.apis,Hu=$u.extra,zu=$u.state,Gu=Hr(ju,[Pr("config",Lu=Au)]),ho(Lu,Gu,ju,Nu,Pu,Hu,zu)),xa=function(r,n){return e=r,t={},o=N(n,function(n){return e=n.name(),t="Cannot configure "+n.name()+" for "+r,Sr(e,e,Zt(),hr(function(n){return Ht("The field: "+e+" is forbidden. "+t)}));var e,t}).concat([$r("dump",v)]),Gr(e,t,yr(o));var e,t,o},wa=function(n){return n.dump},Sa=function(n,e){return x(x({},n.dump),bo(e))},Oa=xa,Ta=Sa,ka="placeholder",Ea=Ut([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Ca=function(n){return ut(n,"uiType")},Ma=function(n,e,t,r){return Ca(t)&&t.uiType===ka?(i=t,u=r,(o=n).exists(function(n){return n!==i.owner})?Ea.single(!0,y(i)):it(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+Qe(u)+"]\nNamespace: "+o.getOr("none")+"\nSpec: "+JSON.stringify(i,null,2))},function(n){return n.replace()})):Ea.single(!1,y(t));var o,i,u},Da=function(i,u,c,a){return Ma(i,0,c,a).fold(function(n,e){var t=Ca(c)?e(u,c.config,c.validated):e(u),r=it(t,"components").getOr([]),o=W(r,function(n){return Da(i,u,n,a)});return[x(x({},t),{components:o})]},function(n,e){if(Ca(c)){var t=e(u,c.config,c.validated);return c.validated.preprocess.getOr(v)(t)}return e(u)})},_a=function(e,t,n,r){var o,i,u,c=et(r,function(n,e){return r=n,o=!1,{name:y(t=e),required:function(){return r.fold(function(n,e){return n},function(n,e){return n})},used:function(){return o},replace:function(){if(o)throw new Error("Trying to use the same placeholder more than once: "+t);return o=!0,r}};var t,r,o}),a=(o=e,i=t,u=c,W(n,function(n){return Da(o,i,n,u)}));return nt(c,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))}),a},Ia=Ea.single,Fa=Ea.multiple,Ra=y(ka),Va=0,Ba=function(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Va+String(e)},Aa=Ut([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),ja=Lr("factory",{sketch:v}),Na=Lr("schema",[]),Pa=Br("name"),Ha=Sr("pname","pname",nr(function(n){return"<alloy."+Ba(n.name)+">"}),Rr()),za=$r("schema",function(){return[Nr("preprocess")]}),La=Lr("defaults",y({})),Ga=Lr("overrides",y({})),$a=yr([ja,Na,Pa,Ha,La,Ga]),Ua=yr([ja,Na,Pa,La,Ga]),Wa=yr([ja,Na,Pa,Ha,La,Ga]),Xa=yr([ja,za,Pa,Br("unit"),Ha,La,Ga]),qa=function(n){var e=function(n){return n.name};return n.fold(e,e,e,e)},Ya=function(t,r){return function(n){var e=_r("Converting part type",r,n);return t(e)}},Ka=Ya(Aa.required,$a),Ja=(Ya(Aa.external,Ua),Ya(Aa.optional,Wa)),Qa=Ya(Aa.group,Xa),Za=y("entirety"),nf=function(n,e,t,r){return qt(e.defaults(n,t,r),t,{uid:n.partUids[e.name]},e.overrides(n,t,r))},ef=function(o,n){var e={};return P(n,function(n){n.fold(T.some,T.none,T.some,T.some).each(function(t){var r=tf(o,t.pname);e[t.name]=function(n){var e=_r("Part: "+t.name+" in "+o,yr(t.schema),n);return x(x({},r),{config:n,validated:e})}})}),e},tf=function(n,e){return{uiType:Ra(),owner:n,name:e}},rf=function(n,e,t){return r=e,i={},o={},P(t,function(n){n.fold(function(r){i[r.pname]=Ia(!0,function(n,e,t){return r.factory.sketch(nf(n,r,e,t))})},function(n){var e=r.parts[n.name];o[n.name]=y(n.factory.sketch(nf(r,n,e[Za()]),e))},function(r){i[r.pname]=Ia(!1,function(n,e,t){return r.factory.sketch(nf(n,r,e,t))})},function(o){i[o.pname]=Fa(!0,function(e,n,t){var r=e[o.name];return N(r,function(n){return o.factory.sketch(qt(o.defaults(e,n,t),n,o.overrides(e,n)))})})})}),{internals:y(i),externals:y(o)};var r,i,o},of=function(n,e,t){return _a(T.some(n),e,e.components,t)},uf=function(n,e,t){var r=e.partUids[t];return n.getSystem().getByUid(r).toOptional()},cf=function(n,e,t){return uf(n,e,t).getOrDie("Could not find part: "+t)},af=function(e,n){var t=N(n,qa);return ir(N(t,function(n){return{key:n,value:e+"-"+n}}))},ff=function(e){return Sr("partUids","partUids",er(function(n){return af(n.uid,e)}),Rr())},sf=Ba("alloy-premade"),lf=function(n){return or(sf,n)},df=function(r){return n=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return r.apply(void 0,u([n.getApis(),n],e))},e=r.toString(),t=e.indexOf(")")+1,o=e.indexOf("("),i=e.substring(o+1,t-1).split(/,\s*/),n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:fo(i.slice(1))}},n;var n,e,t,o,i},mf=y("alloy-id-"),gf=y("data-alloy-id"),pf=mf(),hf=gf(),vf=function(n,e){Object.defineProperty(n.dom,hf,{value:e,writable:!0})},yf=function(n){var e=xe(n)?n.dom[hf]:null;return T.from(e)},bf=Ba,xf=function(n,e,t,r,o){var i,u,c=(u=o,(0<(i=r).length?[jr("parts",i)]:[]).concat([Br("uid"),Lr("dom",{}),Lr("components",[]),oi("originalSpec"),Lr("debug.sketcher",{})]).concat(u));return _r(n+" [SpecSchema]",vr(c.concat(e)),t)},wf=function(n,e,t,r,o){var i=Sf(o),u=W(t,function(n){return n.fold(T.none,T.some,T.none,T.none).map(function(n){return jr(n.name,n.schema.concat([oi(Za())]))}).toArray()}),c=ff(t),a=xf(n,e,i,u,[c]),f=rf(0,a,t);return r(a,of(n,a,f.internals()),i,f.externals())},Sf=function(n){return ut(n,"uid")?n:x(x({},n),{uid:bf("uid")})};var Of,Tf,kf,Ef,Cf=vr([Br("name"),Br("factory"),Br("configFields"),Lr("apis",{}),Lr("extraApis",{})]),Mf=vr([Br("name"),Br("factory"),Br("configFields"),Br("partFields"),Lr("apis",{}),Lr("extraApis",{})]),Df=function(n){var i=_r("Sketcher for "+n.name,Cf,n),e=et(i.apis,df),t=et(i.extraApis,so);return x(x({name:i.name,configFields:i.configFields,sketch:function(n){return e=i.name,t=i.configFields,r=i.factory,o=Sf(n),r(xf(e,t,o,[],[]),o);var e,t,r,o}},e),t)},_f=function(n){var e=_r("Sketcher for "+n.name,Mf,n),t=ef(e.name,e.partFields),r=et(e.apis,df),o=et(e.extraApis,so);return x(x({name:e.name,partFields:e.partFields,configFields:e.configFields,sketch:function(n){return wf(e.name,e.configFields,e.partFields,e.factory,n)},parts:t},r),o)},If=Df({name:"Button",factory:function(n){var e,t=(e=n.action,Jr(U([e.map(function(t){return ao(function(n,e){t(n),e.stop()})}).toArray(),ki()]))),r=n.dom.tag,o=function(e){return it(n.dom,"attributes").bind(function(n){return it(n,e)})};return{uid:n.uid,dom:n.dom,components:n.components,events:t,behaviours:Ta(n.buttonBehaviours,[_i.config({}),ba.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==r)return{role:o("role").getOr("button")};var n=o("type").getOr("button"),e=o("role").map(function(n){return{role:n}}).getOr({});return x({type:n},e)}()},eventOrder:n.eventOrder}},configFields:[Lr("uid",undefined),Br("dom"),Lr("components",[]),Oa("buttonBehaviours",[_i,ba]),Nr("action"),Nr("role"),Lr("eventOrder",{})]}),Ff=wo({fields:[],name:"unselecting",active:/* */Object.freeze({__proto__:null,events:function(){return Jr([Qr(Gn(),w)])},exhibit:function(){return lo({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Rf=function(n){var e,t,r,o=de.fromHtml(n),i=Ce(o),u=(t=(e=o).dom.attributes!==undefined?e.dom.attributes:[],L(t,function(n,e){var t;return"class"===e.name?n:x(x({},n),((t={})[e.name]=e.value,t))},{})),c=(r=o,Array.prototype.slice.call(r.dom.classList,0)),a=0===i.length?{}:{innerHtml:jo(o)};return x({tag:ye(o),classes:c,attributes:u},a)},Vf=function(n){var e=cn(n,{prefix:Oi});return Rf(e)},Bf=function(n){return{dom:Vf(n)}},Af=function(n){return bo([bi.config({toggleClass:Ti("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),xi(n,function(n,e){(e?bi.on:bi.off)(n)})])},jf=function(n,e,t,r){var o=Af(t);return Pf(e,r,o,n)},Nf=function(n,e){var t=e.ui.registry.getAll().icons;return T.from(t[n]).fold(function(){return Vf('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item ${prefix}-icon-'+n+' ${prefix}-icon"></span>')},function(n){return Vf('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item">'+n+"</span>")})},Pf=function(n,e,t,r){return If.sketch({dom:Nf(n,r),action:e,buttonBehaviours:qt(bo([Ff.config({})]),t)})},Hf=Ja({schema:[Br("dom")],name:"label"}),zf=function(e){return Ja({name:e+"-edge",overrides:function(n){return n.model.manager.edgeActions[e].fold(function(){return{}},function(r){return{events:Jr([no(_n(),function(n,e,t){return r(n,t)},[n]),no(Rn(),function(n,e,t){return r(n,t)},[n]),no(Vn(),function(n,e,t){t.mouseIsDown.get()&&r(n,t)},[n])])}})}})},Lf=zf("top-left"),Gf=zf("top"),$f=zf("top-right"),Uf=zf("right"),Wf=zf("bottom-right"),Xf=zf("bottom"),qf=zf("bottom-left"),Yf=[Hf,zf("left"),Uf,Gf,Xf,Lf,$f,qf,Wf,Ka({name:"thumb",defaults:y({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:Jr([ro(_n(),n,"spectrum"),ro(In(),n,"spectrum"),ro(Fn(),n,"spectrum"),ro(Rn(),n,"spectrum"),ro(Vn(),n,"spectrum"),ro(Bn(),n,"spectrum")])}}}),Ka({schema:[$r("mouseIsDown",function(){return ko(!1)})],name:"spectrum",overrides:function(t){var r=t.model.manager,o=function(e,n){return r.getValueFromEvent(n).map(function(n){return r.setValueFrom(e,t,n)})};return{behaviours:bo([ba.config({mode:"special",onLeft:function(n){return r.onLeft(n,t)},onRight:function(n){return r.onRight(n,t)},onUp:function(n){return r.onUp(n,t)},onDown:function(n){return r.onDown(n,t)}}),_i.config({})]),events:Jr([Zr(_n(),o),Zr(In(),o),Zr(Rn(),o),Zr(Vn(),function(n,e){t.mouseIsDown.get()&&o(n,e)})])}}})],Kf=function(n,e,t){e.store.manager.onLoad(n,e,t)},Jf=function(n,e,t){e.store.manager.onUnload(n,e,t)},Qf=/* */Object.freeze({__proto__:null,onLoad:Kf,onUnload:Jf,setValue:function(n,e,t,r){e.store.manager.setValue(n,e,t,r)},getValue:function(n,e,t){return e.store.manager.getValue(n,e,t)},getState:function(n,e,t){return t}}),Zf=/* */Object.freeze({__proto__:null,events:function(t,r){var n=t.resetOnDom?[io(function(n,e){Kf(n,t,r)}),uo(function(n,e){Jf(n,t,r)})]:[mo(t,r,Kf)];return Jr(n)}}),ns=function(){var n=ko(null);return yo({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})},es=function(){var i=ko({}),u=ko({});return yo({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return it(i.get(),n).orThunk(function(){return it(u.get(),n)})},update:function(n){var e=i.get(),t=u.get(),r={},o={};P(n,function(e){r[e.value]=e,it(e,"meta").each(function(n){it(n,"text").each(function(n){o[n]=e})})}),i.set(x(x({},e),r)),u.set(x(x({},t),o))},clear:function(){i.set({}),u.set({})}})},ts=/* */Object.freeze({__proto__:null,memory:ns,dataset:es,manual:function(){return yo({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),rs=function(n,e,t,r){var o=e.store;t.update([r]),o.setValue(n,r),e.onSetValue(n,r)},os=[Nr("initialValue"),Br("getFallbackEntry"),Br("getDataKey"),Br("setValue"),ri("manager",{setValue:rs,getValue:function(n,e,t){var r=e.store,o=r.getDataKey(n);return t.lookup(o).fold(function(){return r.getFallbackEntry(o)},function(n){return n})},onLoad:function(e,t,r){t.store.initialValue.each(function(n){rs(e,t,r,n)})},onUnload:function(n,e,t){t.clear()},state:es})],is=[Br("getValue"),Lr("setValue",O),Nr("initialValue"),ri("manager",{setValue:function(n,e,t,r){e.store.setValue(n,r),e.onSetValue(n,r)},getValue:function(n,e,t){return e.store.getValue(n)},onLoad:function(e,t,n){t.store.initialValue.each(function(n){t.store.setValue(e,n)})},onUnload:O,state:vo.init})],us=[Nr("initialValue"),ri("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store.initialValue.each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:ns})],cs=[Gr("store",{mode:"memory"},Fr("mode",{memory:us,manual:is,dataset:os})),Zo("onSetValue"),Lr("resetOnDom",!1)],as=wo({fields:cs,name:"representing",active:Zf,apis:Qf,extra:{setValueFrom:function(n,e){var t=as.getValue(e);as.setValue(n,t)}},state:ts}),fs=Hi("width",function(n){return n.dom.offsetWidth}),ss=function(n,e){return fs.set(n,e)},ls=function(n){return fs.get(n)},ds=function(t,r){return{left:t,top:r,translate:function(n,e){return ds(t+n,r+e)}}},ms=ds,gs=y("slider.change.value"),ps=function(n){var e=n.event.raw;return-1===e.type.indexOf("touch")?e.clientX!==undefined?T.some(e).map(function(n){return ms(n.clientX,n.clientY)}):T.none():e.touches!==undefined&&1===e.touches.length?T.some(e.touches[0]).map(function(n){return ms(n.clientX,n.clientY)}):T.none()},hs=function(n){return n.model.minX},vs=function(n){return n.model.minY},ys=function(n){return n.model.minX-1},bs=function(n){return n.model.minY-1},xs=function(n){return n.model.maxX},ws=function(n){return n.model.maxY},Ss=function(n){return n.model.maxX+1},Os=function(n){return n.model.maxY+1},Ts=function(n,e,t){return e(n)-t(n)},ks=function(n){return Ts(n,xs,hs)},Es=function(n){return Ts(n,ws,vs)},Cs=function(n){return ks(n)/2},Ms=function(n){return Es(n)/2},Ds=function(n){return n.stepSize},_s=function(n){return n.snapToGrid},Is=function(n){return n.snapStart},Fs=function(n){return n.rounded},Rs=function(n,e){return n[e+"-edge"]!==undefined},Vs=function(n){return Rs(n,"left")},Bs=function(n){return Rs(n,"right")},As=function(n){return Rs(n,"top")},js=function(n){return Rs(n,"bottom")},Ns=function(n){return n.model.value.get()},Ps=function(n){return{x:n}},Hs=function(n){return{y:n}},zs=function(n,e){return{x:n,y:e}},Ls=function(n,e){ce(n,gs(),{value:e})},Gs=function(n,e,t,r){return n<e?n:t<n?t:n===e?e-1:Math.max(e,n-r)},$s=function(n,e,t,r){return t<n?n:n<e?e:n===t?t+1:Math.min(t,n+r)},Us=function(n,e,t){return Math.max(e,Math.min(t,n))},Ws=function(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.step,u=n.snap,c=n.snapStart,a=n.rounded,f=n.hasMinEdge,s=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=f?e-1:e,p=s?t+1:t;if(o<l)return g;if(d<o)return p;var h,v,y,b,x,w,S,O=(x=o,w=l,S=d,Math.min(S,Math.max(x,w))-w),T=Us(O/m*r+e,g,p);return u&&e<=T&&T<=t?(h=T,v=e,y=t,b=i,c.fold(function(){var n=h-v,e=Math.round(n/b)*b;return Us(v+e,v-1,y+1)},function(n){var e=(h-n)%b,t=Math.round(e/b),r=Math.floor((h-n)/b),o=Math.floor((y-n)/b),i=n+Math.min(o,r+t)*b;return Math.max(n,i)})):a?Math.round(T):T},Xs=function(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,c=n.maxBound,a=n.maxOffset,f=n.centerMinEdge,s=n.centerMaxEdge;return o<e?i?0:f:t<o?u?c:s:(o-e)/r*a},qs="left",Ys=function(n){return n.element.dom.getBoundingClientRect()},Ks=function(n,e){return n[e]},Js=function(n){var e=Ys(n);return Ks(e,qs)},Qs=function(n){var e=Ys(n);return Ks(e,"right")},Zs=function(n){var e=Ys(n);return Ks(e,"top")},nl=function(n){var e=Ys(n);return Ks(e,"bottom")},el=function(n){var e=Ys(n);return Ks(e,"width")},tl=function(n){var e=Ys(n);return Ks(e,"height")},rl=function(n,e,t){return(n+e)/2-t},ol=function(n,e){var t=Ys(n),r=Ys(e),o=Ks(t,qs),i=Ks(t,"right"),u=Ks(r,qs);return rl(o,i,u)},il=function(n,e){var t=Ys(n),r=Ys(e),o=Ks(t,"top"),i=Ks(t,"bottom"),u=Ks(r,"top");return rl(o,i,u)},ul=function(n,e){ce(n,gs(),{value:e})},cl=function(n){return{x:n}},al=function(n,e,t){var r={min:hs(e),max:xs(e),range:ks(e),value:t,step:Ds(e),snap:_s(e),snapStart:Is(e),rounded:Fs(e),hasMinEdge:Vs(e),hasMaxEdge:Bs(e),minBound:Js(n),maxBound:Qs(n),screenRange:el(n)};return Ws(r)},fl=function(i){return function(n,e){return t=n,o=(0<i?$s:Gs)(Ns(r=e).x,hs(r),xs(r),Ds(r)),ul(t,cl(o)),T.some(o).map(function(){return!0});var t,r,o}},sl=function(n,e,t,r,o,i){var u,c,a,f,s,l,d,m,g,p=(c=i,a=t,f=r,s=o,l=el(u=e),d=f.bind(function(n){return T.some(ol(n,u))}).getOr(0),m=s.bind(function(n){return T.some(ol(n,u))}).getOr(l),g={min:hs(c),max:xs(c),range:ks(c),value:a,hasMinEdge:Vs(c),hasMaxEdge:Bs(c),minBound:Js(u),minOffset:0,maxBound:Qs(u),maxOffset:l,centerMinEdge:d,centerMaxEdge:m},Xs(g));return Js(e)-Js(n)+p},ll=fl(-1),dl=fl(1),ml=T.none,gl=T.none,pl={"top-left":T.none(),top:T.none(),"top-right":T.none(),right:T.some(function(n,e){Ls(n,Ps(Ss(e)))}),"bottom-right":T.none(),bottom:T.none(),"bottom-left":T.none(),left:T.some(function(n,e){Ls(n,Ps(ys(e)))})},hl=/* */Object.freeze({__proto__:null,setValueFrom:function(n,e,t){var r=al(n,e,t),o=cl(r);return ul(n,o),r},setToMin:function(n,e){var t=hs(e);ul(n,cl(t))},setToMax:function(n,e){var t=xs(e);ul(n,cl(t))},findValueOfOffset:al,getValueFromEvent:function(n){return ps(n).map(function(n){return n.left})},findPositionOfValue:sl,setPositionFromValue:function(n,e,t,r){var o=Ns(t),i=sl(n,r.getSpectrum(n),o.x,r.getLeftEdge(n),r.getRightEdge(n),t),u=ls(e.element)/2;Ri(e.element,"left",i-u+"px")},onLeft:ll,onRight:dl,onUp:ml,onDown:gl,edgeActions:pl}),vl=function(n,e){ce(n,gs(),{value:e})},yl=function(n){return{y:n}},bl=function(n,e,t){var r={min:vs(e),max:ws(e),range:Es(e),value:t,step:Ds(e),snap:_s(e),snapStart:Is(e),rounded:Fs(e),hasMinEdge:As(e),hasMaxEdge:js(e),minBound:Zs(n),maxBound:nl(n),screenRange:tl(n)};return Ws(r)},xl=function(i){return function(n,e){return t=n,o=(0<i?$s:Gs)(Ns(r=e).y,vs(r),ws(r),Ds(r)),vl(t,yl(o)),T.some(o).map(function(){return!0});var t,r,o}},wl=function(n,e,t,r,o,i){var u,c,a,f,s,l,d,m,g,p=(c=i,a=t,f=r,s=o,l=tl(u=e),d=f.bind(function(n){return T.some(il(n,u))}).getOr(0),m=s.bind(function(n){return T.some(il(n,u))}).getOr(l),g={min:vs(c),max:ws(c),range:Es(c),value:a,hasMinEdge:As(c),hasMaxEdge:js(c),minBound:Zs(u),minOffset:0,maxBound:nl(u),maxOffset:l,centerMinEdge:d,centerMaxEdge:m},Xs(g));return Zs(e)-Zs(n)+p},Sl=T.none,Ol=T.none,Tl=xl(-1),kl=xl(1),El={"top-left":T.none(),top:T.some(function(n,e){Ls(n,Hs(bs(e)))}),"top-right":T.none(),right:T.none(),"bottom-right":T.none(),bottom:T.some(function(n,e){Ls(n,Hs(Os(e)))}),"bottom-left":T.none(),left:T.none()},Cl=/* */Object.freeze({__proto__:null,setValueFrom:function(n,e,t){var r=bl(n,e,t),o=yl(r);return vl(n,o),r},setToMin:function(n,e){var t=vs(e);vl(n,yl(t))},setToMax:function(n,e){var t=ws(e);vl(n,yl(t))},findValueOfOffset:bl,getValueFromEvent:function(n){return ps(n).map(function(n){return n.top})},findPositionOfValue:wl,setPositionFromValue:function(n,e,t,r){var o=Ns(t),i=wl(n,r.getSpectrum(n),o.y,r.getTopEdge(n),r.getBottomEdge(n),t),u=Li(e.element)/2;Ri(e.element,"top",i-u+"px")},onLeft:Sl,onRight:Ol,onUp:Tl,onDown:kl,edgeActions:El}),Ml=function(n,e){ce(n,gs(),{value:e})},Dl=function(n,e){return{x:n,y:e}},_l=function(a,f){return function(n,e){return r=n,o=e,i=0<a?$s:Gs,u=(t=f)?Ns(o).x:i(Ns(o).x,hs(o),xs(o),Ds(o)),c=t?i(Ns(o).y,vs(o),ws(o),Ds(o)):Ns(o).y,Ml(r,Dl(u,c)),T.some(u).map(function(){return!0});var t,r,o,i,u,c}},Il=ps,Fl=_l(-1,!1),Rl=_l(1,!1),Vl=_l(-1,!0),Bl=_l(1,!0),Al={"top-left":T.some(function(n,e){Ls(n,zs(ys(e),bs(e)))}),top:T.some(function(n,e){Ls(n,zs(Cs(e),bs(e)))}),"top-right":T.some(function(n,e){Ls(n,zs(Ss(e),bs(e)))}),right:T.some(function(n,e){Ls(n,zs(Ss(e),Ms(e)))}),"bottom-right":T.some(function(n,e){Ls(n,zs(Ss(e),Os(e)))}),bottom:T.some(function(n,e){Ls(n,zs(Cs(e),Os(e)))}),"bottom-left":T.some(function(n,e){Ls(n,zs(ys(e),Os(e)))}),left:T.some(function(n,e){Ls(n,zs(ys(e),Ms(e)))})},jl=/* */Object.freeze({__proto__:null,setValueFrom:function(n,e,t){var r=al(n,e,t.left),o=bl(n,e,t.top),i=Dl(r,o);return Ml(n,i),i},setToMin:function(n,e){var t=hs(e),r=vs(e);Ml(n,Dl(t,r))},setToMax:function(n,e){var t=xs(e),r=ws(e);Ml(n,Dl(t,r))},getValueFromEvent:Il,setPositionFromValue:function(n,e,t,r){var o=Ns(t),i=sl(n,r.getSpectrum(n),o.x,r.getLeftEdge(n),r.getRightEdge(n),t),u=wl(n,r.getSpectrum(n),o.y,r.getTopEdge(n),r.getBottomEdge(n),t),c=ls(e.element)/2,a=Li(e.element)/2;Ri(e.element,"left",i-c+"px"),Ri(e.element,"top",u-a+"px")},onLeft:Fl,onRight:Rl,onUp:Vl,onDown:Bl,edgeActions:Al}),Nl=[Lr("stepSize",1),Lr("onChange",O),Lr("onChoose",O),Lr("onInit",O),Lr("onDragStart",O),Lr("onDragEnd",O),Lr("snapToGrid",!1),Lr("rounded",!0),Nr("snapStart"),Ar("model",Fr("mode",{x:[Lr("minX",0),Lr("maxX",100),$r("value",function(n){return ko(n.mode.minX)}),Br("getInitialValue"),ri("manager",hl)],y:[Lr("minY",0),Lr("maxY",100),$r("value",function(n){return ko(n.mode.minY)}),Br("getInitialValue"),ri("manager",Cl)],xy:[Lr("minX",0),Lr("maxX",100),Lr("minY",0),Lr("maxY",100),$r("value",function(n){return ko({x:n.mode.minX,y:n.mode.minY})}),Br("getInitialValue"),ri("manager",jl)]})),xa("sliderBehaviours",[ba,as]),$r("mouseIsDown",function(){return ko(!1)})],Pl=y("mouse.released"),Hl=_f({name:"Slider",configFields:Nl,partFields:Yf,factory:function(i,n,e,t){var r,u=function(n){return cf(n,i,"thumb")},c=function(n){return cf(n,i,"spectrum")},o=function(n){return uf(n,i,"left-edge")},a=function(n){return uf(n,i,"right-edge")},f=function(n){return uf(n,i,"top-edge")},s=function(n){return uf(n,i,"bottom-edge")},l=i.model,d=l.manager,m=function(n,e){d.setPositionFromValue(n,e,i,{getLeftEdge:o,getRightEdge:a,getTopEdge:f,getBottomEdge:s,getSpectrum:c})},g=function(n,e){l.value.set(e);var t=u(n);return m(n,t),i.onChange(n,t,e),T.some(!0)},p=function(t){var n=i.mouseIsDown.get();i.mouseIsDown.set(!1),n&&uf(t,i,"thumb").each(function(n){var e=l.value.get();i.onChoose(t,n,e)})},h=function(n,e){e.stop(),i.mouseIsDown.set(!0),i.onDragStart(n,u(n))},v=function(n,e){e.stop(),i.onDragEnd(n,u(n)),p(n)};return{uid:i.uid,dom:i.dom,components:n,behaviours:Sa(i.sliderBehaviours,[ba.config({mode:"special",focusIn:function(n){return uf(n,i,"spectrum").map(ba.focusIn).map(w)}}),as.config({store:{mode:"manual",getValue:function(n){return l.value.get()}}}),ci.config({channels:((r={})[Pl()]={onReceive:p},r)})]),events:Jr([Zr(gs(),function(n,e){g(n,e.event.value)}),io(function(n,e){var t=l.getInitialValue();l.value.set(t);var r=u(n);m(n,r);var o=c(n);i.onInit(n,r,o,l.value.get())}),Zr(_n(),h),Zr(Fn(),v),Zr(Rn(),h),Zr(Bn(),v)]),apis:{resetToMin:function(n){d.setToMin(n,i)},resetToMax:function(n){d.setToMax(n,i)},changeValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),zl=function(e,t,r,n){return Pf(t,function(){var n=r();e.setContextToolbar([{label:t+" group",items:n}])},{},n)},Ll=function(n){return[(o=n,i=function(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"},Hl.sketch({dom:Vf('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[Hl.parts["left-edge"](Bf('<div class="${prefix}-hue-slider-black"></div>')),Hl.parts.spectrum({dom:Vf('<div class="${prefix}-slider-gradient-container"></div>'),components:[Bf('<div class="${prefix}-slider-gradient"></div>')],behaviours:bo([bi.config({toggleClass:Ti("thumb-active")})])}),Hl.parts["right-edge"](Bf('<div class="${prefix}-hue-slider-white"></div>')),Hl.parts.thumb({dom:Vf('<div class="${prefix}-slider-thumb"></div>'),behaviours:bo([bi.config({toggleClass:Ti("thumb-active")})])})],onChange:function(n,e,t){var r=i(t.x());Ri(e.element,"background-color",r),o.onChange(n,e,r)},onDragStart:function(n,e){bi.on(e)},onDragEnd:function(n,e){bi.off(e)},onInit:function(n,e,t,r){var o=i(r.x());Ri(e.element,"background-color",o)},stepSize:10,model:{mode:"x",minX:0,maxX:360,getInitialValue:function(){return{x:o.getInitialValue()}}},sliderBehaviours:bo([wi(Hl.refresh)])}))];var o,i},Gl=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],$l=function(e,n){return(xe(n)?T.some(n):Ee(n).filter(xe)).map(function(n){return Wi(function(n,e){return e(n)},Yi,n,function(n){return ji(n,"font-size").isSome()},e).bind(function(n){return ji(n,"font-size")}).getOrThunk(function(){return Bi(n,"font-size")})}).getOr("")},Ul=function(n){var e=n.selection.getStart(),t=de.fromDom(e),r=de.fromDom(n.getBody()),o=$l(function(n){return ve(r,n)},t);return G(Gl,function(n){return o===n}).getOr("medium")},Wl=function(n){var e,t=Ul(n);return e=t,$(Gl,function(n){return n===e}).getOr(2)},Xl=function(r,n){var e;e=n,T.from(Gl[e]).each(function(n){var e,t;t=n,Ul(e=r)!==t&&e.execCommand("fontSize",!1,t)})},ql=y(Gl),Yl=vr([Br("getInitialValue"),Br("onChange"),Br("category"),Br("sizes")]),Kl=function(n){var i=_r("SizeSlider",Yl,n);return Hl.sketch({dom:{tag:"div",classes:[Ti("slider-"+i.category+"-size-container"),Ti("slider"),Ti("slider-size-container")]},onChange:function(n,e,t){var r,o=t.x();0<=(r=o)&&r<i.sizes.length&&i.onChange(o)},onDragStart:function(n,e){bi.on(e)},onDragEnd:function(n,e){bi.off(e)},model:{mode:"x",minX:0,maxX:i.sizes.length-1,getInitialValue:function(){return{x:i.getInitialValue()}}},stepSize:1,snapToGrid:!0,sliderBehaviours:bo([wi(Hl.refresh)]),components:[Hl.parts.spectrum({dom:Vf('<div class="${prefix}-slider-size-container"></div>'),components:[Bf('<div class="${prefix}-slider-size-line"></div>')]}),Hl.parts.thumb({dom:Vf('<div class="${prefix}-slider-thumb"></div>'),behaviours:bo([bi.config({toggleClass:Ti("thumb-active")})])})]})},Jl=ql(),Ql=function(n){return[Bf('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),Kl({onChange:(e=n).onChange,sizes:Jl,category:"font",getInitialValue:e.getInitialValue}),Bf('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')];var e},Zl=function(n){var e=n.uid!==undefined&&ct(n,"uid")?n.uid:bf("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).toOptional()},asSpec:function(){return x(x({},n),{uid:e})}}},nd=window.Promise?window.Promise:(Of=function(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],cd(n,ed(rd,this),ed(od,this))},Tf=window,kf=Of.immediateFn||"function"==typeof Tf.setImmediate&&Tf.setImmediate||function(n){setTimeout(n,1)},Ef=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},Of.prototype["catch"]=function(n){return this.then(null,n)},Of.prototype.then=function(t,r){var o=this;return new Of(function(n,e){td.call(o,new ud(t,r,n,e))})},Of.all=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var a=Array.prototype.slice.call(1===n.length&&Ef(n[0])?n[0]:n);return new Of(function(o,i){if(0===a.length)return o([]);for(var u=a.length,n=0;n<a.length;n++)!function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}(n,a[n])})},Of.resolve=function(e){return e&&"object"==typeof e&&e.constructor===Of?e:new Of(function(n){n(e)})},Of.reject=function(t){return new Of(function(n,e){e(t)})},Of.race=function(o){return new Of(function(n,e){for(var t=0,r=o;t<r.length;t++)r[t].then(n,e)})},Of);function ed(n,e){return function(){return n.apply(e,arguments)}}function td(r){var o=this;null!==this._state?kf(function(){var n,e=o._state?r.onFulfilled:r.onRejected;if(null!==e){try{n=e(o._value)}catch(t){return void r.reject(t)}r.resolve(n)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function rd(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void cd(ed(e,n),ed(rd,this),ed(od,this))}this._state=!0,this._value=n,id.call(this)}catch(t){od.call(this,t)}}function od(n){this._state=!1,this._value=n,id.call(this)}function id(){for(var n=0,e=this._deferreds;n<e.length;n++){var t=e[n];td.call(this,t)}this._deferreds=[]}function ud(n,e,t,r){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=r}function cd(n,e,t){var r=!1;try{n(function(n){r||(r=!0,e(n))},function(n){r||(r=!0,t(n))})}catch(o){if(r)return;r=!0,t(o)}}function ad(n){return t=n,new nd(function(n){var e=new FileReader;e.onloadend=function(){n(e.result)},e.readAsDataURL(t)}).then(function(n){return n.split(",")[1]});var t}var fd=ad,sd=function(u){var e=Zl({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:Jr([oo(zn()),Zr(Hn(),function(n,e){var t,r;t=e.event.raw,r=t.target.files||t.dataTransfer.files,T.from(r[0]).each(function(n){var o,i;o=u,fd(i=n).then(function(r){o.undoManager.transact(function(){var n=o.editorUpload.blobCache,e=n.create(Ba("mceu"),i,r);n.add(e);var t=o.dom.createHTML("img",{src:e.blobUri()});o.insertContent(t)})})})})])});return If.sketch({dom:Nf("image",u),components:[e.asSpec()],action:function(n){e.get(n).element.dom.click()}})},ld=function(n){return n.dom.textContent},dd=function(n){return 0<n.length},md=function(n){return n===undefined||null===n?"":n},gd=function(n){return vd(n).fold(function(){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:T.none()}},function(n){return t=ld(e=n),r=lt(e,"href"),o=lt(e,"title"),i=lt(e,"target"),{url:md(r),text:t!==r?md(t):"",title:md(o),target:md(i),link:T.some(e)};var e,t,r,o,i})},pd=function(e,t,n){return n.text.toOptional().filter(dd).fold(function(){return lt(n=e,"href")===ld(n)?T.some(t):T.none();var n},T.some)},hd=function(o,i){i.url.toOptional().filter(dd).fold(function(){var e;e=o,i.link.bind(v).each(function(n){e.execCommand("unlink")})},function(e){var n,t,r=(n=i,(t={}).href=e,n.title.toOptional().filter(dd).each(function(n){t.title=n}),n.target.toOptional().filter(dd).each(function(n){t.target=n}),t);i.link.bind(v).fold(function(){var n=i.text.toOptional().filter(dd).getOr(e);o.insertContent(o.dom.createHTML("a",r,o.dom.encode(n)))},function(t){var n=pd(t,e,i);st(t,r),n.each(function(n){var e;e=n,t.dom.textContent=e})})})},vd=function(n){var e=de.fromDom(n.selection.getStart());return Zi(e,"a")},yd=Mn(),bd=function(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)},xd=function(n,e){var t,r;return{key:n,value:{config:{},me:(t=n,r=Jr(e),wo({fields:[Br("enabled")],name:t,active:{events:y(r)}})),configAsRaw:y({}),initialConfig:{},state:vo}}},wd=/* */Object.freeze({__proto__:null,getCurrent:function(n,e,t){return e.find(n)}}),Sd=[Br("find")],Od=wo({fields:Sd,name:"composing",apis:wd}),Td=Df({name:"Container",factory:function(n){var e=n.dom,t=e.attributes,r=c(e,["attributes"]);return{uid:n.uid,dom:x({tag:"div",attributes:x({role:"presentation"},t)},r),components:n.components,behaviours:wa(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[Lr("components",[]),xa("containerBehaviours",[]),Lr("events",{}),Lr("domModification",{}),Lr("eventOrder",{})]}),kd=Df({name:"DataField",factory:function(t){return{uid:t.uid,dom:t.dom,behaviours:Ta(t.dataBehaviours,[as.config({store:{mode:"memory",initialValue:t.getInitialValue()}}),Od.config({find:T.some})]),events:Jr([io(function(n,e){as.setValue(n,t.getInitialValue())})])}},configFields:[Br("uid"),Br("dom"),Br("getInitialValue"),Oa("dataBehaviours",[as,Od])]}),Ed=function(n){return n.dom.value},Cd=function(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom.value=e},Md=y([Nr("data"),Lr("inputAttributes",{}),Lr("inputStyles",{}),Lr("tag","input"),Lr("inputClasses",[]),Zo("onSetValue"),Lr("styles",{}),Lr("eventOrder",{}),xa("inputBehaviours",[as,_i]),Lr("selectOnFocus",!0)]),Dd=function(n){return x(x({},(e=n,bo([_i.config({onFocus:e.selectOnFocus?function(n){var e=n.element,t=Ed(e);e.dom.setSelectionRange(0,t.length)}:O})]))),Sa(n.inputBehaviours,[as.config({store:x(x({mode:"manual"},n.data.map(function(n){return{initialValue:n}}).getOr({})),{getValue:function(n){return Ed(n.element)},setValue:function(n,e){Ed(n.element)!==e&&Cd(n.element,e)}}),onSetValue:n.onSetValue})]));var e},_d=Df({name:"Input",configFields:Md(),factory:function(n,e){return{uid:n.uid,dom:{tag:(t=n).tag,attributes:x({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses},components:[],behaviours:Dd(n),eventOrder:n.eventOrder};var t}}),Id=/* */Object.freeze({__proto__:null,exhibit:function(n,e){return lo({attributes:ir([{key:e.tabAttr,value:"true"}])})}}),Fd=[Lr("tabAttr","data-alloy-tabstop")],Rd=wo({fields:Fd,name:"tabstopping",active:Id}),Vd=tinymce.util.Tools.resolve("tinymce.util.I18n"),Bd=function(n,e){var t=Zl(_d.sketch({inputAttributes:{placeholder:Vd.translate(e)},onSetValue:function(n,e){ue(n,Pn())},inputBehaviours:bo([Od.config({find:T.some}),Rd.config({}),ba.config({mode:"execution"})]),selectOnFocus:!1})),r=Zl(If.sketch({dom:Vf('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);as.setValue(e,"")}}));return{name:n,spec:Td.sketch({dom:Vf('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),r.asSpec()],containerBehaviours:bo([bi.config({toggleClass:Ti("input-container-empty")}),Od.config({find:function(n){return T.some(t.get(n))}}),xd("input-clearing",[Zr(Pn(),function(n){var e=t.get(n);(0<as.getValue(e).length?bi.off:bi.on)(n)})])])})}},Ad=["input","button","textarea","select"],jd=function(n,e,t){(e.disabled()?Gd:$d)(n,e)},Nd=function(n,e){return!0===e.useNative&&A(Ad,ye(n.element))},Pd=function(n){ft(n.element,"disabled","disabled")},Hd=function(n){gt(n.element,"disabled")},zd=function(n){ft(n.element,"aria-disabled","true")},Ld=function(n){ft(n.element,"aria-disabled","false")},Gd=function(e,n,t){n.disableClass.each(function(n){bt(e.element,n)}),(Nd(e,n)?Pd:zd)(e),n.onDisabled(e)},$d=function(e,n,t){n.disableClass.each(function(n){wt(e.element,n)}),(Nd(e,n)?Hd:Ld)(e),n.onEnabled(e)},Ud=function(n,e){return Nd(n,e)?mt(n.element,"disabled"):"true"===lt(n.element,"aria-disabled")},Wd=/* */Object.freeze({__proto__:null,enable:$d,disable:Gd,isDisabled:Ud,onLoad:jd,set:function(n,e,t,r){(r?Gd:$d)(n,e)}}),Xd=/* */Object.freeze({__proto__:null,exhibit:function(n,e){return lo({classes:e.disabled()?e.disableClass.toArray():[]})},events:function(t,n){return Jr([Qr(Kn(),function(n,e){return Ud(n,t)}),mo(t,n,jd)])}}),qd=[Gr("disabled",l,Vr),Lr("useNative",!0),Nr("disableClass"),Zo("onDisabled"),Zo("onEnabled")],Yd=wo({fields:qd,name:"disabling",active:Xd,apis:Wd}),Kd=[xa("formBehaviours",[as])],Jd=function(n){return"<alloy.field."+n+">"},Qd=function(o,n){return{uid:o.uid,dom:o.dom,components:n,behaviours:Sa(o.formBehaviours,[as.config({store:{mode:"manual",getValue:function(n){var e,t,r=(e=o,t=n.getSystem(),et(e.partUids,function(n,e){return y(t.getByUid(n))}));return et(r,function(n,o){return n().bind(function(n){var e,t,r=Od.getCurrent(n);return e=r,t=new Error("Cannot find a current component to extract the value from for form part '"+o+"': "+Ho(n.element)),e.fold(function(){return Ct.error(t)},Ct.value)}).map(as.getValue)})},setValue:function(t,n){nt(n,function(e,n){uf(t,o,n).each(function(n){Od.getCurrent(n).each(function(n){as.setValue(n,e)})})})}}})]),apis:{getField:function(n,e){return uf(n,o,e).bind(Od.getCurrent)}}}},Zd=(df(function(n,e,t){return n.getField(e,t)}),function(n){var i,e=(i=[],{field:function(n,e){return i.push(n),t="form",r=Jd(n),o=e,{uiType:Ra(),owner:t,name:r,config:o,validated:{}};var t,r,o},record:function(){return i}}),t=n(e),r=e.record(),o=N(r,function(n){return Ka({name:n,pname:Jd(n)})});return wf("form",Kd,o,Qd,t)}),nm=function(){var e=ko(T.none()),t=function(){return e.get().each(function(n){return n.destroy()})};return{clear:function(){t(),e.set(T.none())},isSet:function(){return e.get().isSome()},set:function(n){t(),e.set(T.some(n))},run:function(n){return e.get().each(n)}}},em=function(){var e=ko(T.none());return{clear:function(){return e.set(T.none())},set:function(n){return e.set(T.some(n))},isSet:function(){return e.get().isSome()},on:function(n){return e.get().each(n)}}},tm=function(n){var r="navigateEvent",e=yr([Br("fields"),Lr("maxFieldIndex",n.fields.length-1),Br("onExecute"),Br("getInitialValue"),$r("state",function(){return{dialogSwipeState:em(),currentScreen:ko(0)}})]),u=_r("SerialisedDialog",e,n),o=function(e,n,t){return If.sketch({dom:Vf('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){ce(n,r,{direction:e})},buttonBehaviours:bo([Yd.config({disableClass:Ti("toolbar-navigation-disabled"),disabled:function(){return!t}})])})},i=function(n,o){var i=Ui(n.element,"."+Ti("serialised-dialog-screen"));Qi(n.element,"."+Ti("serialised-dialog-chain")).each(function(r){0<=u.state.currentScreen.get()+o&&u.state.currentScreen.get()+o<i.length&&(ji(r,"left").each(function(n){var e=parseInt(n,10),t=ls(i[0]);Ri(r,"left",e-o*t+"px")}),u.state.currentScreen.set(u.state.currentScreen.get()+o))})},c=function(r){var n=Ui(r.element,"input");T.from(n[u.state.currentScreen.get()]).each(function(n){r.getSystem().getByDom(n).each(function(n){var e,t;e=r,t=n.element,e.getSystem().triggerFocus(t,e.element)})});var e=f.get(r);Fu.highlightAt(e,u.state.currentScreen.get())},a=Zl(Zd(function(t){return{dom:Vf('<div class="${prefix}-serialised-dialog"></div>'),components:[Td.sketch({dom:Vf('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:N(u.fields,function(n,e){return e<=u.maxFieldIndex?Td.sketch({dom:Vf('<div class="${prefix}-serialised-dialog-screen"></div>'),components:[o(-1,"previous",0<e),t.field(n.name,n.spec),o(1,"next",e<u.maxFieldIndex)]}):t.field(n.name,n.spec)})})],formBehaviours:bo([wi(function(n,e){var t;t=e,Qi(n.element,"."+Ti("serialised-dialog-chain")).each(function(n){Ri(n,"left",-u.state.currentScreen.get()*t.width+"px")})}),ba.config({mode:"special",focusIn:function(n,e){c(n)},onTab:function(n,e){return i(n,1),T.some(!0)},onShiftTab:function(n,e){return i(n,-1),T.some(!0)}}),xd("form-events",[io(function(e,n){u.state.currentScreen.set(0),u.state.dialogSwipeState.clear();var t=f.get(e);Fu.highlightFirst(t),u.getInitialValue(e).each(function(n){as.setValue(e,n)})}),ao(u.onExecute),Zr(Ln(),function(n,e){"left"===e.event.raw.propertyName&&c(n)}),Zr(r,function(n,e){var t=e.event.direction;i(n,t)})])])}})),f=Zl({dom:Vf('<div class="${prefix}-dot-container"></div>'),behaviours:bo([Fu.config({highlightClass:Ti("dot-active"),itemClass:Ti("dot-item")})]),components:W(u.fields,function(n,e){return e<=u.maxFieldIndex?[Bf('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:Vf('<div class="${prefix}-serializer-wrapper"></div>'),components:[a.asSpec(),f.asSpec()],behaviours:bo([ba.config({mode:"special",focusIn:function(n){var e=a.get(n);ba.focusIn(e)}}),xd("serializer-wrapper-events",[Zr(_n(),function(n,e){var t=e.event;u.state.dialogSwipeState.set({xValue:t.raw.touches[0].clientX,points:[]})}),Zr(In(),function(n,e){var t=e.event;u.state.dialogSwipeState.on(function(n){e.event.prevent(),u.state.dialogSwipeState.set(function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,r={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([r])}}(n,t.raw.touches[0].clientX))})}),Zr(Fn(),function(r,n){u.state.dialogSwipeState.on(function(n){var e=a.get(r),t=-1*function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0}(n);i(e,t)})})])])}},rm=d(function(r,o){return[{label:"the link group",items:[tm({fields:[Bd("url","Type or paste URL"),Bd("text","Link text"),Bd("title","Link title"),Bd("target","Link target"),{name:"link",spec:kd.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return T.none()}})}],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return T.some(gd(o))},onExecute:function(n,e){var t=as.getValue(n);hd(o,t),r.restoreToolbar(),o.focus()}})]}]}),om=function(r,o){return jf(o,"link","link",function(){var n,e,t=rm(r,o);r.setContextToolbar(t),n=o,e=function(){r.focusToolbar()},(yd.os.isAndroid()?bd:s)(e,n),vd(o).each(function(n){o.selection.select(n.dom)})})},im=Jr([{key:Wn(),value:Yr({can:function(n,e){var t,r,o=e.event,i=o.originator,u=o.target;return r=u,!(ve(t=i,n.element)&&!ve(t,r))||(console.warn(Wn()+" did not get interpreted by the desired target. \nOriginator: "+Ho(i)+"\nTarget: "+Ho(u)+"\nCheck the "+Wn()+" event handlers"),!1)}})}]),um=/* */Object.freeze({__proto__:null,events:im}),cm=v,am=function(e){var n=function(n){return function(){throw new Error("The component must be in a context to send: "+n+(e?"\n"+Ho(e().element)+" is not in context.":""))}};return{debugInfo:y("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:l}},fm=am(),sm=function(n,o){var i={};return nt(n,function(n,r){nt(n,function(n,e){var t=it(i,e).getOr([]);i[e]=t.concat([o(r,n)])})}),i},lm=function(u,c,n,a){try{var e=(t=n,r=function(n,e){var t=n[c],r=e[c],o=a.indexOf(t),i=a.indexOf(r);if(-1===o)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+JSON.stringify(a,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(a,null,2));return o<i?-1:i<o?1:0},(o=R.call(t,0)).sort(r),o);return Ct.value(e)}catch(i){return Ct.error([i])}var t,r,o},dm=function(n,e){return{cHandler:b.apply(undefined,[n.handler].concat(e)),purpose:n.purpose}},mm=function(n){return n.cHandler},gm=function(n,e){return{name:n,handler:e}},pm=function(n,e,t){var r,o,i=x(x({},t),(r=n,o={},P(e,function(n){o[n.name()]=n.handlers(r)}),o));return sm(i,gm)},hm=function(n){var e,i=I(e=n)?{can:y(!0),abort:y(!1),run:e}:e;return function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];var o=[n,e].concat(t);i.abort.apply(undefined,o)?e.stop():i.can.apply(undefined,o)&&i.run.apply(undefined,o)}},vm=function(n,e,t){var r,o,i=e[t];return i?lm("Event: "+t,"name",n,i).map(function(n){var e=N(n,function(n){return n.handler});return Kr(e)}):(r=t,o=n,Ct.error(["The event ("+r+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(N(o,function(n){return n.name}),null,2)]))},ym=function(n,i){var e=ot(n,function(r,o){return(1===r.length?Ct.value(r[0].handler):vm(r,i,o)).map(function(n){var e=hm(n),t=1<r.length?H(i[o],function(e){return j(r,function(n){return n.name===e})}).join(" > "):r[0].name;return or(o,{handler:e,purpose:t})})});return ur(e,{})},bm="alloy.base.behaviour",xm=function(n){var e,t;return Mr("custom.definition",yr([Sr("dom","dom",Qt(),yr([Br("tag"),Lr("styles",{}),Lr("classes",[]),Lr("attributes",{}),Nr("value"),Nr("innerHtml")])),Br("components"),Br("uid"),Lr("events",{}),Lr("apis",{}),Sr("eventOrder","eventOrder",((e={})[Kn()]=["disabling",bm,"toggling","typeaheadevents"],e[Wn()]=[bm,"focusing","keying"],e[ne()]=[bm,"disabling","toggling","representing"],e[Pn()]=[bm,"representing","streaming","invalidating"],e[te()]=[bm,"representing","item-events","tooltipping"],e[Rn()]=["focusing",bm,"item-type-events"],e[_n()]=["focusing",bm,"item-type-events"],e[An()]=["item-type-events","tooltipping"],e[Yn()]=["receiving","reflecting","tooltipping"],t=e,Kt.mergeWithThunk(y(t))),Rr()),Nr("domModification")]),n)},wm=function(e,n){P(n,function(n){bt(e,n)})},Sm=function(e,n){P(n,function(n){wt(e,n)})},Om=function(n,e){return t=n,o=N(r=e,function(n){return Hr(n.name(),[Br("config"),Lr("state",vo)])}),i=Mr("component.behaviours",yr(o),t.behaviours).fold(function(n){throw new Error(Ir(n)+"\nComplete spec:\n"+JSON.stringify(t,null,2))},function(n){return n}),{list:r,data:et(i,function(n){var e=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return e}})};var t,r,o,i},Tm=function(n){var e,t,r=(e=it(n,"behaviours").getOr({}),t=H(Qe(e),function(n){return e[n]!==undefined}),N(t,function(n){return e[n].me}));return Om(n,r)},km=function(n,e,t){var r,o,i,u=x(x({},(r=n).dom),{uid:r.uid,domChildren:N(r.components,function(n){return n.element})}),c=n.domModification.fold(function(){return lo({})},lo),a={"alloy.base.modification":c},f=0<e.length?function(e,n,t,r){var o=x({},n);P(t,function(n){o[n.name()]=n.exhibit(e,r)});var i=sm(o,function(n,e){return{name:n,modification:e}}),u=function(n){return z(n,function(n,e){return x(x({},e.modification),n)},{})},c=z(i.classes,function(n,e){return e.modification.concat(n)},[]),a=u(i.attributes),f=u(i.styles);return lo({classes:c,attributes:a,styles:f})}(t,a,e,u):c;return i=f,x(x({},o=u),{attributes:x(x({},o.attributes),i.attributes),styles:x(x({},o.styles),i.styles),classes:o.classes.concat(i.classes)})},Em=function(n,e,t){var r,o,i,u={"alloy.base.behaviour":n.events};return r=t,o=n.eventOrder,i=pm(r,e,u),ym(i,o).getOrDie()},Cm=function(t){var n=function(){return s},r=ko(fm),e=Dr(xm(t)),o=Tm(t),i=o.list,u=o.data,c=function(n){var e=de.fromTag(n.tag);st(e,n.attributes),wm(e,n.classes),Vi(e,n.styles),n.innerHtml.each(function(n){return No(e,n)});var t=n.domChildren;return Re(e,t),n.value.each(function(n){Cd(e,n)}),n.uid,vf(e,n.uid),e}(km(e,i,u)),a=Em(e,i,u),f=ko(e.components),s={getSystem:r.get,config:function(n){var e=u;return(I(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(t,null,2))})()},hasConfigured:function(n){return I(u[n.name()])},spec:t,readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return e.apis},connect:function(n){r.set(n)},disconnect:function(){r.set(am(n))},element:c,syncComponents:function(){var n=Ce(c),e=W(n,function(n){return r.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(e)},components:f.get,events:a};return s},Mm=function(n){var e,t=cm(n),r=t.events,o=c(t,["events"]),i=(e=it(o,"components").getOr([]),N(e,Fm)),u=x(x({},o),{events:x(x({},um),r),components:i});return Ct.value(Cm(u))},Dm=function(n){var e=de.fromText(n);return _m({element:e})},_m=function(n){var e=_r("external.component",vr([Br("element"),Nr("uid")]),n),t=ko(am());e.uid.each(function(n){vf(e.element,n)});var r={getSystem:t.get,config:T.none,hasConfigured:l,connect:function(n){t.set(n)},disconnect:function(){t.set(am(function(){return r}))},getApis:function(){return{}},element:e.element,spec:n,readState:y("No state"),syncComponents:O,components:y([]),events:{}};return lf(r)},Im=bf,Fm=function(e){return it(e,sf).fold(function(){var n=e.hasOwnProperty("uid")?e:x({uid:Im("")},e);return Mm(n).getOrDie()},function(n){return n})},Rm=lf,Vm="alloy.item-hover",Bm="alloy.item-focus",Am=function(n){(Do(n.element).isNone()||_i.isFocused(n))&&(_i.isFocused(n)||_i.focus(n),ce(n,Vm,{item:n}))},jm=function(n){ce(n,Bm,{item:n})},Nm=y(Vm),Pm=y(Bm),Hm=[Br("data"),Br("components"),Br("dom"),Lr("hasSubmenu",!1),Nr("toggling"),Oa("itemBehaviours",[bi,_i,ba,as]),Lr("ignoreFocus",!1),Lr("domModification",{}),ri("builder",function(n){return{dom:n.dom,domModification:x(x({},n.domModification),{attributes:x(x(x({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:Ta(n.itemBehaviours,[n.toggling.fold(bi.revoke,function(n){return bi.config(x({aria:{mode:"checked"}},n))}),_i.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){jm(n)}}),ba.config({mode:"execution"}),as.config({store:{mode:"memory",initialValue:n.data}}),xd("item-type-events",u(ki(),[Zr(An(),Am),Zr(Jn(),_i.focus)]))]),components:n.components,eventOrder:n.eventOrder}}),Lr("eventOrder",{})],zm=[Br("dom"),Br("components"),ri("builder",function(n){return{dom:n.dom,components:n.components,events:Jr([(e=Jn(),Zr(e,function(n,e){e.stop()}))])};var e})],Lm=function(){return"item-widget"},Gm=y([Ka({name:"widget",overrides:function(e){return{behaviours:bo([as.config({store:{mode:"manual",getValue:function(n){return e.data},setValue:function(){}}})])}}})]),$m=[Br("uid"),Br("data"),Br("components"),Br("dom"),Lr("autofocus",!1),Lr("ignoreFocus",!1),Oa("widgetBehaviours",[as,_i,ba]),Lr("domModification",{}),ff(Gm()),ri("builder",function(t){var n=rf(Lm(),t,Gm()),e=of(Lm(),t,n.internals()),r=function(n){return uf(n,t,"widget").map(function(n){return ba.focusIn(n),n})},o=function(n,e){return Yu(e.event.target)||t.autofocus&&e.setSource(n.element),T.none()};return{dom:t.dom,components:e,domModification:t.domModification,events:Jr([ao(function(n,e){r(n).each(function(n){e.stop()})}),Zr(An(),Am),Zr(Jn(),function(n,e){t.autofocus?r(n):_i.focus(n)})]),behaviours:Ta(t.widgetBehaviours,[as.config({store:{mode:"memory",initialValue:t.data}}),_i.config({ignore:t.ignoreFocus,onFocus:function(n){jm(n)}}),ba.config({mode:"special",focusIn:t.autofocus?function(n){r(n)}:Oo(),onLeft:o,onRight:o,onEscape:function(n,e){return _i.isFocused(n)||t.autofocus?(t.autofocus&&e.setSource(n.element),T.none()):(_i.focus(n),T.some(!0))}})])}})],Um=Fr("type",{widget:$m,item:Hm,separator:zm}),Wm=y([Qa({factory:{sketch:function(n){var e=_r("menu.spec item",Um,n);return e.builder(e)}},name:"items",unit:"item",defaults:function(n,e){return e.hasOwnProperty("uid")?e:x(x({},e),{uid:bf("item")})},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),Xm=y([Br("value"),Br("items"),Br("dom"),Br("components"),Lr("eventOrder",{}),xa("menuBehaviours",[Fu,as,Od,ba]),Gr("movement",{mode:"menu",moveOnTab:!0},Fr("mode",{grid:[ii(),ri("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:e.initSize.numColumns,numRows:e.initSize.numRows},focusManager:n.focusManager}})],matrix:[ri("config",function(n,e){return{mode:"matrix",selectors:{row:e.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),Br("rowSelector")],menu:[Lr("moveOnTab",!0),ri("config",function(n,e){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:e.moveOnTab,focusManager:n.focusManager}})]})),Ar("markers",Ko()),Lr("fakeFocus",!1),Lr("focusManager",Vu()),Zo("onHighlight")]),qm=y("alloy.menu-focus"),Ym=_f({name:"Menu",configFields:Xm(),partFields:Wm(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:Sa(n.menuBehaviours,[Fu.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),as.config({store:{mode:"memory",initialValue:n.value}}),Od.config({find:T.some}),ba.config(n.movement.config(n,n.movement))]),events:Jr([Zr(Pm(),function(e,t){var n=t.event;e.getSystem().getByDom(n.target).each(function(n){Fu.highlight(e,n),t.stop(),ce(e,qm(),{menu:e,item:n})})}),Zr(Nm(),function(n,e){var t=e.event.item;Fu.highlight(n,t)})]),components:e,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Km=function(n,t){var r=Ne(t),e=Mo(r).bind(function(e){var o,i,n=function(n){return ve(e,n)};return n(t)?T.some(t):(o=n,(i=function(n){for(var e=0;e<n.childNodes.length;e++){var t=de.fromDom(n.childNodes[e]);if(o(t))return T.some(t);var r=i(n.childNodes[e]);if(r.isSome())return r}return T.none()})(t.dom))}),o=n(t);return e.each(function(e){Mo(r).filter(function(n){return ve(n,e)}).fold(function(){Eo(e)},O)}),o},Jm=function(n,e,t,r){var o=n.getSystem().build(r);Ye(n,o,t)},Qm=function(n,e,t,r){var o=Zm(n);G(o,function(n){return ve(r.element,n.element)}).each(Ke)},Zm=function(n,e){return n.components()},ng=function(e,n,t,o,r){var i=Zm(e);return T.from(i[o]).map(function(n){return Qm(e,0,0,n),r.each(function(n){Jm(e,0,function(n,e){var t,r;r=e,Me(t=n,o).fold(function(){Fe(t,r)},function(n){De(n,r)})},n)}),n})},eg=wo({fields:[],name:"replacing",apis:/* */Object.freeze({__proto__:null,append:function(n,e,t,r){Jm(n,0,Fe,r)},prepend:function(n,e,t,r){Jm(n,0,Ie,r)},remove:Qm,replaceAt:ng,replaceBy:function(e,n,t,r,o){var i=Zm(e);return $(i,r).bind(function(n){return ng(e,0,0,n,o)})},set:function(e,n,t,r){Km(function(){var n=N(r,e.getSystem().build);qe(e,n)},e.element)},contents:Zm})}),tg=function(t,r,o,n){return it(o,n).bind(function(n){return it(t,n).bind(function(n){var e=tg(t,r,o,n);return T.some([n].concat(e))})}).getOr([])},rg=function(n,e){var t={};nt(n,function(n,e){P(n,function(n){t[n]=e})});var r=e,o=tt(e,function(n,e){return{k:n,v:e}}),i=et(o,function(n,e){return[e].concat(tg(t,r,o,e))});return et(t,function(n){return it(i,n).getOr([n])})},og=function(n){return"prepared"===n.type?T.some(n.menu):T.none()},ig={init:function(){var i=ko({}),u=ko({}),c=ko({}),a=ko(T.none()),f=ko({}),s=function(t){return function(n,e){for(var t=Qe(n),r=0,o=t.length;r<o;r++){var i=t[r],u=n[i];if(e(u,i,n))return T.some(u)}return T.none()}(i.get(),function(n,e){return n===t})},l=function(n){return e(n).bind(og)},e=function(n){return it(u.get(),n)},t=function(n){return it(i.get(),n)};return{setMenuBuilt:function(n,e){var t;u.set(x(x({},u.get()),((t={})[n]={type:"prepared",menu:e},t)))},setContents:function(n,e,t,r){a.set(T.some(n)),i.set(t),u.set(e),f.set(r);var o=rg(r,t);c.set(o)},expand:function(t){return it(i.get(),t).map(function(n){var e=it(c.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return it(c.get(),n)},collapse:function(n){return it(c.get(),n).bind(function(n){return 1<n.length?T.some(n.slice(1)):T.none()})},lookupMenu:e,lookupItem:t,otherMenus:function(n){var e=f.get();return Y(Qe(e),n)},getPrimary:function(){return a.get().bind(l)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),c.set({}),a.set(T.none())},isClear:function(){return a.get().isNone()},getTriggeringPath:function(n,u){var e=H(t(n).toArray(),function(n){return l(n).isSome()});return it(c.get(),n).bind(function(n){var i=q(e.concat(n));return function(n){for(var e=[],t=0;t<n.length;t++){var r=n[t];if(!r.isSome())return T.none();e.push(r.getOrDie())}return T.some(e)}(W(i,function(n,e){return t=n,r=u,o=i.slice(0,e+1),l(t).bind(function(e){return s(t).bind(function(n){return r(n).map(function(n){return{triggeredMenu:e,triggeringItem:n,triggeringPath:o}})})}).fold(function(){return a.get().is(n)?[]:[T.none()]},function(n){return[T.some(n)]});var t,r,o}))})}}},extractPreparedMenu:og},ug=y("collapse-item"),cg=Df({name:"TieredMenu",configFields:[ti("onExecute"),ti("onEscape"),ei("onOpenMenu"),ei("onOpenSubmenu"),Zo("onRepositionMenu"),Zo("onCollapseMenu"),Lr("highlightImmediately",!0),jr("data",[Br("primary"),Br("menus"),Br("expansions")]),Lr("fakeFocus",!1),Zo("onHighlight"),Zo("onHover"),jr("markers",[Br("backgroundMenu")].concat(qo()).concat(Yo())),Br("dom"),Lr("navigateOnHover",!0),Lr("stayInDom",!1),xa("tmenuBehaviours",[ba,Fu,Od,eg]),Lr("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)},highlightPrimary:function(n,e){n.highlightPrimary(e)},repositionMenus:function(n,e){n.repositionMenus(e)}},factory:function(c,n){var a,e,i=ko(T.none()),o=function(r,o,n){return et(n,function(n,e){var t=function(){return Ym.sketch(x(x({},n),{value:e,markers:c.markers,fakeFocus:c.fakeFocus,onHighlight:c.onHighlight,focusManager:c.fakeFocus?{get:o=function(n){return Fu.getHighlighted(n).map(function(n){return n.element})},set:function(e,n){var t=o(e);e.getSystem().getByDom(n).fold(O,function(n){Fu.highlight(e,n)});var r=o(e);Ru(e,t,r)}}:Vu()}));var o};return e===o?{type:"prepared",menu:r.getSystem().build(t())}:{type:"notbuilt",nbMenu:t}})},f=ig.init(),s=function(n){return as.getValue(n).value},u=function(n){return et(c.data.menus,function(n,e){return W(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})},l=function(e,n){Fu.highlight(e,n),Fu.getHighlighted(n).orThunk(function(){return Fu.getFirst(n)}).each(function(n){fe(e,n.element,Jn())})},d=function(e,n){return wu(N(n,function(n){return e.lookupMenu(n).bind(function(n){return"prepared"===n.type?T.some(n.menu):T.none()})}))},m=function(e,n,t){var r=d(n,n.otherMenus(t));P(r,function(n){Sm(n.element,[c.markers.backgroundMenu]),c.stayInDom||eg.remove(e,n)})},g=function(n,r){var o,e=(o=n,i.get().getOrThunk(function(){var t={},n=Ui(o.element,"."+c.markers.item),e=H(n,function(n){return"true"===lt(n,"aria-haspopup")});return P(e,function(n){o.getSystem().getByDom(n).each(function(n){var e=s(n);t[e]=n})}),i.set(T.some(t)),t}));nt(e,function(n,e){var t=A(r,e);ft(n.element,"aria-expanded",t)})},p=function(r,o,i){return T.from(i[0]).bind(function(n){return o.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return T.none();var e=n.menu,t=d(o,i.slice(1));return P(t,function(n){bt(n.element,c.markers.backgroundMenu)}),Le(e.element)||eg.append(r,Rm(e)),Sm(e.element,[c.markers.backgroundMenu]),l(r,e),m(r,o,i),T.some(e)})})};(e=a=a||{})[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent";var h=function(o,i,u){void 0===u&&(u=a.HighlightSubmenu);var n=s(i);return f.expand(n).bind(function(r){return g(o,r),T.from(r[0]).bind(function(t){return f.lookupMenu(t).bind(function(n){var e=function(n,e,t){if("notbuilt"!==t.type)return t.menu;var r=n.getSystem().build(t.nbMenu());return f.setMenuBuilt(e,r),r}(o,t,n);return Le(e.element)||eg.append(o,Rm(e)),c.onOpenSubmenu(o,i,e,q(r)),u===a.HighlightSubmenu?(Fu.highlightFirst(e),p(o,f,r)):(Fu.dehighlightAll(e),T.some(i))})})})},r=function(e,t){var n=s(t);return f.collapse(n).bind(function(n){return g(e,n),p(e,f,n).map(function(n){return c.onCollapseMenu(e,t,n),n})})},t=function(t){return function(e,n){return Zi(n.getSource(),"."+c.markers.item).bind(function(n){return e.getSystem().getByDom(n).toOptional().bind(function(n){return t(e,n).map(function(){return!0})})})}},v=Jr([Zr(qm(),function(t,r){var n=r.event.item;f.lookupItem(s(n)).each(function(){var n=r.event.menu;Fu.highlight(t,n);var e=s(r.event.item);f.refresh(e).each(function(n){return m(t,f,n)})})}),ao(function(e,n){var t=n.event.target;e.getSystem().getByDom(t).each(function(n){0===s(n).indexOf("collapse-item")&&r(e,n),h(e,n,a.HighlightSubmenu).fold(function(){c.onExecute(e,n)},function(){})})}),io(function(e,n){var t,r;t=o(e,c.data.primary,c.data.menus),r=u(),f.setContents(c.data.primary,t,c.data.expansions,r),f.getPrimary().each(function(n){eg.append(e,Rm(n)),c.onOpenMenu(e,n),c.highlightImmediately&&l(e,n)})})].concat(c.navigateOnHover?[Zr(Nm(),function(n,e){var t,r,o=e.event.item;t=n,r=s(o),f.refresh(r).bind(function(n){return g(t,n),p(t,f,n)}),h(n,o,a.HighlightParent),c.onHover(n,o)})]:[])),y=function(n){return Fu.getHighlighted(n).bind(Fu.getHighlighted)},b={collapseMenu:function(e){y(e).each(function(n){r(e,n)})},highlightPrimary:function(e){f.getPrimary().each(function(n){l(e,n)})},repositionMenus:function(r){f.getPrimary().bind(function(e){return y(r).bind(function(n){var e,t=s(n),r=(e=f.getMenus(),ot(e,function(n){return n})),o=wu(N(r,ig.extractPreparedMenu));return f.getTriggeringPath(t,function(n){return t=n,Q(o,function(n){if(!n.getSystem().isConnected())return T.none();var e=Fu.getCandidates(n);return G(e,function(n){return s(n)===t})});var t})}).map(function(n){return{primary:e,triggeringPath:n}})}).fold(function(){var n;n=r,T.from(n.components()[0]).filter(function(n){return"menu"===lt(n.element,"role")}).each(function(n){c.onRepositionMenu(r,n,[])})},function(n){var e=n.primary,t=n.triggeringPath;c.onRepositionMenu(r,e,t)})}};return{uid:c.uid,dom:c.dom,markers:c.markers,behaviours:Sa(c.tmenuBehaviours,[ba.config({mode:"special",onRight:t(function(n,e){return Yu(e.element)?T.none():h(n,e,a.HighlightSubmenu)}),onLeft:t(function(n,e){return Yu(e.element)?T.none():r(n,e)}),onEscape:t(function(n,e){return r(n,e).orThunk(function(){return c.onEscape(n,e).map(function(){return n})})}),focusIn:function(e,n){f.getPrimary().each(function(n){fe(e,n.element,Jn())})}}),Fu.config({highlightClass:c.markers.selectedMenu,itemClass:c.markers.menu}),Od.config({find:function(n){return Fu.getHighlighted(n)}}),eg.config({})]),eventOrder:c.eventOrder,apis:b,events:v}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:or(n,e),expansions:{}}},collapseItem:function(n){return{value:Ba(ug()),meta:{text:n}}}}}),ag=function(n,e,t,r){return it(e.routes,r.start).bind(function(n){return it(n,r.destination)})},fg=function(n,e,t,r){return ag(0,e,0,r).bind(function(e){return e.transition.map(function(n){return{transition:n,route:e}})})},sg=function(t,r,n){var e,o,i;i=n,lg(e=t,o=r).bind(function(n){return fg(e,o,i,n)}).each(function(n){var e=n.transition;wt(t.element,e.transitionClass),gt(t.element,r.destinationAttr)})},lg=function(e,t,n){var r=e.element;return dt(r,t.destinationAttr).map(function(n){return{start:lt(e.element,t.stateAttr),destination:n}})},dg=function(n,e,t,r){sg(n,e,t),mt(n.element,e.stateAttr)&&lt(n.element,e.stateAttr)!==r&&e.onFinish(n,r),ft(n.element,e.stateAttr,r)},mg=/* */Object.freeze({__proto__:null,findRoute:ag,disableTransition:sg,getCurrentRoute:lg,jumpTo:dg,progressTo:function(t,r,o,i){var e,u;u=r,mt((e=t).element,u.destinationAttr)&&(dt(e.element,u.destinationAttr).each(function(n){ft(e.element,u.stateAttr,n)}),gt(e.element,u.destinationAttr));var n,c,a=(n=r,c=i,{start:lt(t.element,n.stateAttr),destination:c});fg(t,r,o,a).fold(function(){dg(t,r,o,i)},function(n){sg(t,r,o);var e=n.transition;bt(t.element,e.transitionClass),ft(t.element,r.destinationAttr,i)})},getState:function(n,e,t){return dt(n.element,e.stateAttr)}}),gg=/* */Object.freeze({__proto__:null,events:function(o,i){return Jr([Zr(Ln(),function(t,n){var r=n.event.raw;lg(t,o).each(function(e){ag(0,o,0,e).each(function(n){n.transition.each(function(n){r.propertyName===n.property&&(dg(t,o,i,e.destination),o.onTransition(t,e))})})})}),io(function(n,e){dg(n,o,i,o.initialState)})])}}),pg=[Lr("destinationAttr","data-transitioning-destination"),Lr("stateAttr","data-transitioning-state"),Br("initialState"),Zo("onTransition"),Zo("onFinish"),Ar("routes",Cr(Ct.value,Cr(Ct.value,vr([zr("transition",[Br("property"),Br("transitionClass")])]))))],hg=wo({fields:pg,name:"transitioning",active:gg,apis:mg,extra:{createRoutes:function(n){var r={};return nt(n,function(n,e){var t=e.split("<->");r[t[0]]=or(t[1],n),r[t[1]]=or(t[0],n)}),r},createBistate:function(n,e,t){return ir([{key:n,value:or(e,t)},{key:e,value:or(n,t)}])},createTristate:function(n,e,t,r){return ir([{key:n,value:ir([{key:e,value:r},{key:t,value:r}])},{key:e,value:ir([{key:n,value:r},{key:t,value:r}])},{key:t,value:ir([{key:n,value:r},{key:e,value:r}])}])}}}),vg=Ti("scrollable"),yg=function(n){bt(n,vg)},bg=function(n){wt(n,vg)},xg=vg,wg=function(n){return it(n,"format").getOr(n.title)},Sg=function(n,e,t,r,o){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:o?[Ti("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:Ti("format-matches"),selected:t},itemBehaviours:bo(o?[]:[xi(n,function(n,e){(e?bi.on:bi.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:r},innerHtml:e}}]}},Og=function(n,e,t,r){return{value:n,dom:{tag:"div"},components:[If.sketch({dom:{tag:"div",classes:[Ti("styles-collapser")]},components:r?[{dom:{tag:"span",classes:[Ti("styles-collapse-icon")]}},Dm(n)]:[Dm(n)],action:function(n){var e;r&&(e=t().get(n),cg.collapseMenu(e))}}),{dom:{tag:"div",classes:[Ti("styles-menu-items-container")]},components:[Ym.parts.items({})],behaviours:bo([xd("adhoc-scrollable-menu",[io(function(n,e){Ri(n.element,"overflow-y","auto"),Ri(n.element,"-webkit-overflow-scrolling","touch"),yg(n.element)}),uo(function(n){Ni(n.element,"overflow-y"),Ni(n.element,"-webkit-overflow-scrolling"),bg(n.element)})])])}],items:e,menuBehaviours:bo([hg.config({initialState:"after",routes:hg.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},Tg=function(r){var o,i,n,e,t,u=(o=r.formats,i=function(){return c},n=Og("Styles",[].concat(N(o.items,function(n){return Sg(wg(n),n.title,n.isSelected(),n.getPreview(),ct(o.expansions,wg(n)))})),i,!1),e=et(o.menus,function(n,e){var t=N(n,function(n){return Sg(wg(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",ct(o.expansions,wg(n)))});return Og(e,t,i,!0)}),t=qt(e,or("styles",n)),{tmenu:cg.tieredData("styles",t,o.expansions)}),c=Zl(cg.sketch({dom:{tag:"div",classes:[Ti("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=as.getValue(e);return r.handle(e,t.value),T.none()},onEscape:function(){return T.none()},onOpenMenu:function(n,e){var t=ls(n.element);ss(e.element,t),hg.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var r=ls(n.element),o=Ji(e.element,'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(o).getOrDie();ss(t.element,r),hg.progressTo(i,"before"),hg.jumpTo(t,"after"),hg.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var r=Ji(e.element,'[role="menu"]').getOrDie("hacky"),o=n.getSystem().getByDom(r).getOrDie();hg.progressTo(o,"after"),hg.progressTo(t,"current")},navigateOnHover:!1,highlightImmediately:!0,data:u.tmenu,markers:{backgroundMenu:Ti("styles-background-menu"),menu:Ti("styles-menu"),selectedMenu:Ti("styles-selected-menu"),item:Ti("styles-item"),selectedItem:Ti("styles-selected-item")}}));return c.asSpec()},kg=function(n){return ct(n,"items")?(t=qt(rr(e=n,["items"]),{menu:!0}),r=Eg(e.items),{item:t,menus:qt(r.menus,or(e.title,r.items)),expansions:qt(r.expansions,or(e.title,e.title))}):{item:n,menus:{},expansions:{}};var e,t,r},Eg=function(n){return z(n,function(n,e){var t=kg(e);return{menus:qt(n.menus,t.menus),items:[t.item].concat(n.items),expansions:qt(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},Cg=function(u){var c=function(n){return function(){return u.formatter.match(n)}},a=function(n){return function(){return u.formatter.getCssText(n)}},f=function(n){return N(n,function(n){if(ct(n,"items")){var e=f(n.items);return qt(qt(n,{isSelected:l,getPreview:y("")}),{items:e})}return ct(n,"format")?qt(i=n,{isSelected:c(i.format),getPreview:a(i.format)}):(r=Ba((t=n).title),o=qt(t,{format:r,isSelected:c(r),getPreview:a(r)}),u.formatter.register(r,o),o);var t,r,o,i})};return f(u.getParam("style_formats",Fo,"array"))},Mg=function(t,n,r){var e,o,i,u=(e=t,i=(o=function(n){return W(n,function(n){return n.items===undefined?!ct(n,"format")||e.formatter.canApply(n.format)?[n]:[]:0<o(n.items).length?[n]:[]})})(n),Eg(i));return Tg({formats:u,handle:function(n,e){t.undoManager.transact(function(){bi.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),r()}})},Dg=function(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]},_g=function(n){return W(n,function(n){return(M(n)?_g:Dg)(n)})},Ig=function(n){var e=n.getParam("toolbar",Ro,"array");return(M(e)?_g:Dg)(e)},Fg=function(t,o){var n=function(t){return function(){return Pf(e=t,function(){n.execCommand(e)},{},n=o);var n,e}},e=function(r){return function(){return n=o,t=Af(e=r),Pf(e,function(){n.execCommand(e)},t,n);var n,e,t}},r=function(n,e,t){return function(){return jf(o,n,e,t)}},i=n("undo"),u=n("redo"),c=e("bold"),a=e("italic"),f=e("underline"),s=n("removeformat"),l=r("unlink","link",function(){o.execCommand("unlink",null,!1)}),d=r("unordered-list","ul",function(){o.execCommand("InsertUnorderedList",null,!1)}),m=r("ordered-list","ol",function(){o.execCommand("InsertOrderedList",null,!1)}),g=Cg(o),p=function(){return Mg(o,g,function(){o.fire("scrollIntoView")})},h=function(n,e){return{isSupported:function(){var e=o.ui.registry.getAll().buttons;return n.forall(function(n){return ct(e,n)})},sketch:e}};return{undo:h(T.none(),i),redo:h(T.none(),u),bold:h(T.none(),c),italic:h(T.none(),a),underline:h(T.none(),f),removeformat:h(T.none(),s),link:h(T.none(),function(){return om(t,o)}),unlink:h(T.none(),l),image:h(T.none(),function(){return sd(o)}),bullist:h(T.some("bullist"),d),numlist:h(T.some("numlist"),m),fontsizeselect:h(T.none(),function(){return n={onChange:function(n){Xl(e,n)},getInitialValue:function(){return Wl(e)}},zl(t,"font-size",function(){return Ql(n)},e=o);var e,n}),forecolor:h(T.none(),function(){return n={onChange:function(n,e,t){r.undoManager.transact(function(){r.formatter.apply("forecolor",{value:t}),r.nodeChanged()})},getInitialValue:function(){return-1}},zl(t,"color-levels",function(){return Ll(n)},r=o);var r,n}),styleselect:h(T.none(),function(){return Pf("style-formats",function(n){o.fire("toReading"),t.dropup.appear(p,bi.on,n)},bo([bi.config({toggleClass:Ti("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),ci.config({channels:ir([Si(Bo,bi.off),Si(Ao,bi.off)])})]),o)})}},Rg=function(n){var e=de.fromDom(function(n){if(je()&&_(n.target)){var e=de.fromDom(n.target);if(xe(e)&&ze(e)&&n.composed&&n.composedPath){var t=n.composedPath();if(t)return J(t)}}return T.from(n.target)}(n).getOr(n.target)),t=function(){return n.stopPropagation()},r=function(){return n.preventDefault()},o=i(r,t);return{target:e,x:n.clientX,y:n.clientY,stop:t,prevent:r,kill:o,raw:n}},Vg=function(n,e,t,r,o){var i,u,c=(i=t,u=r,function(n){i(n)&&u(Rg(n))});return n.dom.addEventListener(e,c,o),{unbind:b(Bg,n,e,c,o)}},Bg=function(n,e,t,r){n.dom.removeEventListener(e,t,r)},Ag=w,jg=function(n,e,t){return Vg(n,e,Ag,t,!1)},Ng=function(n,e,t){return Vg(n,e,Ag,t,!0)},Pg=tinymce.util.Tools.resolve("tinymce.util.Delay"),Hg=function(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:y(e)}},zg=function(r,e){var n=de.fromDom(r),o=null,t=jg(n,"orientationchange",function(){Pg.clearInterval(o);var n=Hg(r);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){Pg.clearInterval(o);var e=r.innerHeight,t=0;o=Pg.setInterval(function(){e!==r.innerHeight?(Pg.clearInterval(o),n(T.some(r.innerHeight))):20<t&&(Pg.clearInterval(o),n(T.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}},Lg=function(n,e,t){var r,o,i=n.document.createRange();return r=i,e.fold(function(n){r.setStartBefore(n.dom)},function(n,e){r.setStart(n.dom,e)},function(n){r.setStartAfter(n.dom)}),o=i,t.fold(function(n){o.setEndBefore(n.dom)},function(n,e){o.setEnd(n.dom,e)},function(n){o.setEndAfter(n.dom)}),i},Gg=function(n,e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom,t),i.setEnd(r.dom,o),i},$g=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:n.width,height:n.height}},Ug=Ut([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Wg=function(n,e,t){return e(de.fromDom(t.startContainer),t.startOffset,de.fromDom(t.endContainer),t.endOffset)},Xg=function(n,e){var o,t,r,i=(o=n,e.match({domRange:function(n){return{ltr:y(n),rtl:T.none}},relative:function(n,e){return{ltr:d(function(){return Lg(o,n,e)}),rtl:d(function(){return T.some(Lg(o,e,n))})}},exact:function(n,e,t,r){return{ltr:d(function(){return Gg(o,n,e,t,r)}),rtl:d(function(){return T.some(Gg(o,t,r,n,e))})}}}));return(r=(t=i).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Ug.rtl(de.fromDom(n.endContainer),n.endOffset,de.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return Wg(0,Ug.ltr,r)}):Wg(0,Ug.ltr,r)},qg=(Ug.ltr,Ug.rtl,function(n,e,t,r){return{start:n,soffset:e,finish:t,foffset:r}});var Yg,Kg,Jg,Qg,Zg,np=(Yg=we,Kg="text",{get:function(n){if(!Yg(n))throw new Error("Can only get "+Kg+" value of a "+Kg+" node");return Jg(n).getOr("")},getOption:Jg=function(n){return Yg(n)?T.from(n.dom.nodeValue):T.none()},set:function(n,e){if(!Yg(n))throw new Error("Can only set raw "+Kg+" value of a "+Kg+" node");n.dom.nodeValue=e}}),ep=function(n){return"img"===ye(n)?1:(e=n,np.getOption(e).fold(function(){return Ce(n).length},function(n){return n.length}));var e},tp=Ut([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),rp={before:tp.before,on:tp.on,after:tp.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(v,v,v)}},op=Ut([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),ip={domRange:op.domRange,relative:op.relative,exact:op.exact,exactFromRange:function(n){return op.exact(n.start,n.soffset,n.finish,n.foffset)},getWin:function(n){var e,t=n.match({domRange:function(n){return de.fromDom(n.startContainer)},relative:function(n,e){return rp.getStart(n)},exact:function(n,e,t,r){return n}});return e=t,de.fromDom(ke(e).dom.defaultView)},range:qg},up=function(n,e){var t=ye(n);return"input"===t?rp.after(n):A(["br","img"],t)?0===e?rp.before(n):rp.after(n):rp.on(n,e)},cp=function(n,e,t,r){var o,i,u,c,a,f=(i=e,u=t,c=r,(a=Te(o=n).dom.createRange()).setStart(o.dom,i),a.setEnd(u.dom,c),a),s=ve(n,t)&&e===r;return f.collapsed&&!s},ap=function(n){return T.from(n.getSelection())},fp=function(n,e,t,r,o){var i,u=Gg(n,e,t,r,o);i=u,ap(n).each(function(n){n.removeAllRanges(),n.addRange(i)})},sp=function(l,n){return Xg(l,n).match({ltr:function(n,e,t,r){fp(l,n,e,t,r)},rtl:function(c,a,f,s){ap(l).each(function(n){if(n.setBaseAndExtent)n.setBaseAndExtent(c.dom,a,f.dom,s);else if(n.extend)try{t=c,r=a,o=f,i=s,(e=n).collapse(t.dom,r),e.extend(o.dom,i)}catch(u){fp(l,f,s,c,a)}else fp(l,f,s,c,a);var e,t,r,o,i})}})},lp=function(n,e,t,r,o){var i,u,c,a,f=(i=r,u=o,c=up(e,t),a=up(i,u),ip.relative(c,a));sp(n,f)},dp=function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return T.some(qg(de.fromDom(e.startContainer),e.startOffset,de.fromDom(t.endContainer),t.endOffset))}return T.none()},mp=function(n){if(null===n.anchorNode||null===n.focusNode)return dp(n);var e=de.fromDom(n.anchorNode),t=de.fromDom(n.focusNode);return cp(e,n.anchorOffset,t,n.focusOffset)?T.some(qg(e,n.anchorOffset,t,n.focusOffset)):dp(n)},gp=function(n){return ap(n).filter(function(n){return 0<n.rangeCount}).bind(mp)},pp=function(n,e){var i,t,r,o,u=Xg(i=n,e).match({ltr:function(n,e,t,r){var o=i.document.createRange();return o.setStart(n.dom,e),o.setEnd(t.dom,r),o},rtl:function(n,e,t,r){var o=i.document.createRange();return o.setStart(t.dom,r),o.setEnd(n.dom,e),o}});return r=(t=u).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?T.some(o).map($g):T.none()},hp=function(n){return T.from(n.dom.contentWindow)},vp=function(n){return hp(n).bind(gp)},yp=function(n){return n.getFrame()},bp=function(n,e,t,r){return n[t].getOrThunk(function(){return function(n){return jg(e,r,n)}})},xp=function(c){var n,a=yp(c);return n=a,T.some(de.fromDom(n.dom.contentWindow.document.body)).bind(function(u){return n=a,T.some(de.fromDom(n.dom.contentWindow.document)).bind(function(i){return hp(a).map(function(o){var n=de.fromDom(i.dom.documentElement),e=c.getCursorBox.getOrThunk(function(){return function(){return gp(o).map(function(n){return ip.exact(n.start,n.soffset,n.finish,n.foffset)}).bind(function(n){return pp(o,n).orThunk(function(){return gp(o).filter(function(n){return ve(n.start,n.finish)&&n.soffset===n.foffset}).bind(function(n){var e=n.start.dom.getBoundingClientRect();return 0<e.width||0<e.height?T.some(e):T.none()})})})}}),t=c.setSelection.getOrThunk(function(){return function(n,e,t,r){lp(o,n,e,t,r)}}),r=c.clearSelection.getOrThunk(function(){return function(){ap(o).each(function(n){return n.removeAllRanges()})}});return{body:u,doc:i,win:o,html:n,getSelection:b(vp,a),setSelection:t,clearSelection:r,frame:a,onKeyup:bp(c,i,"onKeyup","keyup"),onNodeChanged:bp(c,i,"onNodeChanged","SelectionChange"),onDomChanged:c.onDomChanged,onScrollToCursor:c.onScrollToCursor,onScrollToElement:c.onScrollToElement,onToReading:c.onToReading,onToEditing:c.onToEditing,onToolbarScrollStart:c.onToolbarScrollStart,onTouchContent:c.onTouchContent,onTapContent:c.onTapContent,onTouchToolstrip:c.onTouchToolstrip,getCursorBox:e}})});var n})},wp=(Qg="getWin",Zg=hp,function(e){return e[Qg].getOrThunk(function(){var n=yp(e);return function(){return Zg(n)}})()}),Sp=function(){var e=Ki("head").getOrDie(),n=Ki('meta[name="viewport"]').getOrThunk(function(){var n=de.fromTag("meta");return ft(n,"name","viewport"),Fe(e,n),n}),t=lt(n,"content");return{maximize:function(){ft(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&0<t.length?ft(n,"content",t):ft(n,"content","user-scalable=yes")}}},Op="data-ephox-mobile-fullscreen-style",Tp="position:absolute!important;",kp="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",Ep=Mn().os.isAndroid(),Cp=function(n,e){var t,r,o,i=function(r){return function(n){var e=lt(n,"style"),t=e===undefined?"no-styles":e.trim();t!==r&&(ft(n,Op,t),ft(n,"style",r))}},u=(t="*",Gi(n,function(n){return me(n,t)},r)),c=W(u,function(n){return e="*",$i(n,function(n){return me(n,e)});var e}),a=(o=Bi(e,"background-color"))!==undefined&&""!==o?"background-color:"+o+"!important":"background-color:rgb(255,255,255)!important;";P(c,i("display:none!important;")),P(u,i(Tp+kp+a)),i((!0===Ep?"":Tp)+kp+a)(n)},Mp=function(){var n=pe("["+Op+"]");P(n,function(n){var e=lt(n,Op);"no-styles"!==e?ft(n,"style",e):gt(n,"style"),gt(n,Op)})};var Dp=function(n){var e=n.raw;return e.touches===undefined||1!==e.touches.length?T.none():T.some(e.touches[0])},_p=function(t){var r,o,i,u=ko(T.none()),c=ko(!1),a=(r=function(n){t.triggerEvent(Zn(),n),c.set(!0)},o=400,i=null,{cancel:function(){null!==i&&(clearTimeout(i),i=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];i=setTimeout(function(){r.apply(null,n),i=null},o)}}),f=ir([{key:_n(),value:function(t){return Dp(t).each(function(n){a.cancel();var e={x:n.clientX,y:n.clientY,target:t.target};a.schedule(t),c.set(!1),u.set(T.some(e))}),T.none()}},{key:In(),value:function(n){return a.cancel(),Dp(n).each(function(i){u.get().each(function(n){var e,t,r,o;e=i,t=n,r=Math.abs(e.clientX-t.x),o=Math.abs(e.clientY-t.y),(5<r||5<o)&&u.set(T.none())})}),T.none()}},{key:Fn(),value:function(e){a.cancel();return u.get().filter(function(n){return ve(n.target,e.target)}).map(function(n){return c.get()?(e.prevent(),!1):t.triggerEvent(Qn(),e)})}}]);return{fireIfReady:function(e,n){return it(f,n).bind(function(n){return n(e)})}}},Ip=function(t){var e=_p({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return jg(t.body,"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return jg(t.body,"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},Fp=6<=Mn().os.version.major,Rp=function(r,e,t){var o=Ip(r),i=Te(e),u=function(n){return!ve(n.start,n.finish)||n.soffset!==n.foffset},n=function(){var n=r.doc.dom.hasFocus()&&r.getSelection().exists(u);t.getByDom(e).each(!0===(n||Mo(i).filter(function(n){return"input"===ye(n)}).exists(function(n){return n.dom.selectionStart!==n.dom.selectionEnd}))?bi.on:bi.off)},c=[jg(r.body,"touchstart",function(n){r.onTouchContent(),o.fireTouchstart(n)}),o.onTouchmove(),o.onTouchend(),jg(e,"touchstart",function(n){r.onTouchToolstrip()}),r.onToReading(function(){Co(r.body)}),r.onToEditing(O),r.onScrollToCursor(function(n){n.preventDefault(),r.getCursorBox().each(function(n){var e=r.win,t=n.top>e.innerHeight||n.bottom>e.innerHeight?n.bottom-e.innerHeight+50:0;0!=t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(!0==Fp?[]:[jg(de.fromDom(r.win),"blur",function(){t.getByDom(e).each(bi.off)}),jg(i,"select",n),jg(r.doc,"selectionchange",n)]);return{destroy:function(){P(c,function(n){n.unbind()})}}},Vp=function(n,e){var t=parseInt(lt(n,e),10);return isNaN(t)?0:t},Bp=function(n){return x(x({},n),{width:2})},Ap=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:n.width,height:n.height}},jp=function(n){var e=n.getSelection();return e!==undefined&&0<e.rangeCount?function(t){if(t.collapsed){var r=de.fromDom(t.startContainer);return Ee(r).bind(function(n){var e=ip.exact(r,t.startOffset,n,ep(n));return pp(t.startContainer.ownerDocument.defaultView,e).map(Bp).map(K)}).getOr([])}return N(t.getClientRects(),Ap)}(e.getRangeAt(0)):[]},Np=function(n){n.focus();var e=de.fromDom(n.document.body);(Mo().exists(function(n){return A(["input","textarea"],ye(n))})?function(n){Pg.setTimeout(function(){n()},0)}:s)(function(){Mo().each(Co),Eo(e)})},Pp="data-"+Ti("last-outer-height"),Hp=function(n,e){ft(n,Pp,e)},zp=function(n){return{top:n.top,bottom:n.top+n.height}},Lp=function(n,e){var t=Vp(e,Pp),r=n.innerHeight;return r<t?T.some(t-r):T.none()},Gp=function(n,u){var e=de.fromDom(u.document.body),t=jg(de.fromDom(n),"resize",function(){Lp(n,e).each(function(i){var n;(0<(n=jp(u)).length?T.some(n[0]).map(zp):T.none()).each(function(n){var e,t,r,o=(e=u,r=i,(t=n).top>e.innerHeight||t.bottom>e.innerHeight?Math.min(r,t.bottom-e.innerHeight+50):0);0!==o&&u.scrollTo(u.pageXOffset,u.pageYOffset+o)})}),Hp(e,n.innerHeight)});Hp(e,n.innerHeight);return{toEditing:function(){Np(u)},destroy:function(){t.unbind()}}},$p=function(t,r){var o=null;return{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==o&&clearTimeout(o),o=setTimeout(function(){t.apply(null,n),o=null},r)}}},Up=function(n,e){var t,r,o,i=Zl(Td.sketch({dom:Vf('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:bo([bi.config({toggleClass:Ti("mask-tap-icon-selected"),toggleOnExecute:!1})])})),u=(t=n,r=200,o=null,{cancel:function(){null!==o&&(clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null===o&&(o=setTimeout(function(){t.apply(null,n),o=null},r))}});return Td.sketch({dom:Vf('<div class="${prefix}-disabled-mask"></div>'),components:[Td.sketch({dom:Vf('<div class="${prefix}-content-container"></div>'),components:[If.sketch({dom:Vf('<div class="${prefix}-content-tap-section"></div>'),components:[i.asSpec()],action:function(n){u.throttle()},buttonBehaviours:bo([bi.config({toggleClass:Ti("mask-tap-icon-selected")})])})]})]})},Wp=y({unbind:O}),Xp=yr([jr("editor",[Br("getFrame"),Nr("getBody"),Nr("getDoc"),Nr("getWin"),Nr("getSelection"),Nr("setSelection"),Nr("clearSelection"),Nr("cursorSaver"),Nr("onKeyup"),Nr("onNodeChanged"),Nr("getCursorBox"),Br("onDomChanged"),Lr("onTouchContent",O),Lr("onTapContent",O),Lr("onTouchToolstrip",O),Lr("onScrollToCursor",Wp),Lr("onScrollToElement",Wp),Lr("onToEditing",Wp),Lr("onToReading",Wp),Lr("onToolbarScrollStart",v)]),Br("socket"),Br("toolstrip"),Br("dropup"),Br("toolbar"),Br("container"),Br("alloy"),$r("win",function(n){return Te(n.socket).dom.defaultView}),$r("body",function(n){return de.fromDom(n.socket.dom.ownerDocument.body)}),Lr("translate",v),Lr("setReadOnly",O),Lr("readOnlyOnInit",w)]),qp=function(n){var e=_r("Getting AndroidWebapp schema",Xp,n);Ri(e.toolstrip,"width","100%");var t=Fm(Up(function(){e.setReadOnly(e.readOnlyOnInit()),f.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};Fe(e.container,t.element);var o,i,u,c,a,f=(o=e,i=r,u=Sp(),c=nm(),a=nm(),{enter:function(){i.hide(),bt(o.container,Ti("fullscreen-maximized")),bt(o.container,Ti("android-maximized")),u.maximize(),bt(o.body,Ti("android-scroll-reload")),c.set(Gp(o.win,wp(o.editor).getOrDie("no"))),xp(o.editor).each(function(n){Cp(o.container,n.body),a.set(Rp(n,o.toolstrip,o.alloy))})},exit:function(){u.restore(),i.show(),wt(o.container,Ti("fullscreen-maximized")),wt(o.container,Ti("android-maximized")),Mp(),wt(o.body,Ti("android-scroll-reload")),a.clear(),c.clear()}});return{setReadOnly:e.setReadOnly,refreshStructure:O,enter:f.enter,exit:f.exit,destroy:O}},Yp=y([Br("dom"),Lr("shell",!0),xa("toolbarBehaviours",[eg])]),Kp=y([Ja({name:"groups",overrides:function(){return{behaviours:bo([eg.config({})])}}})]),Jp=_f({name:"Toolbar",configFields:Yp(),partFields:Kp(),factory:function(e,n,t,r){var o=function(n){return e.shell?T.some(n):uf(n,e,"groups")},i=e.shell?{behaviours:[eg.config({})],components:[]}:{behaviours:[],components:n};return{uid:e.uid,dom:e.dom,components:i.components,behaviours:Sa(e.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,e){o(n).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){eg.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),Qp=y([Br("items"),jr("markers",N(["itemSelector"],Br)),xa("tgroupBehaviours",[ba])]),Zp=y([Qa({name:"items",unit:"item"})]),nh=_f({name:"ToolbarGroup",configFields:Qp(),partFields:Zp(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,components:e,behaviours:Sa(n.tgroupBehaviours,[ba.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),eh="data-"+Ti("horizontal-scroll"),th=function(n){return"true"===lt(n,eh)?0<(t=n).dom.scrollLeft||function(n){n.dom.scrollLeft=1;var e=0!==n.dom.scrollLeft;return n.dom.scrollLeft=0,e}(t):0<(e=n).dom.scrollTop||function(n){n.dom.scrollTop=1;var e=0!==n.dom.scrollTop;return n.dom.scrollTop=0,e}(e);var e,t},rh=function(){var e=function(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:Vf('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:bo([xd("adhoc-scrollable-toolbar",!0===n.scrollable?[co(function(n,e){var t;Ri(n.element,"overflow-x","auto"),t=n.element,ft(t,eh,"true"),yg(n.element)})]:[])]),components:[Td.sketch({components:[nh.parts.items({})]})],markers:{itemSelector:"."+Ti("toolbar-group-item")},items:n.items}},t=Fm(Jp.sketch({dom:Vf('<div class="${prefix}-toolbar"></div>'),components:[Jp.parts.groups({})],toolbarBehaviours:bo([bi.config({toggleClass:Ti("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),ba.config({mode:"cyclic"})]),shell:!0})),n=Fm(Td.sketch({dom:{classes:[Ti("toolstrip")]},components:[Rm(t)],containerBehaviours:bo([bi.config({toggleClass:Ti("android-selection-context-toolbar"),toggleOnExecute:!1})])})),r=function(){Jp.setGroups(t,o.get()),bi.off(t)},o=ko([]);return{wrapper:n,toolbar:t,createGroups:function(n){return N(n,i(nh.sketch,e))},setGroups:function(n){o.set(n),r()},setContextToolbar:function(n){bi.on(t),Jp.setGroups(t,n)},restoreToolbar:function(){bi.isOn(t)&&r()},refresh:function(){},focus:function(){ba.focusIn(t)}}},oh=function(n){return Fm(If.sketch({dom:Vf('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},ih=function(){return Fm(Td.sketch({dom:Vf('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:bo([eg.config({})])}))},uh=function(n,e){eg.append(n,Rm(e))},ch=function(n,e){eg.remove(n,e)},ah=function(n,e,t,r){(!0===t?To.toAlpha:To.toOmega)(r),(t?uh:ch)(n,e)},fh=function(e,n){return n.getAnimationRoot.fold(function(){return e.element},function(n){return n(e)})},sh=function(n){return n.dimension.property},lh=function(n,e){return n.dimension.getDimension(e)},dh=function(n,e){var t=fh(n,e);Sm(t,[e.shrinkingClass,e.growingClass])},mh=function(n,e){wt(n.element,e.openClass),bt(n.element,e.closedClass),Ri(n.element,sh(e),"0px"),Pi(n.element)},gh=function(n,e){wt(n.element,e.closedClass),bt(n.element,e.openClass),Ni(n.element,sh(e))},ph=function(n,e,t,r){t.setCollapsed(),Ri(n.element,sh(e),lh(e,n.element)),Pi(n.element),dh(n,e),mh(n,e),e.onStartShrink(n),e.onShrunk(n)},hh=function(n,e,t,r){var o=r.getOrThunk(function(){return lh(e,n.element)});t.setCollapsed(),Ri(n.element,sh(e),o),Pi(n.element);var i=fh(n,e);wt(i,e.growingClass),bt(i,e.shrinkingClass),mh(n,e),e.onStartShrink(n)},vh=function(n,e,t){var r=lh(e,n.element);("0px"===r?ph:hh)(n,e,t,T.some(r))},yh=function(n,e,t){var r=fh(n,e),o=St(r,e.shrinkingClass),i=lh(e,n.element);gh(n,e);var u=lh(e,n.element);(o?function(){Ri(n.element,sh(e),i),Pi(n.element)}:function(){mh(n,e)})(),wt(r,e.shrinkingClass),bt(r,e.growingClass),gh(n,e),Ri(n.element,sh(e),u),t.setExpanded(),e.onStartGrow(n)},bh=function(n,e,t){var r=fh(n,e);return!0===St(r,e.growingClass)},xh=function(n,e,t){var r=fh(n,e);return!0===St(r,e.shrinkingClass)},wh=/* */Object.freeze({__proto__:null,refresh:function(n,e,t){var r;t.isExpanded()&&(Ni(n.element,sh(e)),r=lh(e,n.element),Ri(n.element,sh(e),r))},grow:function(n,e,t){t.isExpanded()||yh(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&vh(n,e,t)},immediateShrink:function(n,e,t){t.isExpanded()&&ph(n,e,t,T.none())},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:bh,isShrinking:xh,isTransitioning:function(n,e,t){return!0===bh(n,e)||!0===xh(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?vh:yh)(n,e,t)},disableTransitions:dh}),Sh=/* */Object.freeze({__proto__:null,exhibit:function(n,e,t){var r=e.expanded;return lo(r?{classes:[e.openClass],styles:{}}:{classes:[e.closedClass],styles:or(e.dimension.property,"0px")})},events:function(t,r){return Jr([(n=Ln(),eo(n)(function(n,e){e.event.raw.propertyName===t.dimension.property&&(dh(n,t),r.isExpanded()&&Ni(n.element,t.dimension.property),(r.isExpanded()?t.onGrown:t.onShrunk)(n))}))]);var n}}),Oh=[Br("closedClass"),Br("openClass"),Br("shrinkingClass"),Br("growingClass"),Nr("getAnimationRoot"),Zo("onShrunk"),Zo("onStartShrink"),Zo("onGrown"),Zo("onStartGrow"),Lr("expanded",!1),Ar("dimension",Fr("property",{width:[ri("property","width"),ri("getDimension",function(n){return ls(n)+"px"})],height:[ri("property","height"),ri("getDimension",function(n){return Li(n)+"px"})]}))],Th=wo({fields:Oh,name:"sliding",active:Sh,apis:wh,state:/* */Object.freeze({__proto__:null,init:function(n){var e=ko(n.expanded);return yo({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:b(e.set,!1),setExpanded:b(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),kh=function(e,t){var r=Fm(Td.sketch({dom:{tag:"div",classes:[Ti("dropup")]},components:[],containerBehaviours:bo([eg.config({}),Th.config({closedClass:Ti("dropup-closed"),openClass:Ti("dropup-open"),shrinkingClass:Ti("dropup-shrinking"),growingClass:Ti("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),eg.set(n,[])},onGrown:function(n){e(),t()}}),wi(function(n,e){o(O)})])})),o=function(n){window.requestAnimationFrame(function(){n(),Th.shrink(r)})};return{appear:function(n,e,t){!0===Th.hasShrunk(r)&&!1===Th.isTransitioning(r)&&window.requestAnimationFrame(function(){e(t),eg.set(r,[n()]),Th.grow(r)})},disappear:o,component:r,element:r.element}},Eh=function(n){var e,t;return n.raw.which===nu[0]&&!A(["input","textarea"],ye(n.target))&&(e=n.target,!Zi(e,'[contenteditable="true"]',t).isSome())},Ch=function(){return Mn().browser.isFirefox()},Mh=vr([Ar("triggerEvent",Vr),Lr("stopBackspace",!0)]),Dh=function(e,n){var t,r,o,i,u=_r("Getting GUI events settings",Mh,n),c=_p(u),a=N(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return jg(e,n,function(e){c.fireIfReady(e,n).each(function(n){n&&e.kill()}),u.triggerEvent(n,e)&&e.kill()})}),f=ko(T.none()),s=jg(e,"paste",function(e){c.fireIfReady(e,"paste").each(function(n){n&&e.kill()}),u.triggerEvent("paste",e)&&e.kill(),f.set(T.some(setTimeout(function(){u.triggerEvent(qn(),e)},0)))}),l=jg(e,"keydown",function(n){u.triggerEvent("keydown",n)?n.kill():!0===u.stopBackspace&&Eh(n)&&n.prevent()}),d=(t=e,r=function(n){u.triggerEvent("focusin",n)&&n.kill()},Ch()?Ng(t,"focus",r):jg(t,"focusin",r)),m=ko(T.none()),g=(o=e,i=function(n){u.triggerEvent("focusout",n)&&n.kill(),m.set(T.some(setTimeout(function(){u.triggerEvent(Xn(),n)},0)))},Ch()?Ng(o,"blur",i):jg(o,"focusout",i));return{unbind:function(){P(a,function(n){n.unbind()}),l.unbind(),d.unbind(),g.unbind(),s.unbind(),f.get().each(clearTimeout),m.get().each(clearTimeout)}}},_h=function(n,e){var t=it(n,"target").getOr(e);return ko(t)},Ih=Ut([{stopped:[]},{resume:["element"]},{complete:[]}]),Fh=function(n,r,e,t,o,i){var u,c,a,f,s=n(r,t),l=(u=e,c=o,a=ko(!1),f=ko(!1),{stop:function(){a.set(!0)},cut:function(){f.set(!0)},isStopped:a.get,isCut:f.get,event:u,setSource:c.set,getSource:c.get});return s.fold(function(){return i.logEventNoHandlers(r,t),Ih.complete()},function(e){var t=e.descHandler;return mm(t)(l),l.isStopped()?(i.logEventStopped(r,e.element,t.purpose),Ih.stopped()):l.isCut()?(i.logEventCut(r,e.element,t.purpose),Ih.complete()):Ee(e.element).fold(function(){return i.logNoParent(r,e.element,t.purpose),Ih.complete()},function(n){return i.logEventResponse(r,e.element,t.purpose),Ih.resume(n)})})},Rh=function(e,t,r,n,o,i){return Fh(e,t,r,n,o,i).fold(function(){return!0},function(n){return Rh(e,t,r,n,o,i)},function(){return!1})},Vh=function(n,e,t){var r,o,i=(r=e,o=ko(!1),{stop:function(){o.set(!0)},cut:O,isStopped:o.get,isCut:l,event:r,setSource:f("Cannot set source of a broadcasted event"),getSource:f("Cannot get source of a broadcasted event")});return P(n,function(n){var e=n.descHandler;mm(e)(i)}),i.isStopped()},Bh=function(n,e,t,r,o){var i=_h(t,r);return Rh(n,e,t,r,i,o)},Ah=function(n,e){return{element:n,descHandler:e}},jh=function(n,e){return{id:n,descHandler:e}};function Nh(){var i={};return{registerId:function(r,o,n){nt(n,function(n,e){var t=i[e]!==undefined?i[e]:{};t[o]=dm(n,r),i[e]=t})},unregisterId:function(t){nt(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return it(i,n).map(function(n){return ot(n,function(n,e){return jh(e,n)})}).getOr([])},find:function(n,e,t){var o=it(i,e);return Xr(t,function(n){return t=o,yf(r=n).fold(function(){return T.none()},function(e){return t.bind(function(n){return it(n,e)}).map(function(n){return Ah(r,n)})});var t,r},n)}}}function Ph(){var r=Nh(),o={},i=function(r){var n=r.element;return yf(n).fold(function(){return n="uid-",e=r.element,t=Ba(pf+n),vf(e,t),t;var n,e,t},function(n){return n})},u=function(n){yf(n.element).each(function(n){delete o[n],r.unregisterId(n)})};return{find:function(n,e,t){return r.find(n,e,t)},filter:function(n){return r.filterByType(n)},register:function(n){var e=i(n);ct(o,e)&&function(n,e){var t=o[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+Ho(t.element)+"\nCannot use it for: "+Ho(n.element)+"\nThe conflicting element is"+(Le(t.element)?" ":" not ")+"already in the DOM");u(n)}(n,e);var t=[n];r.registerId(t,e,n.events),o[e]=n},unregister:u,getById:function(n){return it(o,n)}}}var Hh=function(t){var r=function(e){return Ee(t.element).fold(function(){return!0},function(n){return ve(e,n)})},o=Ph(),f=function(n,e){return o.find(r,n,e)},n=Dh(t.element,{triggerEvent:function(r,o){return Wo(r,o.target,function(n){return t=n,Bh(f,r,e=o,e.target,t);var e,t})}}),i={debugInfo:y("real"),triggerEvent:function(e,t,r){Wo(e,t,function(n){return Bh(f,e,r,t,n)})},triggerFocus:function(c,a){yf(c).fold(function(){Eo(c)},function(n){Wo(Wn(),c,function(n){var e,t,r,o,i,u;return e=f,t=Wn(),i=n,u=_h(r={originator:a,kill:O,prevent:O,target:c},o=c),Fh(e,t,r,o,u,i),!1})})},triggerEscape:function(n,e){i.triggerEvent("keydown",n.element,e.event)},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:Fm,addToGui:function(n){c(n)},removeFromGui:function(n){a(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){u(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},broadcastEvent:function(n,e){m(n,e)},isConnected:w},e=function(n){n.connect(i),we(n.element)||(o.register(n),P(n.components(),e),i.triggerEvent(ne(),n.element,{target:n.element}))},u=function(n){we(n.element)||(P(n.components(),u),o.unregister(n)),n.disconnect()},c=function(n){Ye(t,n,Fe)},a=function(n){Ke(n)},s=function(t){var n=o.filter(Yn());P(n,function(n){var e=n.descHandler;mm(e)(t)})},l=function(n){s({universal:!0,data:n})},d=function(n,e){s({universal:!1,channels:n,data:e})},m=function(n,e){var t=o.filter(n);return Vh(t,e)},g=function(n){return o.getById(n).fold(function(){return Ct.error(new Error('Could not find component with uid: "'+n+'" in system.'))},Ct.value)},p=function(n){var e=yf(n).getOr("not found");return g(e)};return e(t),{root:t,element:t.element,destroy:function(){n.unbind(),Be(t.element)},add:c,remove:a,getByUid:g,getByDom:p,addToWorld:e,removeFromWorld:u,broadcast:l,broadcastOn:d,broadcastEvent:m}},zh=Ti("readonly-mode"),Lh=Ti("edit-mode");function Gh(n){var e=Fm(Td.sketch({dom:{classes:[Ti("outer-container")].concat(n.classes)},containerBehaviours:bo([To.config({alpha:zh,omega:Lh})])}));return Hh(e)}var $h=function(n,e){var t=de.fromTag("input");Vi(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),Fe(n,t),Eo(t),e(t),Be(t)},Uh=function(n){var e,t,r=n.getSelection();0<r.rangeCount&&(e=r.getRangeAt(0),(t=n.document.createRange()).setStart(e.startContainer,e.startOffset),t.setEnd(e.endContainer,e.endOffset),r.removeAllRanges(),r.addRange(t))},Wh=function(n,e){Mo().each(function(n){ve(n,e)||Co(n)}),n.focus(),Eo(de.fromDom(n.document.body)),Uh(n)},Xh=function(n,e,t,r){var o=function(){Wh(e,r)},i=jg(t,"keydown",function(n){A(["input","textarea"],ye(n.target))||o()});return{toReading:function(){$h(n,Co)},toEditing:o,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},qh=function(t,r,o,i,n){var u=function(){r.run(function(n){n.refreshSelection()})},e=function(n,e){var t=n-i.dom.scrollTop;r.run(function(n){n.scrollIntoView(t,t+e)})},c=function(){r.run(function(n){n.clearSelection()})},a=function(){t.getCursorBox().each(function(n){e(n.top,n.height)}),r.run(function(n){n.syncHeight()})},f=Ip(t),s=$p(a,300),l=[t.onKeyup(function(){c(),s.throttle()}),t.onNodeChanged(u),t.onDomChanged(s.throttle),t.onDomChanged(u),t.onScrollToCursor(function(n){n.preventDefault(),s.throttle()}),t.onScrollToElement(function(n){n.element,e(r,i)}),t.onToEditing(function(){r.run(function(n){n.toEditing()})}),t.onToReading(function(){r.run(function(n){n.toReading()})}),jg(t.doc,"touchend",function(n){ve(t.html,n.target)||ve(t.body,n.target)}),jg(o,"transitionend",function(n){var e;"height"===n.raw.propertyName&&(e=Li(o),r.run(function(n){n.setViewportOffset(e)}),u(),a())}),Ng(o,"touchstart",function(n){var e;r.run(function(n){n.highlightSelection()}),e=n,r.run(function(n){n.onToolbarTouch(e)}),t.onTouchToolstrip()}),jg(t.body,"touchstart",function(n){c(),t.onTouchContent(),f.fireTouchstart(n)}),f.onTouchmove(),f.onTouchend(),jg(t.body,"click",function(n){n.kill()}),jg(o,"touchmove",function(){t.onToolbarScrollStart()})];return{destroy:function(){P(l,function(n){n.unbind()})}}};var Yh,Kh,Jh,Qh,Zh={},nv={exports:Zh};Yh=undefined,Kh=Zh,Jh=nv,Qh=undefined,function(n){"object"==typeof Kh&&void 0!==Jh?Jh.exports=n():"function"==typeof Yh&&Yh.amd?Yh([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function s(i,u,c){function a(e,n){if(!u[e]){if(!i[e]){var t="function"==typeof Qh&&Qh;if(!n&&t)return t(e,!0);if(f)return f(e,!0);var r=new Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}var o=u[e]={exports:{}};i[e][0].call(o.exports,function(n){return a(i[e][1][n]||n)},o,o.exports,s,i,u,c)}return u[e].exports}for(var f="function"==typeof Qh&&Qh,n=0;n<c.length;n++)a(c[n]);return a}({1:[function(n,e,t){var r,o,i=e.exports={};function u(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}function a(n){if(r===setTimeout)return setTimeout(n,0);if((r===u||!r)&&setTimeout)return r=setTimeout,setTimeout(n,0);try{return r(n,0)}catch(e){try{return r.call(null,n,0)}catch(e){return r.call(this,n,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:u}catch(n){r=u}try{o="function"==typeof clearTimeout?clearTimeout:c}catch(n){o=c}}();var f,s=[],l=!1,d=-1;function m(){l&&f&&(l=!1,f.length?s=f.concat(s):d=-1,s.length&&g())}function g(){if(!l){var n=a(m);l=!0;for(var e=s.length;e;){for(f=s,s=[];++d<e;)f&&f[d].run();d=-1,e=s.length}f=null,l=!1,function(n){if(o===clearTimeout)return clearTimeout(n);if((o===c||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(n);try{o(n)}catch(e){try{return o.call(null,n)}catch(e){return o.call(this,n)}}}(n)}}function p(n,e){this.fun=n,this.array=e}function h(){}i.nextTick=function(n){var e=new Array(arguments.length-1);if(1<arguments.length)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];s.push(new p(n,e)),1!==s.length||l||a(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,e){(function(e){function r(){}function u(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],s(n,this)}function o(r,o){for(;3===r._state;)r=r._value;0!==r._state?(r._handled=!0,u._immediateFn(function(){var n,e=1===r._state?o.onFulfilled:o.onRejected;if(null!==e){try{n=e(r._value)}catch(t){return void c(o.promise,t)}i(o.promise,n)}else(1===r._state?i:c)(o.promise,r._value)})):r._deferreds.push(o)}function i(n,e){try{if(e===n)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if(e instanceof u)return n._state=3,n._value=e,void a(n);if("function"==typeof t)return void s((r=t,o=e,function(){r.apply(o,arguments)}),n)}n._state=1,n._value=e,a(n)}catch(i){c(n,i)}var r,o}function c(n,e){n._state=2,n._value=e,a(n)}function a(n){2===n._state&&0===n._deferreds.length&&u._immediateFn(function(){n._handled||u._unhandledRejectionFn(n._value)});for(var e=0,t=n._deferreds.length;e<t;e++)o(n,n._deferreds[e]);n._deferreds=null}function f(n,e,t){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.promise=t}function s(n,e){var t=!1;try{n(function(n){t||(t=!0,i(e,n))},function(n){t||(t=!0,c(e,n))})}catch(r){if(t)return;t=!0,c(e,r)}}var n,t;n=this,t=setTimeout,u.prototype["catch"]=function(n){return this.then(null,n)},u.prototype.then=function(n,e){var t=new this.constructor(r);return o(this,new f(n,e,t)),t},u.all=function(n){var a=Array.prototype.slice.call(n);return new u(function(o,i){if(0===a.length)return o([]);var u=a.length;for(var n=0;n<a.length;n++)!function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}(n,a[n])})},u.resolve=function(e){return e&&"object"==typeof e&&e.constructor===u?e:new u(function(n){n(e)})},u.reject=function(t){return new u(function(n,e){e(t)})},u.race=function(o){return new u(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},u._immediateFn="function"==typeof e?function(n){e(n)}:function(n){t(n,0)},u._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},u._setImmediateFn=function(n){u._immediateFn=n},u._setUnhandledRejectionFn=function(n){u._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=u:n.Promise||(n.Promise=u)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(a,n,f){(function(n,e){var r=a("process/browser.js").nextTick,t=Function.prototype.apply,o=Array.prototype.slice,i={},u=0;function c(n,e){this._id=n,this._clearFn=e}f.setTimeout=function(){return new c(t.call(setTimeout,window,arguments),clearTimeout)},f.setInterval=function(){return new c(t.call(setInterval,window,arguments),clearInterval)},f.clearTimeout=f.clearInterval=function(n){n.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},f.enroll=function(n,e){clearTimeout(n._idleTimeoutId),n._idleTimeout=e},f.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},f._unrefActive=f.active=function(n){clearTimeout(n._idleTimeoutId);var e=n._idleTimeout;0<=e&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},e))},f.setImmediate="function"==typeof n?n:function(n){var e=u++,t=!(arguments.length<2)&&o.call(arguments,1);return i[e]=!0,r(function(){i[e]&&(t?n.apply(null,t):n.call(null),f.clearImmediate(e))}),e},f.clearImmediate="function"==typeof e?e:function(n){delete i[n]}}).call(this,a("timers").setImmediate,a("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,e,t){var r=n("promise-polyfill"),o="undefined"!=typeof window?window:Function("return this;")();e.exports={boltExport:o.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});var ev,tv=nv.exports.boltExport,rv=function(n){var t=T.none(),e=[],r=function(n){o()?u(n):e.push(n)},o=function(){return t.isSome()},i=function(n){P(n,u)},u=function(e){t.each(function(n){setTimeout(function(){e(n)},0)})};return n(function(n){o()||(t=T.some(n),i(e),e=[])}),{get:r,map:function(t){return rv(function(e){r(function(n){e(t(n))})})},isReady:o}},ov={nu:rv,pure:function(e){return rv(function(n){n(e)})}},iv=function(n){setTimeout(function(){throw n},0)},uv=function(t){var n=function(n){t().then(n,iv)};return{map:function(n){return uv(function(){return t().then(n)})},bind:function(e){return uv(function(){return t().then(function(n){return e(n).toPromise()})})},anonBind:function(n){return uv(function(){return t().then(function(){return n.toPromise()})})},toLazy:function(){return ov.nu(n)},toCached:function(){var n=null;return uv(function(){return null===n&&(n=t()),n})},toPromise:t,get:n}},cv=function(n){return uv(function(){return new tv(n)})},av=function(n){return uv(function(){return tv.resolve(n)})},fv=function(r,o){return Q([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return e=r<=n.width&&o<=n.height,t=n.keyboard,e?T.some(t):T.none();var e,t}).getOr({portrait:o/5,landscape:r/4})},sv=function(n){var e,t=Hg(n).isPortrait(),r=fv((e=n).screen.width,e.screen.height),o=t?r.portrait:r.landscape;return(t?n.screen.height:n.screen.width)-n.innerHeight>o?0:o},lv=function(n,e){var t=Te(n).dom.defaultView;return Li(n)+Li(e)-sv(t)},dv=function(n,e,t){var r=lv(e,t),o=Li(e)+Li(t)-r;Ri(n,"padding-bottom",o+"px")},mv=Ut([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),gv="data-"+Ti("position-y-fixed"),pv="data-"+Ti("y-property"),hv="data-"+Ti("scrolling"),vv="data-"+Ti("last-window-height"),yv=function(n){return Vp(n,gv)},bv=function(n,e){var t=lt(n,pv);return mv.fixed(n,t,e)},xv=function(n,e){return mv.scroller(n,e)},wv=function(n){var e=yv(n);return("true"===lt(n,hv)?xv:bv)(n,e)},Sv=function(n,e,t){var r=Te(n).dom.defaultView.innerHeight;return ft(n,vv,r+"px"),r-e-t},Ov=function(r,o,i,u){var e=Te(r).dom.defaultView,n=function(n){var e=lt(n,"style");Vi(n,{position:"absolute",top:"0px"}),ft(n,gv,"0px"),ft(n,pv,"top");return{restore:function(){ft(n,"style",e||""),gt(n,gv),gt(n,pv)}}}(i),t=Li(i),c=Li(u),a=function(n,e,t){var r=lt(t,"style");yg(t),Vi(t,{position:"absolute",height:e+"px",width:"100%",top:n+"px"}),ft(t,gv,n+"px"),ft(t,hv,"true"),ft(t,pv,"top");return{restore:function(){bg(t),ft(t,"style",r||""),gt(t,gv),gt(t,hv),gt(t,pv)}}}(t,Sv(r,t,c),r),f=function(n){var e=lt(n,"style");Vi(n,{position:"absolute",bottom:"0px"}),ft(n,gv,"0px"),ft(n,pv,"bottom");return{restore:function(){ft(n,"style",e||""),gt(n,gv),gt(n,pv)}}}(u),s=!0,l=function(){var n=e.innerHeight;return Vp(r,vv)<n},d=function(){var n,e,t;s&&(n=Li(i),e=Li(u),t=Sv(r,n,e),ft(r,gv,n+"px"),Ri(r,"height",t+"px"),dv(o,r,u))};return dv(o,r,u),{setViewportOffset:function(n){ft(r,gv,n+"px"),d()},isExpanding:l,isShrinking:m(l),refresh:d,restore:function(){s=!1,n.restore(),a.restore(),f.restore()}}},Tv=(ev=null,{animate:function(o,i,u,c,e,n){var a=!1,f=function(n){a=!0,e(n)};Pg.clearInterval(ev);var s=function(n){Pg.clearInterval(ev),f(n)};ev=Pg.setInterval(function(){var n,e,t,r=o();n=r,e=i,t=u,(Math.abs(n-e)<=t?T.none():n<e?T.some(n+t):T.some(n-t)).fold(function(){Pg.clearInterval(ev),f(i)},function(n){var e;c(n,s),a||((e=o())!==n||Math.abs(e-i)>Math.abs(r-i))&&(Pg.clearInterval(ev),f(i))})},n)}}),kv="data-"+Ti("last-scroll-top"),Ev=function(n){var e=ji(n,"top").getOr("0");return parseInt(e,10)},Cv=function(n){return parseInt(n.dom.scrollTop,10)},Mv=function(o,i){return cv(function(n){var e=b(Cv,o);ft(o,kv,e());var t=Math.abs(i-e()),r=Math.ceil(t/10);Tv.animate(e,i,r,function(n,e){Vp(o,kv)!==o.dom.scrollTop?e(o.dom.scrollTop):(o.dom.scrollTop=n,ft(o,kv,n))},function(){o.dom.scrollTop=i,ft(o,kv,i),n(i)},10)})},Dv=function(n,e){var t=e+yv(n)+"px";Ri(n,"top",t)};var _v=function(u,n){return n(function(r){var o=[],i=0;0===u.length?r([]):P(u,function(n,e){var t;n.get((t=e,function(n){o[t]=n,++i>=u.length&&r(o)}))})})},Iv=function(n,e,t){var r,o,i,u=e+t,c=ji(n,"top").getOr(t),a=u-parseInt(c,10),f=n.dom.scrollTop+a;return r=n,o=f,i=u,cv(function(n){var e=b(Cv,r);Tv.animate(e,o,15,function(n){r.dom.scrollTop=n,Ri(r,"top",Ev(r)+15+"px")},function(){r.dom.scrollTop=o,Ri(r,"top",i+"px"),n(o)},10)})},Fv=function(n,o){return n.fold(function(n,e,t){return Ri(n,e,o+(r=t)+"px"),av(r);var r},function(n,e){return Iv(n,o,e)})},Rv=function(n,e){var t,r=(t=Ui(n,"["+gv+"]"),N(t,wv)),o=N(r,function(n){return Fv(n,e)});return _v(o,cv)},Vv=function(i,u,n,t,e,r){var o,c,a=(o=function(n){return t=u,r=n,o=Te(e=i).dom.defaultView,cv(function(n){Dv(e,r),Dv(t,r),o.scrollTo(0,r),n(r)});var e,t,r,o},c=ko(ov.pure({})),{start:function(e){var n=ov.nu(function(n){return o(e).get(n)});c.set(n)},idle:function(n){c.get().get(function(){n()})}}),f=$p(function(){a.idle(function(){Rv(n,t.pageYOffset).get(function(){var n;(n=jp(r),T.from(n[0]).bind(function(n){var e=n.top-u.dom.scrollTop;return e>t.innerHeight+5||e<-5?T.some({top:e,bottom:e+n.height}):T.none()})).each(function(n){u.dom.scrollTop=u.dom.scrollTop+n.top}),a.start(0),e.refresh()})})},1e3),s=jg(de.fromDom(t),"scroll",function(){t.pageYOffset<0||f.throttle()});return Rv(n,t.pageYOffset).get(v),{unbind:s.unbind}},Bv=function(n){var a=n.cWin,e=n.ceBody,f=n.socket,t=n.toolstrip,r=n.contentElement,o=n.keyboardType,i=n.outerWindow,s=n.dropup,u=n.outerBody,c=Ov(f,e,t,s),l=o(u,a,Ge(),r),d=zg(i,{onChange:O,onReady:c.refresh});d.onAdjustment(function(){c.refresh()});var m=jg(de.fromDom(i),"resize",function(){c.isExpanding()&&c.refresh()}),g=Vv(t,f,u,i,c,a),p=function(t,e){var n=t.document,r=de.fromTag("div");bt(r,Ti("unfocused-selections")),Fe(de.fromDom(n.documentElement),r);var o=jg(r,"touchstart",function(n){n.prevent(),Wh(t,e),u()}),i=function(n){var e=de.fromTag("span");return wm(e,[Ti("layer-editor"),Ti("unfocused-selection")]),Vi(e,{left:n.left+"px",top:n.top+"px",width:n.width+"px",height:n.height+"px"}),e},u=function(){Ve(r)};return{update:function(){u();var n=jp(t),e=N(n,i);Re(r,e)},isActive:function(){return 0<Ce(r).length},destroy:function(){o.unbind(),Be(r)},clear:u}}(a,r),h=function(){p.clear()};return{toEditing:function(){l.toEditing(),h()},toReading:function(){l.toReading()},onToolbarTouch:function(n){l.onToolbarTouch()},refreshSelection:function(){p.isActive()&&p.update()},clearSelection:h,highlightSelection:function(){p.update()},scrollIntoView:function(n,e){var t,r,o,i,u,c;t=a,o=n,i=e,u=lv(r=f,s),c=b(Uh,t),u<o||u<i?Mv(r,r.dom.scrollTop-u+i).get(c):o<0&&Mv(r,r.dom.scrollTop+o).get(c)},updateToolbarPadding:O,setViewportOffset:function(n){var i,u;c.setViewportOffset(n),i=f,u=n,cv(function(n){var e=b(Ev,i),t=function(n){Ri(i,"top",n+"px")},r=Math.abs(u-e()),o=Math.ceil(r/10);Tv.animate(e,u,o,t,function(){t(u),n(u)},10)}).get(v)},syncHeight:function(){Ri(r,"height",r.dom.contentWindow.document.body.scrollHeight+"px")},refreshStructure:c.refresh,destroy:function(){c.restore(),d.destroy(),g.unbind(),m.unbind(),l.destroy(),p.destroy(),$h(Ge(),Co)}}},Av=function(r,n){var o=Sp(),i=em(),u=em(),c=nm(),a=nm();return{enter:function(){n.hide();var t=de.fromDom(document);xp(r.editor).each(function(n){var e;i.set({socketHeight:ji(r.socket,"height"),iframeHeight:ji(n.frame,"height"),outerScroll:document.body.scrollTop}),u.set({exclusives:(e="."+xg,jg(t,"touchmove",function(n){Zi(n.target,e).filter(th).fold(function(){n.prevent()},O)}))}),bt(r.container,Ti("fullscreen-maximized")),Cp(r.container,n.body),o.maximize(),Ri(r.socket,"overflow","scroll"),Ri(r.socket,"-webkit-overflow-scrolling","touch"),Eo(n.body),c.set(Bv({cWin:n.win,ceBody:n.body,socket:r.socket,toolstrip:r.toolstrip,dropup:r.dropup.element,contentElement:n.frame,outerBody:r.body,outerWindow:r.win,keyboardType:Xh})),c.run(function(n){n.syncHeight()}),a.set(qh(n,c,r.toolstrip,r.socket,r.dropup))})},refreshStructure:function(){c.run(function(n){n.refreshStructure()})},exit:function(){o.restore(),a.clear(),c.clear(),n.show(),i.on(function(n){n.socketHeight.each(function(n){Ri(r.socket,"height",n)}),n.iframeHeight.each(function(n){Ri(r.editor.getFrame(),"height",n)}),document.body.scrollTop=n.scrollTop}),i.clear(),u.on(function(n){n.exclusives.unbind()}),u.clear(),wt(r.container,Ti("fullscreen-maximized")),Mp(),bg(r.toolbar),Ni(r.socket,"overflow"),Ni(r.socket,"-webkit-overflow-scrolling"),Co(r.editor.getFrame()),xp(r.editor).each(function(n){n.clearSelection()})}}};function jv(n){var e=Gh({classes:[Ti("ios-container")]}),t=rh(),r=nm(),o=oh(r),i=ih(),u=kh(function(){r.run(function(n){n.refreshStructure()})},n);e.add(t.wrapper),e.add(i),e.add(u.component);return{system:e,element:e.element,init:function(n){r.set(function(n){var e=_r("Getting IosWebapp schema",Xp,n);Ri(e.toolstrip,"width","100%"),Ri(e.container,"position","relative");var t=Fm(Up(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}},o=Av(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:o.refreshStructure,enter:o.enter,exit:o.exit,destroy:O}}(n))},exit:function(){r.run(function(n){eg.remove(i,o),n.exit()})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){ah(i,o,n,e.root)},socket:i,dropup:u}}var Nv=tinymce.util.Tools.resolve("tinymce.EditorManager"),Pv=function(n,e,t){n.system.broadcastOn([Vo],{command:e,state:t})},Hv=function(e){return function(){var n=function(){e._skinLoaded=!0,e.fire("SkinLoaded")};e.initialized?n():e.on("init",n)}},zv="toReading",Lv="toEditing",Gv=function(S){return{getNotificationManagerImpl:function(){return{open:y({progressBar:{value:O},close:O,text:O,getEl:y(null),moveTo:O,moveRel:O,settings:{}}),close:O,reposition:O,getArgs:y({})}},renderUI:function(){var n,e,t,r=S.getElement(),o=(n=S,{content:(e=T.from(n.getParam("skin_url")).fold(function(){return Nv.baseURL+"/skins/ui/oxide"},function(n){return n}))+"/content.mobile.min.css",ui:e+"/skin.mobile.min.css"});!1==(!1===S.getParam("skin"))?(t=_o.DOM.styleSheetLoader,S.contentCSS.push(o.content),t.load(o.ui,Hv(S)),S.on("remove",function(){return t.unload(o.ui)})):Hv(S)();var i,u,c,a,f,s,l,d,m,g=function(){S.fire("ScrollIntoView")},p=Mn().os.isAndroid()?(i=g,u=Gh({classes:[Ti("android-container")]}),c=rh(),a=nm(),f=oh(a),s=ih(),l=kh(O,i),u.add(c.wrapper),u.add(s),u.add(l.component),{system:u,element:u.element,init:function(n){a.set(qp(n))},exit:function(){a.run(function(n){n.exit(),eg.remove(s,f)})},setToolbarGroups:function(n){var e=c.createGroups(n);c.setGroups(e)},setContextToolbar:function(n){var e=c.createGroups(n);c.setContextToolbar(e)},focusToolbar:function(){c.focus()},restoreToolbar:function(){c.restoreToolbar()},updateMode:function(n){ah(s,f,n,u.root)},socket:s,dropup:l}):jv(g),h=de.fromDom(r);d=h,m=p.system,Je(d,m,_e);var v=r.ownerDocument.defaultView,y=zg(v,{onChange:function(){var n,e,t;p.system.broadcastOn([Bo],{width:(n=v,e=Mn().os.isiOS(),t=Hg(n).isPortrait(),e&&!t?n.screen.height:n.screen.width)})},onReady:O}),b=function(n,e,t,r){!1===r&&S.selection.collapse();var o=x(n,e,t);p.setToolbarGroups(!0===r?o.readOnly:o.main),S.setMode(!0===r?"readonly":"design"),S.fire(!0===r?zv:Lv),p.updateMode(r)},x=function(n,e,t){var r=n.get();return{readOnly:r.backToMask.concat(e.get()),main:r.backToMask.concat(t.get())}},w=function(n,e){return S.on(n,e),{unbind:function(){S.off(n)}}};return S.on("init",function(){p.init({editor:{getFrame:function(){return de.fromDom(S.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:O}},onToReading:function(n){return w(zv,n)},onToEditing:function(n){return w(Lv,n)},onScrollToCursor:function(e){S.on("ScrollIntoView",function(n){e(n)});return{unbind:function(){S.off("ScrollIntoView"),y.destroy()}}},onTouchToolstrip:function(){u()},onTouchContent:function(){var n=de.fromDom(S.editorContainer.querySelector("."+Ti("toolbar")));Do(n).bind(function(n){return p.system.getByDom(n).toOptional()}).each(ae),p.restoreToolbar(),u()},onTapContent:function(n){var e=n.target;"img"===ye(e)?(S.selection.select(e.dom),n.kill()):"a"===ye(e)&&p.system.getByDom(de.fromDom(S.editorContainer)).each(function(n){To.isAlpha(n)&&function(n){var e=document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,0,0,!1,!1,!1,!1,0,null),document.body.appendChild(e),e.dispatchEvent(t),document.body.removeChild(e)}(e.dom)})}},container:de.fromDom(S.editorContainer),socket:de.fromDom(S.contentAreaContainer),toolstrip:de.fromDom(S.editorContainer.querySelector("."+Ti("toolstrip"))),toolbar:de.fromDom(S.editorContainer.querySelector("."+Ti("toolbar"))),dropup:p.dropup,alloy:p.system,translate:O,setReadOnly:function(n){b(g,m,d,n)},readOnlyOnInit:function(){return!1}});var t,n,r,o,i,e,u=function(){p.dropup.disappear(function(){p.system.broadcastOn([Ao],{})})},c={label:"The first group",scrollable:!1,items:[Pf("back",function(){S.selection.collapse(),p.exit()},{},S)]},a={label:"Back to read only",scrollable:!1,items:[Pf("readonly-back",function(){b(g,m,d,!0)},{},S)]},f=Fg(p,S),s=(t=f,n=Ig(S),r={},W(n,function(n){var e=!ct(r,n)&&ct(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return r[n]=!0,e})),l={label:"The extra group",scrollable:!1,items:[]},d=ko([{label:"the action group",scrollable:!0,items:s},l]),m=ko([{label:"The read only mode group",scrollable:!0,items:[]},l]),g=ko({backToMask:[c],backToReadOnly:[a]});o=p,e=Qe((i=S).formatter.get()),P(e,function(e){i.formatter.formatChanged(e,function(n){Pv(o,e,n)})}),P(["ul","ol"],function(t){i.selection.selectorChanged(t,function(n,e){Pv(o,t,n)})})}),S.on("remove",function(){p.exit()}),S.on("detach",function(){var e,n;e=p.system,n=Ce(e.element),P(n,function(n){e.getByDom(n).each(Ue)}),Be(e.element),p.system.destroy()}),{iframeContainer:p.socket.element.dom,editorContainer:p.element.dom}}}};Io.add("mobile",Gv)}();