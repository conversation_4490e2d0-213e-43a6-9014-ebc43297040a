import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken, getWindowName } from '@/utils/auth' // get token from cookie
import getPageTitle from '@/utils/get-page-title'
import { configSetting } from '../public/config'
NProgress.configure({ showSpinner: false }) // NProgress Configuration
const whiteList = ['/login'] // no redirect whitelist
const loginInfo = getWindowName()
const { roleId, appId, loginName, saToken, loginUserName } = loginInfo
const { permissionMenu } = configSetting
router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start()

  // set page title
  document.title = getPageTitle(to.meta.title)

  // determine whether the user has logged in
  const hasToken = saToken
  let isPermission = '' // 是否使用权限登录
  let storeName = '' // 登录的用户
  if (permissionMenu) {
    if (hasToken && roleId && appId && loginName) {
      isPermission = true
    } else {
      isPermission = false
    }
    storeName = loginName
  } else {
    // 在本地调试时可以把hasToken 替换成token的值 同时把public/config->permissionMenu 置为false
    isPermission = hasToken
    storeName = '超级管理员'
  }
  if (isPermission) {
    // if (hasToken && roleId && appId && loginName) {
    if (to.path === '/login') {
      // if is logged in, redirect to the home page
      next({ path: '/' })
      NProgress.done()
    } else {
      // const hasRoles = store.getters.name
      const hasRoles = store.getters.name
      if (hasRoles) {
        if (
          to.path === '/404' &&
          from.path === '/' &&
          store.state.permission.addRoutes.length > 1
        ) {
          next({
            path: store.getters.redirectPath
          })
        } else {
          next()
        }
        // console.log(store.getters.permission_routes, to, from);
      } else {
        try {
          await store.dispatch('user/getInfo', storeName)
          // get user info
          const accessRoutes = await store.dispatch('permission/generateRoutes', {
            roleId,
            appId
          })
          router.addRoutes(accessRoutes)
          next({
            ...to,
            replace: true
          })
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    /* has no token*/

    if (whiteList.indexOf(to.path) !== -1) {
      // in the free login whitelist, go directly
      next()
    } else {
      // other pages that do not have permission to access are redirected to the login page.
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})
