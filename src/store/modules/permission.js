import { asyncRoutes, constantRoutes, formatRoute } from '@/router'
import { appsMenuList } from '@/api/menu'
// import { getAppId, getRoleId } from '@/utils/auth'
import { MessageBox } from 'element-ui'
import { configSetting } from '../../../public/config'
import waterMark from '@/utils/waterMark'
import { getLoginName, getLoginUserName } from '@/utils/auth'
const { permissionMenu } = configSetting

/**
 * Filter asynchronous routing tables by recursion
 * @param {*} routes
 * @param {*} roles
 * @returns
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []
  routes.forEach(route => {
    const tmp = { ...route }
    if (tmp.children) {
      tmp.children = filterAsyncRoutes(tmp.children, roles)
    }
    res.push(tmp)
  })
  return res
}

const state = {
  routes: [],
  addRoutes: [],
  redirectPath: '',
  systemName: '电子健康卡管理系统',
  appLogo: ''
}
const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  SET_REDIRECTPATH: (state, redirectPath) => {
    state.redirectPath = redirectPath
  },
  SET_APPNAME: (state, systemName) => {
    state.systemName = systemName
  },
  SET_APPLOGO: (state, logo) => {
    state.appLogo = logo
  },
  SET_APPMARK: (state, mark) => {}
}
const actions = {
  generateRoutes({ commit }, { roleId, appId }) {
    return new Promise(resolve => {
      if (permissionMenu) {
        appsMenuList({
          roleId: roleId,
          appId: appId
        }).then(res => {
          let accessedRoutes
          const { menuList, logo, systemName } = res.data
          commit('SET_APPNAME', systemName)
          commit('SET_APPLOGO', logo)
          if (menuList && menuList.length) {
            accessedRoutes = asyncRoutes || []
            // let newRoutes = formatRoutes(recursion(menuList), accessedRoutes)
            // console.log( formatRoute(recursion(menuList)));
            // TODO 此处为动态功能菜单
            let newRoutes = formatRoute(recursion(menuList))
            newRoutes.push({ path: '*', redirect: '/404', hidden: true })
            commit('SET_ROUTES', newRoutes)
            commit('SET_REDIRECTPATH', newRoutes[0].redirect)
            // 设置水印
            setWmk(res.data.wmk)
            resolve(newRoutes)
          } else {
            MessageBox.confirm(
              '您所登录的用户暂没有权限,请关闭当前页面返回至门户',
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            )
              .then(() => {})
              .catch(() => {})
          }
        })
      } else {
        commit('SET_ROUTES', asyncRoutes)
        commit('SET_REDIRECTPATH', asyncRoutes[0].redirect)
        resolve(asyncRoutes)
      }
    })
  },
  setWmk({ commit }, data) {
    setWmk(data)
  }
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}

export function recursion(arr) {
  let newItem = {}
  if (arr !== undefined) {
    let a = arr.map(item => {
      newItem = {
        type: item.type,
        parentId: item.parentId,
        // id: item.menuId,
        label: item.menuName,
        name: item.url,
        requriePath: item.url,
        children: [],
        hidden: item.hidden === 'true' ? true : false,
        // TODO 此处为动态功能菜单
        path: '/' + item.url,
        meta: {
          title: item.menuName,
          icon: item.icon
        }
      }
      if (item.children !== null || item.children !== undefined) {
        newItem.children = recursion(item.children)
      }
      return newItem
    })
    return a
  }
}
export function formatRoutes(resRoutes, asyncRoutes) {
  const res = []
  resRoutes.forEach(item => {
    asyncRoutes.forEach(el => {
      if (item.name === el.name) {
        res.push({
          ...el,
          meta: {
            ...el.meta,
            title: item.label,
            icon: item.icon
          },
          children: formatRoutes(item.children, el.children).length
            ? formatRoutes(item.children, el.children)
            : el.children
        })
      }
    })
  })
  return res
}
/**
   * 
   * @param {boolean} flag 
   * @param {Object} data 
   * @data
   * {
   *  isOpenWmk:true // 是否开启
   *  content: '', // 内容
      wordSize: '', // 字体大小
      density: '', // 密度
      color: '', // 密度
      rotate: '' // 旋转角度
  * }
  */
export function setWmk({ isOpenWmk, content, wordSize, density, color, rotate }) {
  if (isOpenWmk) {
    if (!content) content = getLoginUserName() + '/' + getLoginName()
    waterMark.set({
      firstLine: content,
      secondLine: new Date().toLocaleDateString(),
      font: wordSize,
      fillStyle: color,
      rotate: rotate
    })
  } else {
    waterMark.remove()
  }
}
