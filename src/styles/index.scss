@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei,
    Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px;
}

.home-container {
  position: relative;
  // height: calc(100vh - 50px);
  background: #f0f6fc;
  padding: 15px;

  .el-button--primary {
    background: #1273ce;
    color: #fff;
    border-radius: 2px;
    border-color: #1273ce;
  }

  .el-button--primary.is-plain {
    color: #409eff;
    background: #ecf5ff;
    border-color: #b3d8ff;
  }

  .el-button--default {
    background: #fff;
    color: #303133;
    border-radius: 2px;
    border-color: #d6e5fb;
  }

  .el-button--custom {
    background: #fff;
    color: #1273ce;
    border-radius: 2px;
    border-color: #1273ce;
  }
}

.el-tree-node__expand-icon {
  &::before {
    color: #192958;
    font-size: 14px;
  }
}
.el-tree-node__expand-icon.is-leaf {
  color: transparent;
  &::before {
    display: none;
  }
}

.el-tree {
  color: #303133;
  font-size: 14px;
  .el-tree-node__content {
    .is-checked + .el-tree-node__label {
      font-weight: 500;
    }
  }
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background: #1273ce;
  border-color: #1273ce;
}

.el-dialog {
  .el-dialog__header {
    .el-dialog__title {
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }
  .el-dialog__body {
    .el-form {
      .el-input {
        width: 240px;
      }
    }
  }
}

.form-col {
  .el-form-item__label {
    font-size: 14px;
    color: #313133;
    font-weight: 400;
  }
}

.el-pagination {
  .el-pager {
    .active {
      background: #1273ce !important;
    }
  }
}

.scroll {
  height: 100%;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
    height: 0px;
    margin-right: 2px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 6px 0 0px #9ec1e3;
    background: rgba(199, 201, 204, 0.8);
  }
}
.el-table {
  color: #313133;
  font-size: 14px;
}
