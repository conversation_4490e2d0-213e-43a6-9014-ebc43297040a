<template>
  <div class="home-echart">
    <div ref="chart" class="chart-item"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'InforCard',
  components: {},
  props: {
    name: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: []
    },
    type: {
      type: String,
      default: ''
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 初始化
    init() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(this.$refs.chart)
      var option = {
        title: {
          text: this.name
        },
        tooltip: {},
        xAxis: {
          data: this.data.map(item => {
            return item.name
          })
        },
        yAxis: {},
        series: [
          {
            name: '',
            type: this.type,
            data: this.data.map(item => {
              return item.count
            })
          }
        ]
      }
      // 绘制图表
      myChart.setOption(option)
    }
  }
}
</script>

<style lang="scss">
.home-echart {
  position: relative;
  width: 100%;
  height: 100%;
  .chart-item {
    position: relative;
    width: 100%;
    height: 100%;
  }
}
</style>
