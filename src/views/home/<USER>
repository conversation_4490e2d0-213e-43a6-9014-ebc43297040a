<template>
  <div class="home-container">
    <el-row :gutter="20">
      <el-col :span="8" v-for="(info, i) in inforCardData" :key="`infor-${i}`">
        <div class="info-card-wrap"><InfoCard :info="info" /></div>
      </el-col>
    </el-row>
    <div class="charts" v-if="showCharts">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <div class="echart-wrap">
              <Echart :data="trends" name="申领趋势" type="line" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <div class="echart-wrap">
              <Echart :data="ageGroup" name="申领年龄段统计" type="bar" />
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-card>
            <div class="echart-wrap">
              <Echart :data="agencyGroup" name="申领机构排行（TOP6）" type="bar" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card>
            <div class="echart-wrap">
              <Echart :data="regionGroup" name="申领地区统计" type="bar" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import InfoCard from './components/InfoCard'
import Echart from './components/Echart'
import { statistics } from '@/api/home'
export default {
  components: { InfoCard, Echart },
  data() {
    return {
      inforCardData: [
        {
          title: '总计申领人次',
          name: 'total',
          count: 0,
          color: '#436189'
        },
        {
          title: '本月申领人次',
          name: 'month',
          count: 0,
          color: '#703071'
        },
        {
          title: '本日申领人次',
          name: 'today',
          count: 0,
          color: '#cb2b2b'
        }
      ],
      trends: [],
      ageGroup: [],
      agencyGroup: [],
      regionGroup: [],
      showCharts: true, // 控制数据请求完成后展示echarts
      loading: false
    }
  },
  mounted() {
    this.query()
  },
  methods: {
    // 查询
    query() {
      this.showCharts = false
      statistics()
        .then(res => {
          this.formatData(res.data)
          this.showCharts = true
          this.loading = false
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 格式化数据
    formatData(data) {
      // 格式化信息卡片
      this.inforCardData.map(item => {
        for (let key in data) {
          if (item.name === key) {
            item.count = data[key]
          }
        }
      })
      // 格式化图表
      this.trends = data.trends
      this.ageGroup = data.ageGroup
      this.agencyGroup = data.agencyGroup
      this.regionGroup = data.regionGroup
    }
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  .info-card-wrap {
    padding: 30px;
    box-sizing: border-box;
  }
  .echart-wrap {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    height: 400px;
  }
}
</style>
