<template>
  <div class="info-card-wrapper" :style="BorderStyle">
    <div class="content-con">
      <div class="left-area">
        <div class="left-name">{{ info.title }}</div>
        <div class="left-count">{{ info.count }}</div>
      </div>
      <div class="right-area">
        <div class="wave-wrap">
          <!-- Bars -->
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
          <div class="wave-item"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InforCard',
  components: {},
  props: {
    info: {
      type: Object,
      default: {}
    }
  },
  computed: {
    BorderStyle() {
      return {
        'border-left': `8px solid ${this.info.color}`,
        'color': `${this.info.color}`
      }
    }
  }
}
</script>

<style lang="scss">
.info-card-wrapper {
  position: relative;
  border-radius: 8px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-shadow: 10px 10px 30px #ccc;
  .content-con {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    .left-area {
      height: 100%;
      width: 60%;
      padding: 20px;
      box-sizing: border-box;
      .left-name {
        text-align: center;
        color: #000;
        font-size: 16px;
      }
      .left-count {
        margin-top: 16px;
        text-align: center;
        font-size: 56px;
        font-weight: bold;
      }
    }
    .right-area {
      height: 100%;
      width: 40%;
      padding: 0 20px 0 0;
      box-sizing: border-box;
      overflow: hidden;
      .wave-wrap {
        position: relative;
        display: block;
        width: 100%;
        height: 0;
        padding-top: 100%;
        overflow: hidden;
      }
      .wave-item {
        margin-top: -17%;
        height: 34%;
        width: 3%;
        top: 50%;
        position: absolute;
      }
      .wave-item:nth-of-type(1) {
        animation: wave 17s 0s linear infinite;
      }
      .wave-item:nth-of-type(2) {
        animation: wave 17s -16.227s linear infinite;
      }
      .wave-item:nth-of-type(3) {
        animation: wave 17s -15.455s linear infinite;
      }
      .wave-item:nth-of-type(4) {
        animation: wave 17s -14.682s linear infinite;
      }
      .wave-item:nth-of-type(5) {
        animation: wave 17s -13.909s linear infinite;
      }
      .wave-item:nth-of-type(6) {
        animation: wave 17s -13.136s linear infinite;
      }
      .wave-item:nth-of-type(7) {
        animation: wave 17s -12.364s linear infinite;
      }
      .wave-item:nth-of-type(8) {
        animation: wave 17s -11.591s linear infinite;
      }
      .wave-item:nth-of-type(9) {
        animation: wave 17s -10.818s linear infinite;
      }
      .wave-item:nth-of-type(10) {
        animation: wave 17s -10.045s linear infinite;
      }
      .wave-item:nth-of-type(11) {
        animation: wave 17s -9.273s linear infinite;
      }
      .wave-item:nth-of-type(12) {
        animation: wave 17s -8.5s linear infinite;
      }
      .wave-item:nth-of-type(13) {
        animation: wave 17s -7.727s linear infinite;
      }
      .wave-item:nth-of-type(14) {
        animation: wave 17s -6.955s linear infinite;
      }
      .wave-item:nth-of-type(15) {
        animation: wave 17s -6.182s linear infinite;
      }
      .wave-item:nth-of-type(16) {
        animation: wave 17s -5.409s linear infinite;
      }
      .wave-item:nth-of-type(17) {
        animation: wave 17s -4.636s linear infinite;
      }
      .wave-item:nth-of-type(18) {
        animation: wave 17s -3.864s linear infinite;
      }
      .wave-item:nth-of-type(19) {
        animation: wave 17s -3.091s linear infinite;
      }
      .wave-item:nth-of-type(20) {
        animation: wave 17s -2.318s linear infinite;
      }
      .wave-item:nth-of-type(21) {
        animation: wave 17s -1.545s linear infinite;
      }
      .wave-item:nth-of-type(22) {
        animation: wave 17s -0.773s linear infinite;
      }

      @keyframes wave {
        0% {
          left: -2%;
          background: #3b44d1;
        }
        5% {
          background: #3b44d1;
        }
        10% {
          height: 10%;
          margin-top: -5%;
          background: #3b44d1;
        }
        15% {
          background: #f639f8;
        }
        20% {
          height: 34%;
          margin-top: -17%;
          background: #f639f8;
        }
        25% {
          background: #f639f8;
        }
        30% {
          height: 10%;
          margin-top: -5%;
          background: #f639f8;
        }
        35% {
          background: #dc5245;
        }
        40% {
          height: 34%;
          margin-top: -17%;
          background: #dc5245;
        }
        45% {
          background: #dc5245;
        }
        50% {
          height: 10%;
          margin-top: -5%;
          background: #dc5245;
        }
        55% {
          background: #54e67b;
        }
        60% {
          height: 34%;
          margin-top: -17%;
          background: #54e67b;
        }
        65% {
          background: #54e67b;
        }
        70% {
          height: 10%;
          margin-top: -5%;
          background: #54e67b;
        }
        75% {
          background: #3db3f3;
        }
        80% {
          height: 34%;
          margin-top: -17%;
          background: #3db3f3;
        }
        85% {
          background: #3db3f3;
        }
        90% {
          height: 10%;
          margin-top: -5%;
          background: #3db3f3;
        }
        95% {
          background: #3db3f3;
        }
        100% {
          height: 34%;
          margin-top: -17%;
          left: 100%;
          background: #3b44d1;
        }
      }
    }
  }
}
</style>
