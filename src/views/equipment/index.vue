<template>
  <div class="home-container apps-manage">
    <div class="form-col">
      
      <el-form size="small" :model="searchForm" inline>
        <el-form-item label="设备类别"  prop="equipmentTypeId">
          <el-select v-model="searchForm.equipmentTypeId" clearable placeholder="请选择设备类别">
            <el-option
              v-for="item in equipmentTypes"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="购置日期" prop="visitTime">
          <el-date-picker
            v-model="searchForm.visitTime"
            type="daterange"
            range-separator="至"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
            <!-- <el-date-picker
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              v-model="searchForm.purchaseDate"
              style="width: 100%"
            ></el-date-picker> -->
        </el-form-item>
        <el-form-item label="使用状态"  prop="status">
            <el-select v-model="searchForm.status" clearable placeholder="请选择活动区域">
              <el-option label="正常使用" :value="0"></el-option>
              <el-option label="维修中" :value="1"></el-option>
              <el-option label="闲置" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资产归属">
            <el-input v-model="searchForm.assetDepartment" placeholder="请输入资产归属" clearable />
          </el-form-item>

        <el-form-item label="设备名称">
          <el-input v-model="searchForm.name" placeholder="请输入关键字查找" clearable />
        </el-form-item>
        <el-form-item label="设备编号">
          <el-input v-model="searchForm.code" placeholder="请输入关键字查找" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="query">查 询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <div class="text">设备信息</div>
        <div>
          <el-button class="el-button--custom" size="small" @click="exportMb">
            模版导出
          </el-button>
          <el-button class="el-button--custom" size="small" @click="UploadMb">
            模版上传
          </el-button>
          <el-button class="el-button--custom" size="small" @click="exportTable">
            导 出
          </el-button>
          <el-button type="primary" plain size="small" @click="add">新增</el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        border
        :header-cell-style="{
          'background': '#F2F4F7',
          'color': '#303133',
          'font-weight': 600
        }"
        tooltip-effect="dark"
        width="100%"
        highlight-current-row
      >
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column label="设备名称" prop="name" />
        <el-table-column label="设备编号" prop="code" />
        <el-table-column label="设备型号" prop="equipmentModel" />
        <el-table-column label="设备类别" prop="equipmentTypeName" />
        <el-table-column label="生产厂商" prop="manufacturer" />
        <el-table-column label="购置日期" prop="purchaseDate" />
        <el-table-column label="资产归属" prop="assetDepartment" />
        <el-table-column label="使用状态" prop="status">
          <template slot-scope="props">
            <span v-if="props.row.status==0">正常使用</span>
            <span v-if="props.row.status==1">维修中</span>
            <span v-if="props.row.status==2">闲置</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="props">
            <span class="blue" @click="remove(props.row)">删除</span>
            <span class="blue" @click="edit(props.row)">编辑</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageNum"
          background
          size="small"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      :title="isEdit ? '编辑设备信息' : '新增设备信息'"
      :visible.sync="isShowDialog"
      width="1080px"
      :before-close="cancel"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div class="edit-form-wrap">
        <el-form ref="form" :model="form" :rules="rules" label-width="150px" size="small" :inline="true">
          <div class="form-title">设备基本信息</div>
          <el-form-item label="设备名称" prop="name">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="设备编号" prop="code">
            <el-input v-model="form.code"></el-input>
          </el-form-item>
          <el-form-item label="设备型号" prop="equipmentModel">
            <el-input v-model="form.equipmentModel"></el-input>
          </el-form-item>
          <el-form-item label="生产厂家" prop="manufacturer">
            <el-input v-model="form.manufacturer"></el-input>
          </el-form-item>
          <el-form-item label="设备类别"  prop="equipmentTypeId">
            <el-select v-model="form.equipmentTypeId" placeholder="请选择设备类别">
              <el-option
                v-for="item in equipmentTypes"
                :key="item.id"
                :label="item.dictLabel"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上传设备图片">
            <el-upload
              class="avatar-uploader"
              action=""
              :show-file-list="false"
              :http-request="upLoadImage"
              accept=".png,.jpeg,.jpg"
            >
              <img v-if="imageUrl" :src="imageUrl" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <div class="tips">*上传的应用图标不能超过500kb</div>
          </el-form-item>
          <div class="form-title">采购信息</div>
          <el-form-item label="采购价格" prop="purchasePrice">
            <el-input v-model="form.purchasePrice"></el-input>
          </el-form-item>
          <el-form-item label="供应商" prop="supplier">
            <el-input v-model="form.supplier"></el-input>
          </el-form-item>
          <el-form-item label="合同编号" prop="contractNumber">
            <el-input v-model="form.contractNumber"></el-input>
          </el-form-item>
          <el-form-item label="购置日期" prop="purchaseDate">
            <el-date-picker
              type="date"
              placeholder="选择日期"
              v-model="form.purchaseDate"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="使用状态"  prop="status">
            <el-select v-model="form.status" placeholder="请选择活动区域">
              <el-option label="正常使用" :value="0"></el-option>
              <el-option label="维修中" :value="1"></el-option>
              <el-option label="闲置" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="资产归属" prop="assetDepartment">
            <el-input v-model="form.assetDepartment"></el-input>
          </el-form-item>
          <el-form-item label="资产责任人">
            <el-input v-model="form.assetResponsiblePerson"></el-input>
          </el-form-item>
          <el-form-item label="相关文档">
            <el-upload
              ref="upload"
              name="file"
              action=""
              :on-remove="onRemove"
              :http-request="upLoadFile"
              :file-list="fileList"
              :before-upload="beforeUpload"
              accept=".pdf, .doc, .docx, .xls, .xlsx"
            >
              <el-button type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">
                *仅限上传pdf、.docs、.xlsx 文件格式，且大小不超过5M。
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item label="折旧信息">
            <el-input type="textarea" v-model="form.scrapInfo"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit" size="small">提 交</el-button>
        <el-button @click="cancel" size="small">重 置</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="模版上传"
      :visible.sync="showDialogMb"
      width="20%"
      class="el-dialog1"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="fileList1=[]"
    >
     <el-upload
        class="upload-demo"
        drag
        :auto-upload="false"
        accept=".doc, .docx, .xls, .xlsx, .csv"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :on-change="handleChange"
        action="action"
        :limit="1"
        :file-list="fileList1">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传模版文件</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="small">取 消</el-button>
        <el-button @click="handleUpload" size="small" type="primary">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken,getWindowName } from '@/utils/auth'
const loginInfo = getWindowName()
const { appId } = loginInfo
import axios from 'axios'
import moment from 'moment'
import {
  equipmentList,
  equipmentSaveOrUpdate,
  equipmentDetail,
  equipmentDelete
} from '@/api/equipment'
import { fileUpload, getAllDataList } from '@/api/common'
export default {
  data() {
    return {
      loading: false,
      isShowDialog: false,
      isEdit: false, // 是否编辑
      tableData: [],
      total: 0,
      pageSize: 10,
      pageNum: 1,
      fileList: [], // 文件列表
      deleteIds: [], // 删除的文件列表
      imageUrl: '', // 设备图片
      searchForm: {
        name: '',
        code: '',
        equipmentTypeId: '', // 设备类别
        visitTime: ['', ''], // 购置日期
        status: '', // 使用状态
        assetDepartment: '', // 资产归属
      },
      rules: {
        name: [
          { required: true, message: "请输入设备名称", trigger: "blur" },
        ],
        code: [
          { required: true, message: "请输入设备编号", trigger: "blur" },
        ],
        equipmentModel: [
          { required: true, message: "请输入设备型号", trigger: "blur" },
        ],
        manufacturer: [
          { required: true, message: "请输入生产厂家", trigger: "blur" },
        ],
        equipmentTypeId: [
          { required: true, message: "请选择设备类别", trigger: "change" },
        ],
        purchasePrice: [
          { required: true, message: "请输入采购价格", trigger: "blur" },
        ],
        supplier: [
          { required: true, message: "请输入供应商", trigger: "blur" },
        ],
        contractNumber: [
          { required: true, message: "请输入合同编号", trigger: "blur" },
        ],
        purchaseDate: [
          { required: true, message: "请选择购置日期", trigger: "change" },
        ],
        status: [
          { required: true, message: "请选择使用状态", trigger: "change" },
        ],
        assetDepartment: [
          { required: true, message: "请输入资产归属", trigger: "blur" },
        ],
        assetResponsiblePerson: [
          { required: true, message: "请输入资产责任人", trigger: "blur" },
        ],
      },
      form: {
        id: '',
        deleteIds: [],
        name: '',
        code: '',
        equipmentModel: '',
        manufacturer: '',
        equipmentTypeId: '',
        purchasePrice: '',
        supplier: '',
        contractNumber: '',
        purchaseDate: '',
        status: '',
        assetDepartment: '',
        assetResponsiblePerson: '',
        scrapInfo: ''
      },
      url: '',
      showDialogMb: false,
      fileList1: [],
      equipmentTypes: [] // 设备类型下拉列表
    }
  },
  mounted() {
    if (process.env.NODE_ENV == 'development') {
      this.url = '/medical'
    } else {
      this.url = '/medical'
    }
    // this.axiosTable()
  },
  created() {
    this.init()
  },
  methods: {
    UploadMb(){
      this.showDialogMb = true
    },
    handleUpload() {
      console.log(this.fileList1)
      if (this.fileList1.length == 0) {
        this.$message({
          message: '上传文件不能为空！',
          type: 'warning'
        });
      } else {
        let url = this.url + "/equipment/importExcel"
        let formDate = new FormData()
        formDate.append('file',this.fileList1[0].raw)
        axios.post(url,formDate,{
            headers: {
              'saToken': getToken(), // 设置请求头，确保服务器正确解析 FormData
              'appId': appId
            }}).then((res)=>{
          console.log(res)
          if (res.data.code == 200) {
            this.$message({
              message: '上传成功',
              type: 'success'
            });
            this.fileList1 = []
            this.query()
            this.showDialogMb = false
          } else {
            this.$message({
              message: res.data.data,
              type: 'error'
            });
          }
        })
      }
    },
        /**
     * data: 下载文件
     * fileName: 文件名
     * type: 下载文件类型
     */
    downloadHandler(data, fileName, type) {
        // 匹配任意文件类型：type : "application/octet-stream"
        const blob = new Blob([data],  { type: type });
        console.log(blob)
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = fileName;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
    },
    exportMb() {
      let url = this.url + "/equipment/download/template"
      axios.post(url,{},{ responseType: 'arraybuffer',
            headers: {
              'saToken': getToken(), // 设置请求头，确保服务器正确解析 FormData
              'appId': appId
            }}).then((res)=>{

        if (res.status == 200) {
          this.$message({
            message: '模版下载成功！',
            type: 'success'
          });
        }
        this.downloadHandler(res.data,'模版.xlsx','application/vnd.ms-excel')
      })
    },
    handleChange(file, fileList) {
      this.fileList1 = fileList
      console.log(fileList)
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    // 新增编辑节点弹窗关闭
    handleClose() {
      this.showDialogMb = false
    },
    init() {
      this.query()
      this.getEquipmentType()
    },
    // 查询
    query() {
      this.pageNum = 1
      this.axiosTable()
    },
    // 表格默认请求
    axiosTable() {
      this.loading = true
      const data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        code: this.searchForm.code,
        name: this.searchForm.name,
        equipmentTypeId: this.searchForm.equipmentTypeId,
        // purchaseDate: this.searchForm.purchaseDate,
        status: this.searchForm.status,
        assetDepartment: this.searchForm.assetDepartment
      }
      this.searchForm.visitTime
        ? this.searchForm.visitTime
        : (this.searchForm.visitTime = ['', ''])
      data.purchaseStartDate = this.searchForm.visitTime[0]
      data.purchaseEndDate = this.searchForm.visitTime[1]
      equipmentList(data)
        .then(res => {
          this.tableData = res.data.list
          this.loading = false
          this.total = res.data.total
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 新增
    add() {
      this.form = this.$options.data().form
      this.imageUrl = ''
      this.fileList = []
      this.isEdit = false
      this.isShowDialog = true
    },
    // sizeChange
    handleSizeChange(size) {
      this.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.pageNum = page
      this.axiosTable(page)
    },
    // 编辑
    edit(row) {
      equipmentDetail({ id: row.id })
        .then(res => {
          this.form.id = res.data.id
          this.form.name = res.data.name
          this.form.code = res.data.code
          this.form.equipmentModel = res.data.equipmentModel
          this.form.manufacturer = res.data.manufacturer
          this.form.equipmentTypeId = res.data.equipmentTypeId
          this.form.purchasePrice = res.data.purchasePrice
          this.form.supplier = res.data.supplier
          this.form.contractNumber = res.data.contractNumber
          this.form.purchaseDate = res.data.purchaseDate
          this.form.status = res.data.status
          this.form.assetDepartment = res.data.assetDepartment
          this.form.assetResponsiblePerson = res.data.assetResponsiblePerson
          this.form.scrapInfo = res.data.scrapInfo
          this.imageUrl = res.data.equipmentImage
          this.fileList = res.data.annex ? res.data.annex.map(item => {
            const data = {
              id: item.id,
              name: item.fileName,
              url: item.filePath,
              type: item.fileType
            }
            return data
          }) : []
          this.isEdit = true
          this.isShowDialog = true
        })
        .catch(e => {})
    },
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          equipmentDelete({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 导出
    exportTable() {
      let arr = JSON.parse(JSON.stringify(this.tableData))
      arr.forEach((item)=>{
        if (item.status == 1) {
          item.statusName = '是'
        } else {
          item.statusName = '否'
        }
      })
      const header = ['name', 'code', 'equipmentModel', 'equipmentTypeName', 'supplier', 'purchaseDate' ,'statusName']
      const data = this.formatJson(header, arr)
      import('@/utils/export2excel').then(excel => {
        excel.export_json_to_excel({
          header: ['设备名称', '设备编号', '设备型号', '设备类别', '生产厂商', '购置日期', '使用状态'],
          data,
          filename: '设备信息',
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    // 弹窗提交
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const data = {
            id: this.form.id,
            name: this.form.name,
            code: this.form.code,
            equipmentModel: this.form.equipmentModel,
            manufacturer: this.form.manufacturer,
            equipmentTypeId: this.form.equipmentTypeId,
            purchasePrice: this.form.purchasePrice,
            supplier: this.form.supplier,
            contractNumber: this.form.contractNumber,
            purchaseDate: moment(this.form.purchaseDate).format('YYYY-MM-DD'),
            status: this.form.status,
            assetDepartment: this.form.assetDepartment,
            assetResponsiblePerson: this.form.assetResponsiblePerson,
            scrapInfo: this.form.scrapInfo,
            deleteIds: this.form.deleteIds,
            addAnnex: this.fileList.map(item => {
              const obj = {
                id: Number(item.id),
                fileType: item.type,
                fileName: item.name,
                filePath: item.url
              }
              return obj
            }),
            equipmentImage: this.imageUrl
          }
          equipmentSaveOrUpdate(data)
            .then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.query()
                this.isShowDialog = false
                this.isEdit = false
                this.reset()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch(() => {})
        }
      })
    },
    // 弹框取消
    cancel() {
      this.isShowDialog = false
    },
    // 自定义上传图片
    upLoadImage(file) {
      if (file.file.size > 500 * 1024) {
        this.$message.warning('上传图片大小不能超过500kb!')
      } else {
        const formData = new FormData()
        formData.append('file', file.file)
        fileUpload(formData).then(res => {
          this.imageUrl = res.data.filePath
        })
      }
    },
    upLoadFile(file) {
      const formData = new FormData()
      formData.append('file', file.file)
      fileUpload(formData).then(res => {
        if (res.code === 200) {
          const data = {
            id: res.data.id,
            name: res.data.fileName,
            url: res.data.filePath,
            type: res.data.fileType
          }
          this.fileList.push(data)
        }
      })
    },
    // 移除上传附件
    onRemove(file) {
      this.fileList.splice(
        this.fileList.findIndex(el => el.uid === file.uid),
        1
      )
      this.form.deleteIds.push(file.id)
    },
    // 文件上传拦截
    beforeUpload(file) {
      if (file.size > 1024 * 1024 * 5) {
        this.$message.warning('文件大小不能超过5M')
        return false
      }
      return true
    },
    // 对需要导出的数据做处理
    formatJson(header, data) {
      return data.map(v => {
        if (v.appTag === 'BS') {
          v.appTag = '网页'
        } else {
          v.appTag = '客户端'
        }
        if (v.appSign === '1') {
          v.appSign = '平台应用'
        } else {
          v.appSign = '第三方应用'
        }
        return header.map(j => {
          return v[j]
        })
      })
    },
    // 获取设备类型下拉
    getEquipmentType() {
      const data = { dictType: 'sys_equipment_type' }
      getAllDataList(data).then(res => {
        this.equipmentTypes = res.data
      })
    },
    reset() {
      this.total = 0
      this.pageSize = 10
      this.pageNum = 1
      this.form = this.$options.data().form
      this.imageUrl = ''
      this.fileList = []
    }
  }
}
</script>

<style lang="scss" scoped>
.apps-manage {
  ::v-deep .el-dialog1 .el-dialog{
      width: 400px !important;
      .pciminchen {
        .titlename {
          margin-right: 10px;
        }
        .el-select {
          width: 280px;
        }
      }
    }
  .form-col {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      // margin-bottom: 0;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        &::before {
          content: '';
          width: 4px;
          height: 14px;
          background: #1273ce;
          display: inline-block;
          margin-right: 4px;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
      img {
        height: 36px;
        width: 36px;
      }
      .blue {
        color: #1273ce;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
      .red {
        color: #ff4645;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
::v-deep .el-dialog {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
  }
  .avatar {
    width: 60px;
    height: 60px;
    display: block;
  }
  .tips {
    color: #949699;
  }
}
.edit-form-wrap {
  position: relative;
  width: 100%;
  padding: 0 20px;
}
.form-title {
  padding: 10px;
  box-sizing: border-box;
  border-left: 4px solid #1273ce;
}
.form-textarea {
  padding: 10px 30px;
}
.multiple-institutions {
  padding: 0 20px;
  .multiple-col {
    margin: 0 5px;
  }
}
</style>
