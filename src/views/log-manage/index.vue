<template>
  <div class="home-container log-manage">
    <div class="form-col">
      <el-form size="small" :model="searchForm" inline>
        <el-form-item label="用户名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入用户名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="searchForm.operateType">
            <el-option label="集成平台界面操作" value="2" />
            <el-option label="应用登录" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="工号">
          <el-input
            v-model="searchForm.userName"
            placeholder="请输入工号"
            clearable
          />
        </el-form-item>
        <el-form-item label="操作日期">
          <el-date-picker
            v-model="searchForm.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="reset">重 置</el-button>
          <el-button type="primary" @click="query">查 询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <div class="text">日志列表</div>
        <div>
          <el-button
            class="el-button--custom"
            size="small"
            @click="exportTable"
          >
            导出
          </el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        border
        :header-cell-style="{
          background: '#F2F4F7',
          color: '#303133',
          'font-weight': 600
        }"
        tooltip-effect="dark"
        width="100%"
        highlight-current-row
      >
        <!-- <el-table-column type="index" width="50" label="序号" /> -->
        <el-table-column label="工号" prop="userName" />
        <el-table-column label="用户名称" prop="name" />
        <el-table-column label="操作类型" prop="operateTypeName" />
        <el-table-column label="作用于应用" prop="usingApplication" />
        <el-table-column label="操作详情" prop="operateDetails">
          <template slot-scope="props">
            {{ props.row.operateType === '1' ? '' : props.row.operateDetails }}
          </template>
        </el-table-column>
        <el-table-column label="用户IP" prop="userIp" />
        <el-table-column label="操作日期" prop="operateDate" />
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageNum"
          background
          :page-sizes="[10, 20, 30, 40]"
          size="small"
          :page-size="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { logQuery } from '@/api/log-manage'
export default {
  data() {
    return {
      searchForm: {
        name: '',
        operateType: '',
        userName: '',
        time: ['', ''],
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      loading: false,
      tableData: []
    }
  },
  mounted() {
    this.axiosTable(1)
  },
  methods: {
    // 重置
    reset() {
      this.searchForm = this.$options.data().searchForm
      this.axiosTable(1)
    },
    // 查询
    query() {
      this.searchForm.pageNum = 1
      this.axiosTable(1)
    },
    // 表格默认请求
    axiosTable(page = 1) {
      this.loading = true
      if (!this.searchForm.time) this.searchForm.time = ['', '']
      let searchForm = {
        ...this.searchForm,
        beginTime: this.searchForm.time[0],
        endTime: this.searchForm.time[1],
        pageNum: page
      }
      delete searchForm.time
      logQuery(searchForm)
        .then((res) => {
          this.loading = false
          this.tableData = res.data
          this.total = res.total
        })
        .catch((e) => {
          this.loading = false
        })
    },
    // sizeChange
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.searchForm.pageNum = page
      this.axiosTable(page)
    },

    // 导出
    exportTable() {
      const header = [
        'userName',
        'name',
        'operateTypeName',
        'usingApplication',
        'operateDetails',
        'userIp',
        'operateDate'
      ]
      const data = this.formatJson(header, this.tableData)
      import('@/utils/export2excel').then((excel) => {
        excel.export_json_to_excel({
          header: [
            '工号',
            '用户名称',
            '操作类型',
            '作用于应用',
            '操作详情',
            '用户IP',
            '操作日期'
          ],
          data,
          filename: '日志列表',
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    // 对需要导出的数据做处理
    formatJson(header, data) {
      return data.map((v) =>
        header.map((j) => {
          v.operateType === '1'
            ? (v.operateDetails = '')
            : (v.operateDetails = v.operateDetails)
          return v[j]
        })
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.log-manage {
  .form-col {
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        &::before {
          content: '';
          width: 4px;
          height: 14px;
          background: #1273ce;
          display: inline-block;
          margin-right: 4px;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
</style>