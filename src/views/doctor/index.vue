<template>
  <div class="home-container apps-manage">
    <div class="form-col">
      <el-button type="primary" plain size="mini" @click="add">新增</el-button>
      <el-form size="small" :model="searchForm" inline>
        <el-form-item>
          <el-select v-model="searchForm.orgCodes" clearable filterable multiple placeholder="机构">
            <el-option
                v-for="item in orgItems"
                :key="item.code"
                :label="item.name"
                :value="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="searchForm.practiceLevelIds"
            clearable
            placeholder="专业技术资格（评）"
            multiple
          >
            <el-option
              v-for="item in practiceLevel"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.practiceTypeIds" clearable multiple placeholder="医师执业类别">
            <el-option
              v-for="item in practiceTypes"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.practiceItemId" clearable placeholder="执业范围">
            <el-option
              v-for="item in practiceItems"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item labe="搜索">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入姓名或身份证查询"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="query">查 询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <div class="text">卫生技术人员信息</div>
        <div>
          <el-button class="el-button--custom" size="small" @click="exportMb">
          模版导出
        </el-button>
        <el-button class="el-button--custom" size="small" @click="UploadMb">
          模版上传
        </el-button>
          <el-button class="el-button--custom" size="small" @click="exportTable">
            导 出
          </el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        border
        :header-cell-style="{
          'background': '#F2F4F7',
          'color': '#303133',
          'font-weight': 600
        }"
        tooltip-effect="dark"
        width="100%"
        highlight-current-row
      >
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column label="手机号码" prop="phoneNumber" />
        <el-table-column label="姓名" prop="name" />
        <el-table-column label="性别" width="60" prop="sex">
          <template slot-scope="props">
            <span>
              {{ props.row.sex == '1' ? '男' : props.row.sex == '2' ? '女' : '未知' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="出生日期" prop="birthday" />
        <el-table-column label="执业证书编码" prop="code" />
        <el-table-column label="主要执业机构" prop="practiceOrg" />
        <el-table-column label="专业技术资格（评）" prop="practiceLevel" />
        <el-table-column label="医师执业类别" prop="practiceType" />
        <el-table-column label="执业范围" prop="practiceItem" />
        <el-table-column label="用户角色" prop="roles">
          <template slot-scope="props">
            <span>
              {{
                (props.row.roles ? props.row.roles : [])
                  .map(item => {
                    return item.roleName
                  })
                  .join(',')
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="上一次登录" prop="lastLoginTime" />
        <el-table-column label="操作" width="200">
          <template slot-scope="props">
            <span class="blue" @click="remove(props.row)">删除</span>
            <span class="blue" @click="edit(props.row)">编辑</span>
            <span class="blue" @click="passwordSetting(props.row)">重置密码</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageNum"
          background
          size="small"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      title="模版上传"
      :visible.sync="showDialogMb"
      width="20%"
      class="el-dialog1"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="fileList=[]"
    >
     <el-upload
        class="upload-demo"
        drag
        :auto-upload="false"
        accept=".doc, .docx, .xls, .xlsx, .csv"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :on-change="handleChange"
        action="action"
        :limit="1"
        :file-list="fileList">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传模版文件</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="small">取 消</el-button>
        <el-button @click="handleUpload" size="small" type="primary">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="isEdit ? '编辑医生信息' : '新增医生信息'"
      :visible.sync="isShowDialog"
      width="1080px"
      :before-close="cancel"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div class="edit-form-wrap">
        <el-form :model="doctorInfo" ref="ruleForm" label-width="150px" :rules="rules" size="small" :inline="true">
          <div class="form-title">基本信息</div>
          <el-form-item label="姓名" prop="name">
            <el-input v-model="doctorInfo.name"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-select v-model="doctorInfo.sex" placeholder="请选择性别">
              <el-option label="男" :value="1"></el-option>
              <el-option label="女" :value="2"></el-option>
              <el-option label="未知" :value="9"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="身份证号" prop="identityNo">
            <el-input v-model="doctorInfo.identityNo"></el-input>
          </el-form-item>
          <el-form-item label="出生日期" prop="birthday">
            <el-date-picker
              v-model="doctorInfo.birthday"
              type="date"
              placeholder="选择日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="民族" prop="ethnicity">
            <el-input v-model="doctorInfo.ethnicity"></el-input>
          </el-form-item>
          <el-form-item label="电话号码" prop="phoneNumber">
            <el-input
              v-model="doctorInfo.phoneNumber"
              prop="doctorInfoPhoneNumber"
            ></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="doctorInfo.email"></el-input>
          </el-form-item>
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="doctorInfo.userName" :disabled="isEdit"></el-input>
          </el-form-item>
          <el-form-item label="机构名称" prop="orgCode">
            <el-select
              v-model="doctorInfo.orgCode"
              clearable
              filterable
              placeholder="请选择机构"
              prop="doctorInfoOrgCode"
            >
              <el-option
                v-for="item in orgItems"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="角色" prop="roles">
            <el-select
              v-model="doctorInfo.roles"
              clearable
              multiple
              placeholder="请选择角色"
            >
              <el-option
                v-for="item in currentRoleList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否专家" prop="isExpert">
            <el-select v-model="doctorInfo.isExpert" placeholder="请选择是否专家" @change="setDepts(doctorInfo.isExpert)">
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <div v-if="doctorInfo.isExpert==1">
            <el-form-item label="科室">
                <el-select v-model="doctorInfo.depts1" multiple clearable placeholder="选择科室">
                  <el-option
                    v-for="item in depts1"
                    :key="item.id"
                    :label="item.dictLabel"
                    :value="item.id"
                  ></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="疾病">
              <el-select v-model="doctorInfo.depts2" multiple clearable placeholder="选择疾病">
                <el-option
                  v-for="item in depts2"
                  :key="item.id"
                  :label="item.dictLabel"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="治疗手段">
              <el-select v-model="doctorInfo.depts3" multiple clearable placeholder="选择治疗手段">
                <el-option
                  v-for="item in depts3"
                  :key="item.id"
                  :label="item.dictLabel"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="地区">
              <el-select v-model="doctorInfo.depts4" multiple clearable placeholder="选择地区">
                <el-option
                  v-for="item in depts4"
                  :key="item.id"
                  :label="item.dictLabel"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
          <el-form-item label="上传照片">
            <el-upload
              class="avatar-uploader"
              action=""
              :show-file-list="false"
              :http-request="httpRequest"
              accept=".png,.jpg,.jpeg"
            >
              <div v-if="imageUrl" class="avatar">
                <img :src="imageUrl" />
                <i class="el-icon-error" @click.stop="removeUpload"></i>
              </div>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <div class="tips">*仅支持JPG，PNG格式，文件小于500Kb</div>
          </el-form-item>
        </el-form>
        <el-form
          ref="doctorEducation"
          :model="doctorEducation"
          label-width="150px"
          size="small"
          :inline="true"
        >
          <div class="form-title">教育经历</div>
          <el-form-item label="毕业院校">
            <el-input v-model="doctorEducation.school"></el-input>
          </el-form-item>
          <el-form-item label="专业">
            <el-input v-model="doctorEducation.subject"></el-input>
          </el-form-item>
          <el-form-item label="第一学历">
            <el-input v-model="doctorEducation.serviceEducation"></el-input>
          </el-form-item>
          <el-form-item label="第一学历证书编码">
            <el-input v-model="doctorEducation.serviceEducationCode"></el-input>
          </el-form-item>
          <el-form-item label="学位">
            <el-input v-model="doctorEducation.degree"></el-input>
          </el-form-item>
          <el-form-item label="学位证书编码">
            <el-input v-model="doctorEducation.degreeCode"></el-input>
          </el-form-item>
          <el-form-item label="毕业时间" prop="graduationTime" :rules="{
              required: true, message: '请选择毕业时间', trigger: 'change'
            }">
            <el-date-picker
              v-model="doctorEducation.graduationTime"
              type="date"
              placeholder="选择日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="最高学历">
            <el-input v-model="doctorEducation.incumbencyDegree"></el-input>
          </el-form-item>
          <el-form-item label="最高学历证书编码">
            <el-input v-model="doctorEducation.incumbencyDegreeCode"></el-input>
          </el-form-item>
          <el-form-item label="学分">
            <el-input v-model="doctorEducation.credits"></el-input>
          </el-form-item>
          <el-form-item label="学制" class="number-input">
            <el-input-number
              v-model="doctorEducation.learnTime"
              :min="0"
              :max="10"
            ></el-input-number>
          </el-form-item>
        </el-form>
        <el-form
          ref="doctorQualification"
          :model="doctorQualification"
          label-width="150px"
          size="small"
          :inline="true"
        >
          <div class="form-title">资格信息</div>
          <el-form-item label="资格证书编码">
            <el-input v-model="doctorQualification.code"></el-input>
          </el-form-item>
          <el-form-item label="医师类别">
            <el-input v-model="doctorQualification.type"></el-input>
          </el-form-item>
          <el-form-item label="获得资格年度">
            <el-input v-model="doctorQualification.qualificationYear"></el-input>
          </el-form-item>
          <el-form-item label="发证机关">
            <el-input v-model="doctorQualification.sendDocumentsOrg"></el-input>
          </el-form-item>
          <el-form-item label="签发日期" prop="issueDate"
            :rules="{
              required: true, message: '请选择签发日期', trigger: 'change'
            }"
          >
            <el-date-picker
              v-model="doctorQualification.issueDate"
              type="date"
              placeholder="选择日期"
            ></el-date-picker>
          </el-form-item>
        </el-form>
        <el-form
          ref="doctorPracticepoint"
          :model="doctorPracticepoint"
          :rules="rules1"
          label-width="150px"
          size="small"
          :inline="true"
        >
          <div class="form-title">职业信息</div>
          <el-form-item label="执业证书编码">
            <el-input v-model="doctorPracticepoint.code"></el-input>
          </el-form-item>
          <el-form-item label="专业技术资格（评）">
            <el-select
              v-model="doctorPracticepoint.practiceLevelId"
              placeholder="请选择专业技术资格（评）"
            >
              <el-option
                v-for="item in practiceLevel"
                :key="item.id"
                :label="item.dictLabel"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="院内科室" prop="practiceDept">
            <el-input v-model="doctorPracticepoint.practiceDept"></el-input>
          </el-form-item>
          <el-form-item label="医师执业类别">
            <el-select
              v-model="doctorPracticepoint.practiceTypeId"
              placeholder="请选择医师执业类别"
            >
              <el-option
                v-for="item in practiceTypes"
                :key="item.id"
                :label="item.dictLabel"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="执业范围">
            <el-select
              v-model="doctorPracticepoint.practiceItemIds"
              placeholder="请选择执业范围"
              multiple
              @change="handleChange1"
            >
              <el-option
                v-for="item in practiceItems"
                :key="item.id"
                :label="item.dictLabel"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="主要职业机构" prop="practiceOrg">
            <el-input v-model="doctorPracticepoint.practiceOrg"></el-input>
          </el-form-item>
          <el-form-item label="职业机构地址">
            <el-input v-model="doctorPracticepoint.practiceOrgAddress"></el-input>
          </el-form-item>
          <el-form-item label="是否有效" prop="enabled">
            <el-select v-model="doctorPracticepoint.enabled" placeholder="请选是否有效">
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="职称信息">
            <el-input v-model="doctorPracticepoint.titleInformation"></el-input>
          </el-form-item>
          <el-form-item label="审批机构">
            <el-input v-model="doctorPracticepoint.checkOrg"></el-input>
          </el-form-item>
          <el-form-item label="审批日期" prop="checkDate">
            <el-date-picker
              v-model="doctorPracticepoint.checkDate"
              type="date"
              placeholder="选择日期"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="从事职业类别">
            <el-select
              v-model="doctorPracticepoint.practiceTitleId"
              placeholder="请选择从事职业类别"
            >
              <el-option
                v-for="item in qualifications"
                :key="item.id"
                :label="item.dictLabel"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否全科医生" prop="isGeneralPractitioner">
            <el-select
              v-model="doctorPracticepoint.isGeneralPractitioner"
              placeholder="请选择是否全科医生"
            >
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <!-- <el-form label-width="150px" size="small" :inline="true" > -->
          <div class="form-title">
            <span style="margin-right: 20px">多机构备案信息</span>
            <el-button size="mini" type="primary" @click="addPracticepointItems">
              增加
            </el-button>
          </div>
          <div
            class="multiple-institutions"
            v-for="(item, index) in doctorPracticepointItems" :key="index"
          >
          <el-form label-width="150px" size="small" :inline="true" :model="item" ref="doctorPracticepointItems">
              <el-form-item label=" " prop="name" :rules="{
                  required: true, message: '请输入机构名称', trigger: 'blur'
                }">
                  <el-input v-model="item.name"  placeholder="机构名称" size="small"></el-input>
              </el-form-item>
              <el-form-item label=" " prop="date" :rules="{
                  required: true, message: '请选择日期', trigger: 'change'
                }">
                <el-date-picker
                  v-model="item.date"
                  type="daterange"
                  size="small"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
              <span class="multiple-col">
                <el-input v-model="item.remarkOrg" placeholder="备案机构" size="small"></el-input>
              </span>
              <span class="multiple-col">
                <el-button
                  size="small"
                  icon="el-icon-delete"
                  @click="delPracticepointItems(item, index)"
                ></el-button>
              </span>
          </el-form>
          </div>
          <div class="form-title">
            <span style="margin-right: 20px">继续教育</span>
            <el-button size="mini" type="primary" @click="addContinuousEducation">
              增加
            </el-button>
          </div>
          <div
            class="multiple-institutions"
            v-for="(item, index) in continuousEducationList" :key="'edu-' + index"
          >
            <el-form label-width="150px" size="small" :inline="true" :model="item" ref="continuousEducationList">
              <el-form-item label=" " prop="date" :rules="{
                  required: false, message: '请选择日期', trigger: 'change'
                }">
                <el-date-picker
                  v-model="item.date"
                  type="daterange"
                  size="small"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label=" " prop="content" :rules="{
                  required: false, message: '请输入教育经历', trigger: 'blur'
                }">
                <el-input v-model="item.content" placeholder="教育经历" size="small"></el-input>
              </el-form-item>
              <el-form-item label=" ">
                <el-input v-model="item.certificateName" placeholder="证书名称" size="small"></el-input>
              </el-form-item>
              <el-form-item label=" ">
                <el-input v-model="item.certificateCode" placeholder="证书编号" size="small"></el-input>
              </el-form-item>
              <span class="multiple-col">
                <el-button
                  size="small"
                  icon="el-icon-delete"
                  @click="delContinuousEducation(index)"
                ></el-button>
              </span>
            </el-form>
          </div>
          <div class="form-title">
            <span style="margin-right: 20px">进修培训、援外经历(3个月以上)</span>
            <el-button size="mini" type="primary" @click="addTrainingExperience">
              增加
            </el-button>
          </div>
          <div
            class="multiple-institutions"
            v-for="(item, index) in trainingExperienceList" :key="'train-' + index"
          >
            <el-form label-width="150px" size="small" :inline="true" :model="item" ref="trainingExperienceList">
              <el-form-item label=" " prop="date" :rules="{
                  required: false, message: '请选择日期', trigger: 'change'
                }">
                <el-date-picker
                  v-model="item.date"
                  type="daterange"
                  size="small"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label=" " prop="content" :rules="{
                  required: false, message: '请输入经历内容', trigger: 'blur'
                }">
                <el-input v-model="item.content" placeholder="经历内容" size="small"></el-input>
              </el-form-item>
              <span class="multiple-col">
                <el-button
                  size="small"
                  icon="el-icon-delete"
                  @click="delTrainingExperience(index)"
                ></el-button>
              </span>
            </el-form>
          </div>
          <div class="form-title">
            <span style="margin-right: 20px">工作经历</span>
            <el-button size="mini" type="primary" @click="addWorkExperience">
              增加
            </el-button>
          </div>
          <div
            class="multiple-institutions"
            v-for="(item, index) in workExperienceList" :key="'work-' + index"
          >
            <el-form label-width="150px" size="small" :inline="true" :model="item" ref="workExperienceList">
              <el-form-item label=" " prop="date" :rules="{
                  required: false, message: '请选择日期', trigger: 'change'
                }">
                <el-date-picker
                  v-model="item.date"
                  type="daterange"
                  size="small"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label=" " prop="content" :rules="{
                  required: false, message: '请输入工作经历', trigger: 'blur'
                }">
                <el-input v-model="item.content" placeholder="工作经历" size="small"></el-input>
              </el-form-item>
              <span class="multiple-col">
                <el-button
                  size="small"
                  icon="el-icon-delete"
                  @click="delWorkExperience(index)"
                ></el-button>
              </span>
            </el-form>
          </div>
          <div class="form-title">
            <span style="margin-right: 20px">获奖情况</span>
            <el-button size="mini" type="primary" @click="addAward">
              增加
            </el-button>
          </div>
          <div
            class="multiple-institutions"
            v-for="(item, index) in awardList" :key="'award-' + index"
          >
            <el-form label-width="150px" size="small" :inline="true" :model="item" ref="awardList">
              <el-form-item label=" " prop="date" :rules="{
                  required: false, message: '请选择日期', trigger: 'change'
                }">
                <el-date-picker
                  v-model="item.date"
                  type="date"
                  size="small"
                  placeholder="获奖日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label=" " prop="content" :rules="{
                  required: false, message: '请输入获奖情况', trigger: 'blur'
                }">
                <el-input v-model="item.content" placeholder="获奖情况" size="small"></el-input>
              </el-form-item>
              <span class="multiple-col">
                <el-button
                  size="small"
                  icon="el-icon-delete"
                  @click="delAward(index)"
                ></el-button>
              </span>
            </el-form>
          </div>
        <!-- </el-form> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit" size="small">提 交</el-button>
        <el-button @click="cancel" size="small">重 置</el-button>
      </span>
    </el-dialog>
    <!-- 重置密码 -->
    <el-dialog
      title="密码管理"
      :visible.sync="passwordDialog"
      width="500px"
      :before-close="closePasswordDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="passwordForm"
        :rules="passwordRules"
        size="small"
        label-width="120px"
        ref="passwordForm"
      >
        <el-form-item label="当前密码：" prop="password">
          <el-input
            :key="passwordType"
            ref="password"
            v-model="passwordForm.password"
            :type="passwordType"
            placeholder="请输入密码"
            name="password"
            tabindex="2"
            auto-complete="on"
            :disabled="disabledPwd"
          />
          <span class="show-pwd" @click="showPwd">
            <svg-icon
              :icon-class="
                passwordType === 'password' ? 'is-show-password' : 'show-password'
              "
            />
          </span>
        </el-form-item>
        <el-form-item label="新密码：" prop="newPassword" v-if="isShowEditPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            name="newPassword"
            tabindex="2"
            auto-complete="on"
          />
        </el-form-item>
        <el-form-item
          label="确认新密码："
          prop="confirmPassword"
          v-if="isShowEditPassword"
        >
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            name="confirmPassword"
            tabindex="2"
            auto-complete="on"
          />
        </el-form-item>
        <el-form-item>
          <div v-if="!isShowEditPassword">
            <el-button class="el-button--custom" @click="resetPassword">
              一键重置密码
            </el-button>
            <el-button type="primary" @click="changePassword">修改密码</el-button>
          </div>
          <div v-else>
            <el-button @click="closePasswordDialog">取消</el-button>
            <el-button type="primary" @click="savePassword">保存</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getToken,getWindowName } from '@/utils/auth'
const loginInfo = getWindowName()
const { appId } = loginInfo
import axios from 'axios'
import { encrypt, decrypt } from '@/utils/crypto-request'
import { dataList} from '@/api/records'
import {
  doctorList,
  doctorAdd,
  doctorDetail,
  doctorUpdate,
  doctorDelete
} from '@/api/doctor'
import { orgList } from '@/api/structure'
import { getAllDataList, roleList, fileUpload } from '@/api/common'
import { resetPwd, updatePwd, queryPwd } from '@/api/user-manage'
import moment from 'moment'
export default {
  data() {
    const validateNewPassword = (rule, value, callback) => {
      if (value === this.passwordForm.password) {
        callback('新密码不可与旧密码一致!')
      } else {
        callback()
      }
    }
    var validatePass = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入身份证号！'));
        } else {
          let zz = /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
          if (!zz.test(value)) {
            callback(new Error('身份证号格式不正确！'));
          }
          callback();
        }
      };
      var validatePass1 = (rule, value, callback) => {
        if (value === '') {
          callback(new Error('请输入电话号码！'));
        } else {
          let zz = /^1[3-9]\d{9}$/
          if (!zz.test(value)) {
            callback(new Error('电话号码格式不正确！'));
          }
          callback();
        }
      };
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback('两次输入密码不一致!')
      } else {
        callback()
      }
    }
    const validatorPassword = (rule, value, callback) => {
      callback(validatorPwd(rule, value, callback))
    }
    return {
      // ---健康卡---
      loading: false,
      isShowDialog: false,
      isEdit: false, // 是否编辑
      tableData: [],
      total: 0,
      pageSize: 10,
      pageNum: 1,
      depts: [], // 机构下拉列表
      qualifications: [], // 任职资格下拉列表
      practiceLevel: [], // 执业级别下拉列表
      practiceTypes: [], // 医师执业类别下拉列表
      practiceItems: [], // 执业范围下拉列表
      searchForm: {
        orgCode: '', // 机构
        orgCodes: [], // 机构多选
        practiceLevelId: '', // 职业级别
        practiceLevelIds: [], // 职业级别多选
        practiceTypeIds: [], // 职业类型多选
        practiceTypeId: '', // 职业类型
        practiceItemId: '', // 职业范围
        keyword: '' // 关键字
      },
      depts1: [],
      depts2: [],
      depts3: [],
      depts4: [],
      doctorInfo: {
        id: '',
        name: '',
        sex: '',
        identityNo: '',
        birthday: '',
        isExpert: '',
        ethnicity: '',
        phoneNumber: '',
        email: '',
        userName: '',
        orgCode: '',
        roles: [],
        identificationPhoto: '',
        depts1: [],
        depts2: [],
        depts3: [],
        depts4: [],
      }, // 医生基本信息表单
      doctorEducation: {
        id: '',
        school: '',
        subject: '',
        learnTime: '',
        serviceEducation: '',
        serviceEducationCode: '',
        degree: '',
        degreeCode: '',
        graduationTime: '',
        incumbencyDegree: '',
        incumbencyDegreeCode: '',
        continuousEducational: '', // 继续教育
        learnItem: '', // 培训经历
        workItem: '', // 共组经历
        winningRecord: '', // 获奖记录
        credits: ''
      }, // 医生教育信息表单
      doctorQualification: {
        id: '',
        code: '',
        type: '',
        qualificationYear: '',
        sendDocumentsOrg: '',
        issueDate: ''
      }, // 医生资格信息表单
      doctorPracticepoint: {
        id: '',
        code: '',
        practiceLevelId: '',
        practiceDept: '',
        practiceTypeId: '',
        practiceItemIds: '',
        practiceOrg: '',
        practiceOrgAddress: '',
        enabled: '',
        titleInformation: '',
        checkOrg: '',
        checkDate: '',
        practiceTitleId: '',
        isGeneralPractitioner: ''
      }, // 医生职业信息表单
      doctorPracticepointItems: [
        {
          name: '',
          date: [],
          remarkOrg: '',
          id: '',
          practicepointId: ''
        }
      ], // 多机构备案
      deleteIds: [], // 删除的备案
      continuousEducationList: [
        {
          date: [],
          content: '',
          certificateName: '',
          certificateCode: ''
        }
      ], // 继续教育列表
      trainingExperienceList: [
        {
          date: [],
          content: ''
        }
      ], // 进修培训、援外经历
      workExperienceList: [
        {
          date: [],
          content: ''
        }
      ], // 工作经历列表
      awardList: [
        {
          date: '',
          content: ''
        }
      ], // 获奖情况列表
      learnItem: '', // 专业学习培训经历
      currentRoleList: [], // 角色下拉列表
      imageUrl: '', // 上传图片预览
      orgItems: [],
      passwordDialog: false,
      passwordForm: {
        password: '',
        newPassword: '',
        confirmPassword: ''
      },
      rules1: {
        practiceDept: [
          { required: true, message: "请输入院内科室", trigger: "blur" },
        ],
        practiceOrg: [
          { required: true, message: "请选择主要职业机构", trigger: "change" },
        ],
        checkDate: [
          { required: true, message: "请选择审批日期", trigger: "change" },
        ],
        isGeneralPractitioner: [
          { required: true, message: "请选择是否全科医生", trigger: "change" },
        ],
        enabled: [
          { required: true, message: "请选择是否有效", trigger: "change" },
        ],
      },
      rules: {
        name: [
          { required: true, message: "请输入姓名", trigger: "blur" },
        ],
        sex: [
          { required: true, message: "请选择性别", trigger: "change" },
        ],
        identityNo: [
          { validator: validatePass, trigger: "blur",required: true, },
        ],
        phoneNumber: [
          { validator: validatePass1, trigger: "blur",required: true, },
        ],
        birthday: [
          { required: true, message: "请输入出生日期", trigger: "change" },
        ],
        ethnicity: [
          { required: true, message: "请输入名族", trigger: "blur" },
        ],
        userName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        roles: [
          { required: true, message: "请选择角色", trigger: "change" },
        ],
        orgCode: [
          { required: true, message: "请选择机构", trigger: "change" },
        ],
        isExpert: [
          { required: true, message: "请选择专家", trigger: "change" },
        ],
      },
      passwordRules: {
        password: [{ required: false, trigger: 'blur', message: '当前密码不能为空' }],
        newPassword: [
          { required: true, trigger: 'blur', message: '请输入新密码' },
          {
            validator: validateNewPassword,
            trigger: 'blur'
          },
          {
            validator: validatorPassword,
            trigger: 'blur'
          }
        ],
        confirmPassword: [
          { required: true, trigger: 'blur', message: '请确认新密码' },
          {
            validator: validateConfirmPassword,
            trigger: 'blur'
          },
          {
            validator: validatorPassword,
            trigger: 'blur'
          }
        ]
      },
      passwordType: 'password',
      isShowEditPassword: false,
      tempRow: {},
      disabledPwd: false,
      showDialogMb: false,
      fileList: [],
      url: ''
    }
  },
  mounted() {
    if (process.env.NODE_ENV == 'development') {
      this.url = '/medical'
    } else {
      this.url = '/medical'
    }
  },
  created() {
    this.init()
    this.dataList()
  },
  methods: {
    UploadMb(){
      this.showDialogMb = true
    },
    handleUpload() {
      console.log(this.fileList)
      if (this.fileList.length == 0) {
        this.$message({
          message: '上传文件不能为空！',
          type: 'warning'
        });
      } else {
        let url = this.url + "/doctor/importExcel"
        let formDate = new FormData()
        formDate.append('file',this.fileList[0].raw)
        axios.post(url,formDate,{
            headers: {
              'saToken': getToken(), // 设置请求头，确保服务器正确解析 FormData
              'appId': appId
            }}).then((res)=>{
          console.log(res)
          if (res.data.code == 200) {
            this.$message({
              message: '上传成功',
              type: 'success'
            });
            this.fileList = []
            this.query()
            this.showDialogMb = false
          } else {
            this.$message({
              message: res.data.data,
              type: 'error'
            });
          }
        })
      }
    },
        /**
     * data: 下载文件
     * fileName: 文件名
     * type: 下载文件类型
     */
    downloadHandler(data, fileName, type) {
        // 匹配任意文件类型：type : "application/octet-stream"
        const blob = new Blob([data],  { type: type });
        console.log(blob)
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = fileName;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
    },
    exportMb() {
      let url = this.url + "/doctor/download/template"
      axios.post(url,{},{ responseType: 'arraybuffer',
            headers: {
              'saToken': getToken(), // 设置请求头，确保服务器正确解析 FormData
              'appId': appId
            }}).then((res)=>{

        if (res.status == 200) {
          this.$message({
            message: '模版下载成功！',
            type: 'success'
          });
        }
        this.downloadHandler(res.data,'模版.xlsx','application/vnd.ms-excel')
      })
    },
    handleChange(file, fileList) {
      this.fileList = fileList
      console.log(fileList)
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    // 新增编辑节点弹窗关闭
    handleClose() {
      this.showDialogMb = false
    },
    setDepts(val) {

    },
    dataList() {
      dataList('sys_expert_dept').then(res => {
        this.depts1 = res.data
      })
      dataList('sys_expert_disease').then(res => {
        this.depts2 = res.data
      })
      dataList('sys_expert_treat').then(res => {
        this.depts3 = res.data
      })
      dataList('sys_expert_region').then(res => {
        this.depts4 = res.data
      })
    },
    // 查询
    query() {
      this.pageNum = 1
      this.axiosTable()
    },
    // 表格默认请求
    axiosTable() {
      this.loading = true
      const data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        orgCodes: this.searchForm.orgCodes,
        practiceLevelIds: this.searchForm.practiceLevelIds,
        practiceTypeIds: this.searchForm.practiceTypeIds,
        practiceItemId: this.searchForm.practiceItemId,
        keyword: this.searchForm.keyword
      }
      doctorList(data)
        .then(res => {
          this.tableData = res.data.list
          this.loading = false
          this.total = res.data.total
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 新增
    add() {
      this.isEdit = false
      this.isShowDialog = true
    },
    // sizeChange
    handleSizeChange(size) {
      this.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.pageNum = page
      this.axiosTable(page)
    },
    // 编辑
    edit(row) {
      this.isEdit = true
      this.isShowDialog = true
      doctorDetail({ id: row.id }).then(res => {
        if (res.code === 200) {
          this.formatDetail(res.data)
        }
      })
    },
    handleChange1() {
      console.log(this.doctorPracticepoint.practiceItemId)
    },
    // 格式化编辑弹窗内容
    formatDetail(data) {
      data.doctorPracticepoint ? data.doctorPracticepoint : data.doctorPracticepoint = {}
      data.doctorPracticepointItems ? data.doctorPracticepointItems : data.doctorPracticepointItems = []
      data.doctorEducation ? data.doctorEducation : data.doctorEducation = {}
      data.doctorQualification ? data.doctorQualification : data.doctorQualification = {}

      // 基本信息
      if ( data.doctorInfo.expertLabels) {
        this.doctorInfo.depts1 = data.doctorInfo.expertLabels.sys_expert_dept
        this.doctorInfo.depts2 = data.doctorInfo.expertLabels.sys_expert_disease
        this.doctorInfo.depts3 = data.doctorInfo.expertLabels.sys_expert_treat
        this.doctorInfo.depts4 = data.doctorInfo.expertLabels.sys_expert_region
      }

      this.doctorInfo.id = data.doctorInfo.id
      this.doctorInfo.name = data.doctorInfo.name
      this.doctorInfo.sex = data.doctorInfo.sex
      this.doctorInfo.identityNo = data.doctorInfo.identityNo
      this.doctorInfo.birthday = data.doctorInfo.birthday
      this.doctorInfo.isExpert = data.doctorInfo.isExpert
      this.doctorInfo.ethnicity = data.doctorInfo.ethnicity
      this.doctorInfo.phoneNumber = data.doctorInfo.phoneNumber
      this.doctorInfo.email = data.doctorInfo.email
      this.doctorInfo.userName = data.doctorInfo.userName
      this.doctorInfo.orgCode = data.doctorInfo.orgCode
      this.doctorInfo.roles = (data.doctorInfo.roles ? data.doctorInfo.roles : []).map(item => {
        return Number(item.roleId)
      })
      this.imageUrl = data.doctorInfo.identificationPhoto
      // 教育信息
      this.doctorEducation.id = data.doctorEducation.id
      this.doctorEducation.school = data.doctorEducation.school
      this.doctorEducation.subject = data.doctorEducation.subject
      this.doctorEducation.learnTime = data.doctorEducation.learnTime
      this.doctorEducation.serviceEducation = data.doctorEducation.serviceEducation
      this.doctorEducation.serviceEducationCode = data.doctorEducation.serviceEducationCode
      this.doctorEducation.degree = data.doctorEducation.degree
      this.doctorEducation.degreeCode = data.doctorEducation.degreeCode
      this.doctorEducation.graduationTime = data.doctorEducation.graduationTime
      this.doctorEducation.incumbencyDegree = data.doctorEducation.incumbencyDegree
      this.doctorEducation.incumbencyDegreeCode = data.doctorEducation.incumbencyDegreeCode
      this.doctorEducation.credits = data.doctorEducation.credits

      // 继续教育 - 处理格式转换
      if (data.doctorEducation.continuousEducationalList || data.doctorEducation.continuousEducational) {
        // 获取数据，优先使用新字段名
        const continuousData = data.doctorEducation.continuousEducationalList || data.doctorEducation.continuousEducational;
        // 如果是字符串，说明是旧数据，需要转换
        if (typeof continuousData === 'string') {
          this.continuousEducationList = [{
            date: [],
            content: continuousData || '',
            certificateName: '',
            certificateCode: ''
          }]
        } else {
          // 如果是数组，说明是新数据格式
          this.continuousEducationList = continuousData.map(item => {
            return {
              date: item.date || [item.startDate, item.endDate],
              content: item.content || '',
              certificateName: item.certificateName || '',
              certificateCode: item.certificateCode || ''
            }
          })
        }
      } else {
        // 没有数据，初始化一条空记录
        this.continuousEducationList = [{
          date: [],
          content: '',
          certificateName: '',
          certificateCode: ''
        }]
      }

      // 进修培训、援外经历 - 处理格式转换
      if (data.doctorEducation.learnItemList || data.doctorEducation.learnItem) {
        // 获取数据，优先使用新字段名
        const learnData = data.doctorEducation.learnItemList || data.doctorEducation.learnItem;
        // 如果是字符串，说明是旧数据，需要转换
        if (typeof learnData === 'string') {
          this.trainingExperienceList = [{
            date: [],
            content: learnData || ''
          }]
        } else {
          // 如果是数组，说明是新数据格式
          this.trainingExperienceList = learnData.map(item => {
            return {
              date: item.date || [item.startDate, item.endDate],
              content: item.content || ''
            }
          })
        }
      } else {
        // 没有数据，初始化一条空记录
        this.trainingExperienceList = [{
          date: [],
          content: ''
        }]
      }

      // 工作经历 - 处理格式转换
      if (data.doctorEducation.workItemList || data.doctorEducation.workItem) {
        // 获取数据，优先使用新字段名
        const workData = data.doctorEducation.workItemList || data.doctorEducation.workItem;
        // 如果是字符串，说明是旧数据，需要转换
        if (typeof workData === 'string') {
          this.workExperienceList = [{
            date: [],
            content: workData || ''
          }]
        } else {
          // 如果是数组，说明是新数据格式
          this.workExperienceList = workData.map(item => {
            return {
              date: item.date || [item.startDate, item.endDate],
              content: item.content || ''
            }
          })
        }
      } else {
        // 没有数据，初始化一条空记录
        this.workExperienceList = [{
          date: [],
          content: ''
        }]
      }

      // 获奖情况 - 处理格式转换
      if (data.doctorEducation.winningRecordList || data.doctorEducation.winningRecord) {
        // 获取数据，优先使用新字段名
        const awardData = data.doctorEducation.winningRecordList || data.doctorEducation.winningRecord;
        // 如果是字符串，说明是旧数据，需要转换
        if (typeof awardData === 'string') {
          this.awardList = [{
            date: '',
            content: awardData || ''
          }]
        } else {
          // 如果是数组，说明是新数据格式
          this.awardList = awardData.map(item => {
            return {
              date: item.date || item.awardDate,
              content: item.content || ''
            }
          })
        }
      } else {
        // 没有数据，初始化一条空记录
        this.awardList = [{
          date: '',
          content: ''
        }]
      }

      // 医生资格信息
      this.doctorQualification.id = data.doctorQualification.id
      this.doctorQualification.code = data.doctorQualification.code
      this.doctorQualification.type = data.doctorQualification.type
      this.doctorQualification.qualificationYear = data.doctorQualification.qualificationYear
      this.doctorQualification.sendDocumentsOrg = data.doctorQualification.sendDocumentsOrg
      this.doctorQualification.issueDate = data.doctorQualification.issueDate
      // 职业信息
      this.doctorPracticepoint.id = data.doctorPracticepoint.id
      this.doctorPracticepoint.code = data.doctorPracticepoint.code
      this.doctorPracticepoint.practiceLevelId = data.doctorPracticepoint.practiceLevelId
      this.doctorPracticepoint.practiceDept = data.doctorPracticepoint.practiceDept
      this.doctorPracticepoint.practiceTypeId = data.doctorPracticepoint.practiceTypeId
      this.doctorPracticepoint.practiceItemId = data.doctorPracticepoint.practiceItemId
      this.doctorPracticepoint.practiceItemIds = data.doctorPracticepoint.practiceItemIds || []

      this.doctorPracticepoint.practiceOrg = data.doctorPracticepoint.practiceOrg
      this.doctorPracticepoint.practiceOrgAddress = data.doctorPracticepoint.practiceOrgAddress
      this.doctorPracticepoint.enabled = data.doctorPracticepoint.enabled
      this.doctorPracticepoint.titleInformation = data.doctorPracticepoint.titleInformation
      this.doctorPracticepoint.checkOrg = data.doctorPracticepoint.checkOrg
      this.doctorPracticepoint.checkDate = data.doctorPracticepoint.checkDate
      this.doctorPracticepoint.practiceTitleId = data.doctorPracticepoint.practiceTitleId
      this.doctorPracticepoint.isGeneralPractitioner = data.doctorPracticepoint.isGeneralPractitioner
      // 多机构备案
      console.log(data.doctorPracticepointItems)
      this.doctorPracticepointItems = data.doctorPracticepointItems.map(item => {
        const data = {
          name: item.name,
          id: item.id,
          practicepointId: item.practicepointId,
          date: [item.effectiveStartDate, item.effectiveEndDate],
          remarkOrg: item.remarkOrg
        }
        return data
      })
    },
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          doctorDelete({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 导出
    exportTable() {
      let arr = JSON.parse(JSON.stringify(this.tableData))
      arr.forEach((item)=>{
         let json = ''
        if (item.roles) {
          item.roles.forEach((ite)=>{
            json+= ite.roleName
          })
        }
        item.roleName = json
      })
      const header = ['phoneNumber', 'name', 'sex', 'birthday', 'code', 'practiceOrg','practiceLevel','practiceType','practiceItem', 'roleName', 'lastLoginTime']
      const data = this.formatJson(header, arr)
      import('@/utils/export2excel').then(excel => {
        excel.export_json_to_excel({
          header: ['手机号码', '姓名', '性别', '出生日期', '执业证书编码', '主要执业机构', '专业技术资格（评）', '医师执业类别', '执业范围', '用户角色', '上一次登录'],
          data,
          filename: '卫生技术人员信息维护',
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    // 弹窗提交
    submit() {
      let formArr=['ruleForm','doctorEducation','doctorQualification','doctorPracticepoint']//假设这是四个form表单的ref
      var resultArr = []//用来接受返回结果的数组
      var that = this
      function checkForm(formName) { //封装验证表单的函数
        var result = new Promise(function(resolve, reject) {
          that.$refs[formName].validate((valid) => {
            if (valid) {
              resolve();
            } else {
              reject() }
          })
        })

        resultArr.push(result) //push 得到promise的结果
      }
      formArr.forEach(item => { //根据表单的ref校验
          checkForm(item)
      })

      Promise.all(resultArr).then(function(results ) { //都通过了
        let doctorPracticepointItems = that.$refs.doctorPracticepointItems
        let index = 0
        doctorPracticepointItems.forEach((item)=>{
          item.validate((valid) => {
            if (valid) {
              index++
              console.log(index)
            }
          })
        })
        if (index == doctorPracticepointItems.length) {
          const data = {
            doctorInfo: that.formatDoctorInfo(),
            doctorEducation: that.formatDoctorEducation(),
            doctorQualification: that.formatDoctorQualification(),
            doctorPracticepoint: that.formatDoctorPracticepoint(),
            doctorPracticepointItems: that.formatDoctorPracticepointItems(), // 多机构备案
            deleteIds: that.deleteIds // 删除的备案
          }
          if (that.isEdit) {
            doctorUpdate(data)
              .then(res => {
                if (res.code === 200) {
                  that.$message.success(res.message)
                  that.query()
                  that.isShowDialog = false
                  that.isEdit = false
                  that.reset()
                } else {
                  that.$message.error(res.message)
                }
              })
              .catch(() => {})
          } else {
            doctorAdd(data)
              .then(res => {
                if (res.code === 200) {
                  that.$message.success(res.message)
                  that.query()
                  that.isShowDialog = false
                  that.isEdit = false
                  that.reset()
                } else {
                  that.$message.error(res.message)
                }
              })
              .catch(() => {})
          }
        }
      }).catch(function() {
        console.log("err");
      });
    },
    // 格式化医生基本信息
    formatDoctorInfo() {
      let data = {}
      let arr = []
      if (this.doctorInfo.isExpert == 1) {
        arr = [].concat(this.doctorInfo.depts1, this.doctorInfo.depts2,this.doctorInfo.depts3,this.doctorInfo.depts4)
      }
      data.expertLabels = arr
      data.id = this.doctorInfo.id
      data.name = this.doctorInfo.name
      data.sex = this.doctorInfo.sex
      data.isExpert = this.doctorInfo.isExpert
      data.identityNo = this.doctorInfo.identityNo
      if (this.doctorInfo.identityNo.indexOf('*') == -1) {
          data.updateIdentityNo = this.doctorInfo.identityNo
      }
      data.birthday = moment(this.doctorInfo.birthday).format('YYYY-MM-DD')
      data.ethnicity = this.doctorInfo.ethnicity
      data.phoneNumber = this.doctorInfo.phoneNumber
      data.email = this.doctorInfo.email
      data.userName = this.doctorInfo.userName
      data.orgCode = this.doctorInfo.orgCode
      data.roles = this.doctorInfo.roles.map(item => {
        return { roleId: item }
      })
      data.identificationPhoto = this.imageUrl
      return data
    },
    // 格式化医生教育信息
    formatDoctorEducation() {
      let data = {}
      data.id = this.doctorEducation.id
      data.school = this.doctorEducation.school
      data.subject = this.doctorEducation.subject
      data.learnTime = this.doctorEducation.learnTime
      data.serviceEducation = this.doctorEducation.serviceEducation
      data.serviceEducationCode = this.doctorEducation.serviceEducationCode

      // 格式化继续教育数据
      data.continuousEducationalList = this.continuousEducationList.map(item => {
        return {
          startDate: item.date && item.date[0] ? moment(item.date[0]).format('YYYY-MM-DD') : '',
          endDate: item.date && item.date[1] ? moment(item.date[1]).format('YYYY-MM-DD') : '',
          content: item.content,
          certificateName: item.certificateName,
          certificateCode: item.certificateCode
        }
      })

      // 格式化进修培训、援外经历数据
      data.learnItemList = this.trainingExperienceList.map(item => {
        return {
          startDate: item.date && item.date[0] ? moment(item.date[0]).format('YYYY-MM-DD') : '',
          endDate: item.date && item.date[1] ? moment(item.date[1]).format('YYYY-MM-DD') : '',
          content: item.content
        }
      })

      // 格式化工作经历数据
      data.workItemList = this.workExperienceList.map(item => {
        return {
          startDate: item.date && item.date[0] ? moment(item.date[0]).format('YYYY-MM-DD') : '',
          endDate: item.date && item.date[1] ? moment(item.date[1]).format('YYYY-MM-DD') : '',
          content: item.content
        }
      })

      // 格式化获奖情况数据
      data.winningRecordList = this.awardList.map(item => {
        return {
          awardDate: item.date ? moment(item.date).format('YYYY-MM-DD') : '',
          content: item.content
        }
      })

      data.degree = this.doctorEducation.degree
      data.degreeCode = this.doctorEducation.degreeCode
      data.graduationTime = moment(this.doctorEducation.graduationTime).format(
        'YYYY-MM-DD'
      )
      data.incumbencyDegree = this.doctorEducation.incumbencyDegree
      data.incumbencyDegreeCode = this.doctorEducation.incumbencyDegreeCode
      data.credits = this.doctorEducation.credits
      return data
    },
    // 格式化医师资格信息
    formatDoctorQualification() {
      let data = {}
      data.id = this.doctorQualification.id
      data.code = this.doctorQualification.code
      data.type = this.doctorQualification.type
      data.qualificationYear = this.doctorQualification.qualificationYear
      data.sendDocumentsOrg = this.doctorQualification.sendDocumentsOrg
      data.issueDate = moment(this.doctorQualification.issueDate).format('YYYY-MM-DD')
      return data
    },
    // 格式化医生职业信息
    formatDoctorPracticepoint() {
      let data = {}
      data.id = this.doctorPracticepoint.id
      data.code = this.doctorPracticepoint.code
      data.practiceLevelId = this.doctorPracticepoint.practiceLevelId
      data.practiceDept = this.doctorPracticepoint.practiceDept
      data.practiceTypeId = this.doctorPracticepoint.practiceTypeId
      data.practiceItemId = this.doctorPracticepoint.practiceItemId
      data.practiceItemIds = this.doctorPracticepoint.practiceItemIds
      data.practiceOrg = this.doctorPracticepoint.practiceOrg
      data.practiceOrgAddress = this.doctorPracticepoint.practiceOrgAddress
      data.enabled = this.doctorPracticepoint.enabled
      data.titleInformation = this.doctorPracticepoint.titleInformation
      data.checkOrg = this.doctorPracticepoint.checkOrg
      data.checkDate = moment(this.doctorPracticepoint.checkDate).format('YYYY-MM-DD')
      data.practiceTitleId = this.doctorPracticepoint.practiceTitleId
      data.isGeneralPractitioner = this.doctorPracticepoint.isGeneralPractitioner
      return data
    },
    // 格式化多机构
    formatDoctorPracticepointItems() {
      const data = this.doctorPracticepointItems.map(item => {
        let obj = {}
        obj.effectiveStartDate = moment(item.date[0]).format('YYYY-MM-DD')
        obj.effectiveEndDate = moment(item.date[0]).format('YYYY-MM-DD')
        obj.remarkOrg = item.remarkOrg
        obj.name = item.name
        obj.id = item.id
        obj.practicepointId = item.practicepointId
        return obj
      })
      return data
    },
    // 增加多机构内容
    addPracticepointItems() {
      const obj = {
        name: '',
        date: [],
        remarkOrg: ''
      }
      this.doctorPracticepointItems.push(obj)
    },
    // 删除一条多机构内容
    delPracticepointItems(item, index) {
      if (this.doctorPracticepointItems.length > 1) {
        this.doctorPracticepointItems.splice(index, 1)
        if (item.id) {
          this.deleteIds.push(item.id)
        }
      } else {
        this.$message.error('最少保留一行内容！')
      }
    },
    // 增加继续教育内容
    addContinuousEducation() {
      this.continuousEducationList.push({
        date: [],
        content: '',
        certificateName: '',
        certificateCode: ''
      })
    },
    // 删除继续教育内容
    delContinuousEducation(index) {

        this.continuousEducationList.splice(index, 1)

    },
    // 增加进修培训、援外经历
    addTrainingExperience() {
      this.trainingExperienceList.push({
        date: [],
        content: ''
      })
    },
    // 删除进修培训、援外经历
    delTrainingExperience(index) {

        this.trainingExperienceList.splice(index, 1)

    },
    // 增加工作经历内容
    addWorkExperience() {
      this.workExperienceList.push({
        date: [],
        content: ''
      })
    },
    // 删除工作经历内容
    delWorkExperience(index) {
      this.workExperienceList.splice(index, 1)
    },
    // 增加获奖情况内容
    addAward() {
      this.awardList.push({
        date: '',
        content: ''
      })
    },
    // 删除获奖情况内容
    delAward(index) {
      this.awardList.splice(index, 1)
    },
    // 获取角色拉列表
    getRoleList() {
      roleList().then(res => {
        this.currentRoleList = res.data
      })
    },
    // 对需要导出的数据做处理
    formatJson(header, data) {
      return data.map(v => {
        if (v.appTag === 'BS') {
          v.appTag = '网页'
        } else {
          v.appTag = '客户端'
        }
        if (v.appSign === '1') {
          v.appSign = '平台应用'
        } else {
          v.appSign = '第三方应用'
        }
        return header.map(j => {
          return v[j]
        })
      })
    },
    // 移除图片
    removeUpload() {
      this.imageUrl = ''
    },
    reset() {
      this.total = 0
      this.pageSize = 10
      this.pageNum = 1
      this.doctorInfo = this.$options.data().doctorInfo // 医生基本信息表单
      this.doctorEducation = this.$options.data().doctorEducation // 医生教育信息表单
      this.doctorQualification = this.$options.data().doctorQualification // 医生资格信息表单
      this.doctorPracticepoint = this.$options.data().doctorPracticepoint // 医生职业信息表单
      this.doctorPracticepointItems = this.$options.data().doctorPracticepointItems // 多机构备案
      this.deleteIds = this.$options.data().deleteIds
      this.continuousEducationList = this.$options.data().continuousEducationList // 继续教育列表
      this.trainingExperienceList = this.$options.data().trainingExperienceList // 进修培训、援外经历
      this.workExperienceList = this.$options.data().workExperienceList // 工作经历列表
      this.awardList = this.$options.data().awardList // 获奖情况列表
      this.imageUrl = this.$options.data().imageUrl
    },
    // 密码管理弹窗
    passwordSetting(row) {
      this.passwordDialog = true
      this.tempRow = row
      this.getRowPwd(row.userName)
    },
    // 获取当前行的密码
    getRowPwd(username) {
      queryPwd({ username }).then(res => {
        if (res.code === 200) {
          this.passwordForm.password = decrypt(res.data, 'YJZL@2022@GM57sN')
          this.disabledPwd = true
        } else {
          this.$message.error('获取密码失败！请手动输入')
          this.disabledPwd = false
        }
      })
    },
    // 关闭密码弹窗
    closePasswordDialog() {
      this.passwordForm = this.$options.data().passwordForm
      this.isShowEditPassword = false
      this.passwordDialog = false
      this.$refs['passwordForm'].resetFields()
    },
    // 控制是否显示密码
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
    },
    // 重置密码
    resetPassword() {
      this.$refs['passwordForm'].validate(valid => {
        if (valid) {
          let passwordForm = {
            username: this.tempRow.userName,
            oldPassword: encrypt(this.passwordForm.password, 'YJZL@2022@GM57sN')
          }
          resetPwd(passwordForm).then(res => {
            if (res.code === 200) {
              this.$message.success('当前账号重置密码成功！')
              const { loginUserName } = getWindowName()
              if (this.tempRow.userName === (getLoginUserName() || loginUserName)) {
                this.$store.dispatch('user/logout').then(() => {
                  this.$router.push('/login')
                })
              } else {
                this.closePasswordDialog()
              }
            } else {
              this.$message.warning('重置密码失败！')
            }
          })
        } else {
          return false
        }
      })
    },
    // 修改密码
    changePassword() {
      this.isShowEditPassword = true
      // this.disabledPwd = false
      // this.passwordForm.password = ''
      this.passwordType = 'password'
    },
    // 保存
    savePassword() {
      this.$refs['passwordForm'].validate(valid => {
        if (valid) {
          let passwordForm = {
            username: this.tempRow.userName,
            oldPassword: encrypt(this.passwordForm.password, 'YJZL@2022@GM57sN'),
            newPassword1: encrypt(this.passwordForm.newPassword, 'YJZL@2022@GM57sN'),
            newPassword2: encrypt(this.passwordForm.confirmPassword, 'YJZL@2022@GM57sN')
          }
          updatePwd(passwordForm).then(res => {
            if (res.code === 200) {
              this.$message.success('保存密码成功！')
              const { loginUserName } = getWindowName()
              if (this.tempRow.userName === (getLoginUserName() || loginUserName)) {
                this.$store.dispatch('user/logout').then(() => {
                  this.$router.push('/login')
                })
              } else {
                this.closePasswordDialog()
              }
            } else {
              this.$message.warning('保存密码失败！')
            }
          })
        } else {
          return false
        }
      })
    },
    // 初始化获取下拉列表数据
    async init() {
      await this.getDepts()
      await this.getQualifications()
      await this.getPracticeLevel()
      await this.getPracticeTypes()
      await this.getPracticeItems()
      await this.query()
      await this.getRoleList()
      await this.getOrgList()
    },
    getOrgList() {
      orgList({
        pageSize: 10000,
        pageNum: 1,
      }).then(res => {
        this.orgItems = res.data.list
        console.log(res.data.list)
      })
    },
    // 获取机构下拉列表
    getDepts() {
      const data = { dictType: 'sys_org_type' }
      getAllDataList(data).then(res => {
        this.depts = res.data
      })
    },
    // 获取任职资格下拉列表
    getQualifications() {
      const data = { dictType: 'sys_qualifications' }
      getAllDataList(data).then(res => {
        this.qualifications = res.data
      })
    },
    // 获取执业级别下拉列表
    getPracticeLevel() {
      const data = { dictType: 'sys_practice_level' }
      getAllDataList(data).then(res => {
        this.practiceLevel = res.data
      })
    },
    // 获取职业类型下拉列表
    getPracticeTypes() {
      const data = { dictType: 'sys_practice_type' }
      getAllDataList(data).then(res => {
        this.practiceTypes = res.data
      })
    },
    // 获取职业范围下拉列表
    getPracticeItems() {
      const data = { dictType: 'sys_practice_item' }
      getAllDataList(data).then(res => {
        this.practiceItems = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.apps-manage {
  ::v-deep .el-dialog1 .el-dialog{
      width: 400px !important;
      .pciminchen {
        .titlename {
          margin-right: 10px;
        }
        .el-select {
          width: 280px;
        }
      }
    }
  .form-col {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        &::before {
          content: '';
          width: 4px;
          height: 14px;
          background: #1273ce;
          display: inline-block;
          margin-right: 4px;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
      img {
        height: 36px;
        width: 36px;
      }
      .blue {
        color: #1273ce;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
      .red {
        color: #ff4645;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
::v-deep .el-dialog {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
  }
  .avatar {
    position: relative;
    width: 100px;
    height: 100px;
    display: block;
    img {
      width: 100%;
      height: 100%;
      display: block;
    }
    i {
      position: absolute;
      top: 0;
      right: 0;
      font-size: 24px;
    }
  }
  .tips {
    color: #949699;
  }
}
.edit-form-wrap {
  position: relative;
  width: 100%;
  padding: 0 20px;
}
.form-title {
  padding: 10px;
  box-sizing: border-box;
  border-left: 4px solid #1273ce;
}
.form-textarea {
  padding: 10px 30px;
}
.multiple-institutions {
  margin: 10px 0;
  padding: 0 20px;
  ::v-deep {
    .el-form-item__label {
      width: auto !important;
    }
  }
  .multiple-col {
    margin: 0 5px;
  }
}
.show-pwd {
  margin-left: 10px;
}
</style>
<style lang="scss">
.el-dialog .el-dialog__body .el-form .number-input .el-input {
  width: 100%;
}
</style>
