<template>
  <div class="home-container">
    <div class="sign-config-manage">
      <el-radio-group v-model="isType" @change="changeType">
        <el-radio-button label="1">医院标志配置</el-radio-button>
        <el-radio-button label="2">系统配置</el-radio-button>
      </el-radio-group>
      <div class="form-col">
        <el-form
          :model="innerForm"
          size="small"
          label-width="150px"
          :rules="innerFormRule"
          ref="innerForm"
        >
          <div v-if="isType === '1'">
            <el-form-item label="医院标志配置">
              <!-- prop="logo" -->
              <el-upload
                class="avatar-uploader"
                action=""
                :show-file-list="false"
                :http-request="httpRequest"
                accept=".png,.jpg"
                :disabled="imageUrl ? true : false"
                v-model="innerForm.logo"
              >
                <div v-if="imageUrl" class="avatar">
                  <img :src="imageUrl" />
                  <i class="el-icon-error" @click.stop="removeUpload"></i>
                </div>
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              </el-upload>
              <div class="tips">
                *仅支持JPG，PNG格式，文件小于1M，建议上传图片未透明底色
              </div>
            </el-form-item>
          </div>
          <div v-else>
            <div>
              <el-form-item label="应用名称" prop="appId">
                <el-select v-model="innerForm.appId" @change="changeAppId">
                  <el-option
                    :label="item.name"
                    :value="item.id"
                    v-for="item in apps"
                    :key="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="应用名称展示字样" prop="systemName">
                <el-input v-model="innerForm.systemName" placeholder="请输入" />
              </el-form-item>
            </div>
          </div>
          <el-form-item label="标志效果展示">
            <div class="show-logo">
              <div v-if="imageUrl && isType === '1'" class="logo-text">
                <img :src="imageUrl" alt="" v-if="imageUrl && isType === '1'" />
              </div>
              <div v-if="innerForm.systemName && isType === '2'" class="logo-text">
                {{ innerForm.systemName }}
              </div>
              <div v-else-if="!imageUrl" class="empty">
                <img src="../../assets/images/sign-config-manage/empty.png" alt="" />
                <div class="text">
                  {{
                    isType === '1'
                      ? '暂无效果，请先上传医院标志'
                      : '暂无效果，请先输入应用名称展示字样'
                  }}
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="是否启用水印" v-if="isType !== '1'">
            <el-switch
              v-model="innerForm.wmk.isOpenWmk"
              active-color="#1273CE"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="save" type="primary">保 存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { updateName, addSign, selectById, getAppAll } from '@/api/sign-config-manage'
import { mapGetters } from 'vuex'
import { getAppId } from '@/utils/auth'
export default {
  computed: {
    ...mapGetters(['systemName', 'appLogo'])
  },
  data() {
    return {
      isType: '1',
      innerForm: {
        logo: '',
        appId: '',
        systemName: '',
        wmk: {
          isOpenWmk: false, // 是否开启水印功能
          content: '', // 内容
          wordSize: '', // 字体大小
          density: '', // 密度
          color: '', // 密度
          rotate: '' // 旋转角度
        }
      },
      innerFormRule: {
        logo: [
          {
            required: true,
            message: '请上传所需要的logo',
            trigger: 'change'
          }
        ],
        appId: [
          {
            required: true,
            message: '请选择应用名称',
            trigger: 'change'
          }
        ],
        systemName: [
          {
            required: true,
            message: '请输入应用名称展示字样',
            trigger: 'blur'
          },
          {
            max: 19,
            message: '最大限制字符19'
          }
        ]
      },
      imageUrl: '',
      apps: []
    }
  },
  mounted() {
    this.getApps()
    this.innerForm.logo = this.appLogo
    this.imageUrl = this.appLogo
  },
  methods: {
    getApps() {
      getAppAll().then(res => {
        this.apps = res.data
      })
    },
    changeType(val) {
      this.imageUrl = this.appLogo
      this.innerForm = this.$options.data().innerForm
    },
    // 自定义上传图片
    httpRequest(file) {
      if (file.file.size > 1024 * 1024) {
        this.$message.warning('上传图片大小不能超过1m!')
      } else {
        let that = this
        const reader = new FileReader()
        reader.readAsDataURL(file.file)
        reader.onload = function (e) {
          let base64 = e.target.result.split(',')[1]
          let imageUrl = `data:${file.file.type};base64,${base64}`
          that.imageUrl = imageUrl
          that.innerForm.logo = imageUrl
        }
      }
    },
    // 移除图片
    removeUpload() {
      this.imageUrl = ''
      this.innerForm.logo = ''
      // this.$store.commit('permission/SET_APPLOGO', '')
    },
    changeAppId(val) {
      selectById({ appId: val }).then(res => {
        this.innerForm = {
          ...this.innerForm,
          ...res.data,
        }
      })
    },
    // 保存
    save() {
      this.$refs['innerForm'].validate(valid => {
        if (valid) {
          if (this.isType === '1') {
            addSign({ casual: '', sign: this.innerForm.logo }).then(res => {
              if (res.code === 200) {
                this.$message.success('上传成功!')
                this.$store.commit('permission/SET_APPLOGO', this.innerForm.logo)
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            updateName({
              appId: this.innerForm.appId,
              systemName: this.innerForm.systemName,
              wmk:{
                isOpenWmk: this.innerForm.wmk.isOpenWmk,
              }
            }).then(res => {
              if (res.code === 200) {
                let appId = Number(getAppId())
                if (appId === this.innerForm.appId) {
                  this.$store.commit('permission/SET_APPNAME', this.innerForm.systemName)
                  this.$store.dispatch('permission/setWmk', this.innerForm.wmk)
                }
                this.$message.success('上传成功!')
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.sign-config-manage {
  min-height: calc(100vh - 136px);
  background: #fff;
  padding: 16px 20px;
  ::v-deep.is-active {
    .el-radio-button__inner {
      background: #1273ce;
      border-color: #1273ce;
    }
  }
  .el-radio-button__inner {
    color: #313133;
    &:hover {
      color: #1273ce !important;
    }
  }
  ::v-deep.form-col {
    margin-top: 30px;
    .avatar-uploader .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      cursor: pointer;
      position: relative;
      // overflow: hidden;
    }
    .avatar-uploader .el-upload:hover {
      border-color: #4f97da;
    }
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 120px;
      height: 120px;
      line-height: 120px;
      text-align: center;
    }
    .avatar {
      position: relative;
      img {
        width: 120px;
        height: 120px;
        display: block;
      }
      i {
        position: absolute;
        top: -7px;
        right: -7px;
        font-size: 20px;
      }
    }
    .tips {
      color: #949699;
    }
    .el-upload--picture-card:hover,
    .el-upload:focus {
      color: #969799;
    }
    .show-logo {
      width: 320px;
      height: 80px;
      display: inline-block;
      border-radius: 4px;
      border: 1px solid #dcdee5;
      padding: 12px;
      text-align: left;
      .logo-text {
        padding: 12px;
        background: #1273ce;
        height: 100%;
        color: #fff;
        display: inline-block;
        img {
          max-width: calc(320px - 48px);
          max-height: calc(80px - 48px);
        }
      }
      .empty {
        text-align: center;
      }
      .text {
        line-height: 20px;
        margin-top: -12px;
        color: #606266;
        font-size: 13px;
      }
    }
    .el-input {
      width: 256px;
    }
  }
}
</style>
