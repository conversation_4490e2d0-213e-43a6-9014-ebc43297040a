<template>
  <div class="home-container apps-menu-manage">
    <el-row class="container" :gutter="20">
      <el-col :span="6" class="left-container">
        <div class="main">
          <div class="search">
            <el-autocomplete
              prefix-icon="el-icon-search"
              size="small"
              placeholder="请输入应用名称"
              v-model="name"
              clearable
              :fetch-suggestions="querySearch"
              @select="handleSelect"
              @clear="clearSearch"
            />
          </div>
          <div class="list-container">
            <div
              class="list-item"
              v-for="(item, index) in listArr"
              :key="index"
              @click="choose(item, index)"
              :class="{ active: activeIndex === index }"
            >
              <div class="title">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="18" class="right-container">
        <div class="main">
          <div class="header-info">
            <div class="title">
              <span>{{ defaultData.name }}</span>
              <span class="id">ID:{{ defaultData.id }}</span>
            </div>
            <el-button size="small" type="primary" @click="add"
              >新 增</el-button
            >
          </div>
          <div class="table-col">
            <el-table
              :data="tableData"
              v-loading="loading"
              size="small"
              tooltip-effect="dark"
              width="100%"
              :header-cell-style="{
                color: '#303133',
                'font-weight': 600
              }"
              row-key="id"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            >
              <el-table-column label="菜单名称" prop="name" />
              <el-table-column label="图标">
                <template slot-scope="props">
                  <img :src="props.row.icon" alt="" />
                </template>
              </el-table-column>
              <el-table-column label="排序" prop="sort" />
              <el-table-column label="类型">
                <template slot-scope="props">
                  {{
                    props.row.type === '1'
                      ? '目录'
                      : props.row.type === '2'
                      ? '菜单'
                      : '按钮'
                  }}
                </template>
              </el-table-column>
              <el-table-column label="请求地址" prop="url" />
              <el-table-column label="操作">
                <template slot-scope="props">
                  <span class="blue" @click.stop="edit(props.row)">编辑</span>
                  <span class="red" @click.stop="remove(props.row)">删除</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      :title="dialogType === 0 ? '新增菜单信息' : '编辑菜单信息'"
      :visible.sync="isShowDialog"
      width="30%"
      :before-close="cancel"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="innerForm"
        size="small"
        label-width="120px"
        :rules="innerFormRule"
        ref="innerForm"
      >
        <el-form-item label="菜单类型:">
          <el-radio-group v-model="innerForm.type" @change="radioChange">
            <el-radio label="1" border :disabled="dialogType === 1"
              >目录</el-radio
            >
            <el-radio label="2" border :disabled="dialogType === 1"
              >菜单</el-radio
            >
            <el-radio label="3" border :disabled="dialogType === 1"
              >功能按钮</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :label="innerForm.type === '1' ? '目录名称:' : '菜单名称:'"
          prop="name"
        >
          <el-input v-model="innerForm.name" />
        </el-form-item>
        <el-form-item
          :label="innerForm.type === '1' ? '上级目录:' : '上级菜单:'"
          prop="parentName"
        >
          <el-select v-model="innerForm.parentName">
            <el-option
              v-for="(item, index) in parentNameList"
              :key="index"
              :value="item.value"
              :label="item.name"
            />
          </el-select>
          <!-- :disabled="dialogType === 1" -->
          <!-- <el-cascader
            v-if="parentNameList.length"
            v-model="innerForm.parentName"
            :options="parentNameList"
            :props="{ checkStrictly: true, emitPath: false }"
            :show-all-levels="false"
            @change="handleChange"
            ref="cascader"
          ></el-cascader> -->
        </el-form-item>
        <el-form-item label="显示排序:" prop="sort">
          <el-input v-model="innerForm.sort" />
        </el-form-item>
        <el-form-item
          label="请求地址:"
          v-if="innerForm.type !== '3'"
          prop="url"
        >
          <el-input v-model="innerForm.url" :disabled="addDisabled" />
        </el-form-item>
        <el-form-item
          label="上传图标:"
          v-if="innerForm.type !== '3'"
          prop="icon"
        >
          <el-input v-model="innerForm.icon" />
        </el-form-item>
        <el-form-item
          label="上传图标:"
          v-if="innerForm.type !== '3'"
          prop="icon"
        >
          <el-switch
            v-model="innerForm.hidden"
            active-value="true"
            inactive-value="false"
            active-text="隐藏"
            inactive-text="显示"
          >
          </el-switch>
        </el-form-item>
        <el-form-item
          label="按钮权限:"
          v-if="innerForm.type === '3'"
          prop="permission"
        >
          <el-input v-model="innerForm.permission" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取 消</el-button>
        <el-button type="primary" @click="submit" size="small">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// TODO 表格样式
// import { appGetAll } from '@/api/apps-manage'
import {
  menuList,
  menuRegister,
  menuUpdate,
  menuRemove,
  appGetAll
} from '@/api/app-menu-manage'
export default {
  data() {
    return {
      name: '',
      listArr: [],
      activeIndex: 0,
      defaultData: {
        name: '',
        id: ''
      },
      isShowDialog: false,
      dialogType: 0,
      innerForm: {
        type: '1',
        name: '',
        parentName: '一级菜单',
        sort: '',
        url: '',
        icon: '',
        permission: '',
        parentId: '0',
        hidden: 'false'
      },
      innerFormRule: {
        name: { required: true, message: '请输入菜单名称', trigger: 'blur' },
        parentName: {
          required: true,
          message: '请输入父节点ID',
          trigger: 'blur'
        },
        sort: { required: true, message: '请输入显示排序', trigger: 'blur' },
        url: { required: true, message: '请输入请求地址', trigger: 'blur' },
        icon: { required: true, message: '请输入图标', trigger: 'blur' },
        permission: { required: true, message: '请输入权限', trigger: 'blur' }
      },
      tableData: [],
      loading: false,
      restaurants: [],
      parentNameList: [],
      addDisabled: false
    }
  },
  mounted() {
    this.getDefaultAppList()
  },
  methods: {
    // 获取默认应用列表
    getDefaultAppList(appName = '') {
      appGetAll({ appName: appName, pageNum: 1, pageSize: 1000 }).then(
        (res) => {
          this.listArr = res.data
          this.restaurants = res.data.map((el) => {
            return {
              ...el,
              value: el.name
            }
          })
          if (res.data.length) {
            this.defaultData = res.data[0]
            this.axiosTable(this.defaultData.id)
          }
        }
      )
    },
    // 表格默认请求
    axiosTable(appId) {
      this.loading = true
      menuList({ appId })
        .then((res) => {
          this.loading = false
          this.tableData = res.data
          let temp = this.getparentNameList(res.data)
          this.parentNameList = [
            {
              value: '一级菜单',
              name: '当前已为最顶级菜单',
              children: []
            },
            ...temp
          ]
        })
        .catch((e) => {
          this.loading = false
        })
    },
    handleChange(e) {
      this.innerForm.parentId = e
      if (this.dialogType === 0) {
        if (this.innerForm.type === '1' && e !== '一级菜单') {
          this.innerForm.url = 'routeView'
          this.addDisabled = true
        } else {
          this.innerForm.url = ''
          this.addDisabled = false
        }
      } else {
        if (this.innerForm.type === '1' && e !== '一级菜单') {
          this.innerForm.url = 'routeView'
          this.addDisabled = true
        }
      }
    },
    // 单选change
    radioChange(e) {
      if (e === '1' && this.innerForm.parentName !== '一级菜单') {
        this.addDisabled = true
        this.innerForm.url = 'routeView'
      } else {
        this.addDisabled = false
        this.innerForm.url = ''
      }
    },
    // 新增
    add() {
      this.dialogType = 0
      this.isShowDialog = true
      let temp = this.getparentNameList(this.tableData)
      this.parentNameList = [
        {
          value: '一级菜单',
          name: '当前已为最顶级菜单',
          parentId: 0,
          children: []
        },
        ...temp
      ]
    },
    // 左侧选中
    choose(item, index) {
      this.activeIndex = index
      this.defaultData = item
      this.axiosTable(item.id)
    },
    // 编辑
    edit(row) {
      this.isShowDialog = true
      this.dialogType = 1
      let temp = this.getparentNameList(this.tableData)
      this.parentNameList = [
        {
          value: '一级菜单',
          name: '当前已为最顶级菜单',
          parentId: 0,
          children: []
        },
        ...temp
      ]
      this.innerForm = {
        ...this.innerForm,
        ...row,
        parentName: row.parentId === 0 ? '一级菜单' : row.parentId
      }
      if (row.type !== '1') {
        this.addDisabled = false
      }
    },
    // 删除
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          menuRemove({
            id: row.id
          }).then((res) => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.axiosTable(this.defaultData.id)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 弹窗关闭
    cancel() {
      this.innerForm = this.$options.data().innerForm
      this.parentNameList = []
      this.$refs['innerForm'].resetFields()
      this.isShowDialog = false
    },
    // 弹窗保存
    submit() {
      if (this.innerForm.parentName !== '一级菜单') {
        this.parentNameList.map((el) => {
          if (el.id === this.innerForm.parentName) {
            this.innerForm.parentName = el.label
            this.innerForm.parentId = el.id
          }
        })
      } else {
        this.innerForm.parentId = 0
      }
      this.$refs['innerForm'].validate((value) => {
        if (value) {
          let innerForm = {
            ...this.innerForm,
            appId: this.defaultData.id
          }
          if (this.dialogType === 0) {
            menuRegister(innerForm).then((res) => {
              if (res.data) {
                this.cancel()
                this.axiosTable(this.defaultData.id)
                this.$message.success(res.message)
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            menuUpdate(innerForm).then((res) => {
              if (res.data) {
                this.cancel()
                this.axiosTable(this.defaultData.id)
                this.$message.success(res.message)
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 远程搜索
    querySearch(queryString, cb) {
      var restaurants = this.restaurants
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    // 对比
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        )
      }
    },
    // input选中
    handleSelect(item) {
      this.getDefaultAppList(item.name)
    },
    // 清空输入框
    clearSearch() {
      this.getDefaultAppList()
    },
    // rowClick(row) {
    //   this.innerForm.parentName = row.name
    //   this.innerForm.parentId = row.id
    //   this.innerForm.type = row.type
    // },
    getparentNameList(arr) {
      const res = []
      arr.map((item) => {
        if (item.type === '1') {
          const temp = { ...item, value: item.id, label: item.name }
          if (temp.children) {
            res.push(...this.getparentNameList(temp.children))
          }
          res.push(temp)
        }
      })
      return res
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  .el-col {
    height: 100%;
    .main {
      background: #fff;
      height: 100%;
      border-radius: 4px;
      padding: 8px 12px;
    }
  }
  .left-container {
    .search {
      margin-top: 10px;
      .el-autocomplete {
        width: 100%;
      }
    }
    .list-container {
      height: calc(100% - 96px);
      overflow-y: auto;
      font-size: 14px;
      font-weight: 400;
      margin-top: 16px;
      .list-item {
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 2px 0;
        padding: 0 12px;
        cursor: pointer;
        .oper {
          text-align: right;
          img {
            margin: 0px 5px;
          }
        }
      }

      .active {
        background: #d4eaff;
        color: #1273ce;
        font-weight: 500;
        border-radius: 4px;
      }
    }
  }
  .right-container {
    .main {
      padding: 17px 16px;
      .header-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
          span {
            color: #313133;
            font-size: 16px;
            font-weight: 600;
          }
          .id {
            color: #949699;
            font-size: 14px;
            font-weight: 400;
            margin-left: 16px;
          }
        }
      }
      ::v-deep .el-table {
        margin-top: 16px;
        .current-row {
          background: #d4eaff !important;
        }
        .blue {
          color: #1273ce;
          font-size: 14px;
          margin-left: 10px;
          cursor: pointer;
        }
        .red {
          color: #ff4645;
          font-size: 14px;
          margin-left: 10px;
          cursor: pointer;
        }
      }
      .pagination {
        text-align: right;
        margin-top: 10px;
      }
    }
    .table-col ::v-deep {
      .el-table tr {
        background: #f2f4f7 !important;
      }
      // .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      //   background: #F2F4F7 !important;
      // }
      // .el-table__body tr.current-row > td.el-table__cell {
      //   background: #F2F4F7;
      // }
      .el-table td,
      .el-table th.is-leaf {
        border-bottom: 4px solid #fff !important;
      }
      .el-table tr {
        &:hover {
          background: #f2f4f7;
        }
      }
      .el-table--enable-row-hover .el-table__body tr:hover > td {
        background: #f2f4f7;
      }
      .el-table__expand-icon .el-icon-arrow-right:before {
        content: '\e791';
        /* border: 1px solid #ccc; */
        font-size: 14px;
        color: #1273ce;
      }
      /*2.按钮已点击展开之后的样式是减号带边框*/
      .el-table__expand-icon--expanded .el-icon-arrow-right:before {
        content: '\e791';
        color: #1273ce;
        font-size: 14px;
      }
      .el-button--text:focus,
      .el-button--text:hover {
        color: #1273ce !important;
      }
    }
  }
}
.el-radio {
  margin-right: 6px;
}
</style>