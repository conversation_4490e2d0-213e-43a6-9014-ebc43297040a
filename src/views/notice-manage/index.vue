<template>
  <div class="home-container notice-manage">
    <div class="form-col">
      <el-form size="small" :model="searchForm" inline>
        <el-form-item label="分类">
          <el-select v-model="searchForm.category" placeholder="请选择">
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in categoryList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发布日期">
          <el-date-picker
            v-model="searchForm.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="发布者">
          <el-select v-model="searchForm.sendCode" placeholder="请选择">
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in userList"
              :key="item.healthCareProviderId"
              :label="item.healthCareProviderId"
              :value="item.healthCareProviderId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通知关键词">
          <el-input
            v-model="searchForm.primaryKey"
            placeholder="请输入关键词"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择">
            <el-option label="全部" value=""></el-option>
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="reset">重 置</el-button>
          <el-button type="primary" @click="query">查 询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <div class="text">通知管理</div>
        <div class="tooltip" v-if="selectionList.length > 1">
          您当前共选中
          <span>{{ selectionList.length }}条</span>
          公告
        </div>
        <div>
          <el-button type="primary" size="small" @click="add">新 增</el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        ref="multipleTable"
        row-key="id"
        @selection-change="handleSelectionChange"
        border
        :header-cell-style="{
          'background': '#F2F4F7',
          'color': '#303133',
          'font-weight': 600
        }"
        tooltip-effect="dark"
        width="100%"
      >
        <el-table-column label="通知时间" prop="sendTime" />
        <el-table-column label="标题" prop="title" />
        <el-table-column label="所属分类">
          <template slot-scope="props">
            <span>
              {{ props.row.category == 'notice' ? '通知' : '危急值' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="发布者" prop="sendCode" />
        <el-table-column label="接收方工号" prop="receiveDoctorId" />
        <el-table-column label="接收方" prop="receiveDoctorName" />
        <el-table-column label="状态">
          <template slot-scope="props">
            <span>
              {{
                props.row.status == '0'
                  ? '草稿'
                  : props.row.status == '1'
                  ? '已发送'
                  : props.row.status == '2'
                  ? '未处理'
                  : '已处理'
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="props">
            <span class="blue" @click="detail(props.row)">详情</span>
            <span
              v-if="props.row.category === 'notice' && props.row.status == '0'"
              class="blue"
              @click="edit(props.row)"
            >
              编辑
            </span>
            <span class="red" @click="remove(props.row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNum"
          background
          :page-sizes="[10, 20, 30, 40]"
          size="small"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      :title="isEditing ? '编辑通知' : '新建通知'"
      :visible.sync="isShowDialog"
      width="800px"
      :before-close="cancel"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form :model="innerForm" size="small" label-width="80px" ref="innerForm">
        <el-form-item
          label="标题:"
          prop="title"
          :rules="{ required: true, message: '请输入标题', trigger: 'blur' }"
        >
          <el-input v-model="innerForm.title" placeholder="请输入标题" clearable />
        </el-form-item>
        <el-form-item label="发布内容:" prop="content">
          <Tinymce
            v-if="isShowDialog"
            v-model="innerForm.content"
            :value="innerForm.content"
            :height="300"
          />
        </el-form-item>
        <el-form-item label="接收方" prop="">
          <el-cascader
            v-model="deptIds"
            :options="deptWithUserList"
            :props="{ multiple: true }"
            collapse-tags
            clearable
          ></el-cascader>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取 消</el-button>
        <el-button @click="submit(0)" size="small" type="primary" plain>
          保存为草稿
        </el-button>
        <el-button type="primary" @click="submit(1)" size="small">发 布</el-button>
      </span>
    </el-dialog>
    <!-- 危急值通知详情 -->
    <el-dialog
      :visible.sync="dialogDetail"
      width="50%"
      class="notice"
      @close="closeNotice"
    >
      <div slot="title" class="notice-header">
        <span class="notice-title">通知详情</span>
      </div>

      <notice :info="dialogReceiveData" />
      <div class="notice-foot-buttons">
        <el-button @click="closeNotice">关闭</el-button>
      </div>
    </el-dialog>
    <!-- 通知详情 -->
    <el-dialog
      :visible.sync="noticeDetail"
      width="40%"
      class="notice"
      @close="closeNotice"
    >
      <div slot="title" class="notice-header">
        <span class="notice-title">通知详情</span>
      </div>
      <div class="notice-body-title">{{ dialogReceiveData.title }}</div>
      <div class="notice-body-content" v-html="dialogReceiveData.content"></div>
      <div class="notice-body-time">{{ dialogReceiveData.sendTime }}</div>
      <div class="notice-foot-buttons">
        <el-button @click="closeNotice">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import notice from './Notice'
import {
  announcementList,
  announcementRegister,
  announcementUpdate,
  announcementRemove,
  announcementInfo,
  announcementUploadFile,
  announcementDownLoad,
  getDeptList
} from '@/api/announcement-manage'
import {
  noticeList,
  noticeRegister,
  noticeUpdate,
  noticeRemove,
  noticeInfo,
  getAllUserList,
  getDeptUserList
} from '@/api/notice-manage'
import { getLoginUserName } from '@/utils/auth'
export default {
  components: {
    Tinymce,
    notice
  },
  data() {
    return {
      searchForm: {
        category: '',
        sendCode: '',
        time: [],
        status: '',
        primaryKey: '',
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      pageNum: 1,
      pageSize: 10,
      loading: false,
      tableData: [],
      selectionList: [],
      innerForm: {
        id: '',
        title: '',
        content: '',
        status: '1',
        category: 'notice',
        deptId: '',
        sendCode: getLoginUserName()
      },
      isShowDialog: false,
      nowFile: '',
      fileList: [],
      deptList: [],
      userList: [], // 发布者下拉选项
      categoryList: [
        // 分类下拉选项
        {
          value: 'critical',
          label: '危机值'
        },
        {
          value: 'notice',
          label: '通知'
        }
      ],
      statusList: [
        // 状态下拉选项
        {
          value: '0',
          label: '草稿'
        },
        {
          value: '1',
          label: '已发送'
        },
        {
          value: '2',
          label: '未处理'
        },
        {
          value: '3',
          label: '已处理'
        }
      ],
      selectvalue: '',
      dialogDetail: false, // 危急值通知详情
      noticeDetail: false, // 显示通知详情
      dialogReceiveData: {}, // 选中的单个通知详情
      deptIds: [], // 选中科室id
      deptWithUserList: [],
      isEditing: false
    }
  },
  mounted() {
    this.axiosTable(1)
    this.getApi()
    this.getDeptWithUserList()
  },
  methods: {
    // 重置
    reset() {
      this.searchForm = this.$options.data().searchForm
      this.axiosTable(1)
    },
    // 查询
    query() {
      this.searchForm.pageNum = 1
      this.axiosTable(1)
    },
    // 表格默认请求
    axiosTable() {
      if (!this.searchForm.time) {
        this.searchForm.time = []
      }
      this.loading = true
      const searchForm = {
        category: this.searchForm.category,
        sendCode: this.searchForm.sendCode,
        startTime: this.searchForm.time[0],
        endTime: this.searchForm.time[1],
        status: this.searchForm.status,
        primaryKey: this.searchForm.primaryKey,
        pageNum: this.pageNum,
        pageSize: this.pageSize
      }
      noticeList(searchForm)
        .then(res => {
          this.loading = false
          this.tableData = res.data
          this.total = res.total
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 新增
    add() {
      this.isEditing = false
      this.innerForm.id = ''
      this.innerForm.title = ''
      this.innerForm.content = ''
      this.innerForm.deptId = ''
      this.deptIds = []
      this.isShowDialog = true
    },
    // 编辑
    edit(row) {
      this.isEditing = true
      this.innerForm.id = row.id
      this.innerForm.title = row.title
      this.innerForm.content = row.content
      this.innerForm.status = row.status
      this.deptIds = this.formatDeptIds(row.receiveDoctorCode)
      // this.deptIds.push(row.receiveDoctorCode)
      this.isShowDialog = true
    },
    formatDeptIds(id) {
      let data = []
      let arr = id.split(',')
      arr.map(e => {
        this.deptWithUserList.map(d => {
          d.children.map(c => {
            let r = []
            if (c.value == e) {
              r[0] = d.value
              r[1] = e
              data.push(r)
            }
          })
        })
      })
      console.log(data)
      return data
    },
    // 表格选中
    handleSelectionChange(val) {
      this.selectionList = val
    },
    // sizeChange
    handleSizeChange(size) {
      this.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.pageNum = page
      this.axiosTable(page)
    },
    // 查看详情
    detail(row) {
      noticeInfo({ id: row.id }).then(res => {
        this.dialogReceiveData = res.data
        row.category === 'notice'
          ? (this.noticeDetail = true)
          : (this.dialogDetail = true)
      })
    },
    // 删除
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          noticeRemove({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 移除上传附件
    beforeRemove(file) {
      this.fileList.splice(
        this.fileList.findIndex(el => el.fileId === file.fileId),
        1
      )
    },
    // 文件上传拦截
    beforeUpload(file) {
      if (file.size > 1024 * 1024 * 5) {
        this.$message.warning('文件大小不能超过5M')
        return false
      }
      return true
    },
    // 上传拦截
    httpRequest(file) {
      this.fileList.splice(
        this.fileList.findIndex(e => e.fileName === file.file.name),
        1
      )
      const formData = new FormData()
      formData.append('file', file.file)
      announcementUploadFile(formData).then(res => {
        if (res.data.length) {
          res.data.map(el => {
            this.fileList.push({
              ...el,
              name: el.fileName,
              url: ''
            })
          })
          //
        }
      })
    },
    // 下载上传的文件
    onPreview(file) {
      announcementDownLoad({
        filePath: file.filePath
      }).then(res => {
        if (res.data) {
          let data = res.data
          var blob = new Blob([data], { type: 'application/zip' })
          let url = window.URL.createObjectURL(blob)
          if (window.navigator.msSaveBlob) {
            try {
              window.navigator.msSaveBlob(blob, file.filename)
            } catch (error) {
              console.log(error)
            }
          }
          const link = document.createElement('a')
          link.href = url
          link.download = file.filename
          link.click()
          URL.revokeObjectURL(url)
        }
      })
    },
    // 弹窗提交
    submit(type) {
      this.$refs['innerForm'].validate(value => {
        if (value) {
          this.innerForm.status = type
          this.innerForm.receiveUserIds = this.formatReceiveUserIds(this.deptIds)
          if (this.isEditing) {
            noticeUpdate(this.innerForm).then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.cancel()
                this.axiosTable(this.pageNum)
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            noticeRegister(this.innerForm).then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.cancel()
                this.axiosTable(this.pageNum)
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    formatReceiveUserIds(ids) {
      const che = ids.map(i => {
        return i[1]
      })
      return che
    },
    // 弹框取消
    cancel() {
      this.innerForm = this.$options.data().innerForm
      this.isShowDialog = false
    },
    // 获取所有科室
    async getApi() {
      this.deptList = (await getDeptList()).data
      this.userList = (await getAllUserList()).data
    },
    // 获取所有科室+用户
    async getDeptWithUserList() {
      const data = (await getDeptUserList()).data
      this.deptWithUserList = this.formatDeptWithUserList(data)
    },
    // 获取所有科室+用户
    formatDeptWithUserList(data) {
      const list = data.map(item => {
        let newData = {}
        newData.label = item.dept.deptName
        newData.value = item.dept.deptId
        if (item.users.length) {
          newData.children = item.users.map(user => {
            let o = {}
            o.label = user.name
            o.value = user.pk
            return o
          })
        }

        return newData
      })
      return list
    },
    // 公告权限切换
    groupChange(val) {
      if (val) this.innerForm.deptId = []
    },
    checkPermission(rule, value, cb) {
      if (this.innerForm.permission === 'dept' && !this.innerForm.deptId.length) {
        return cb(new Error('请选择科室'))
      } else {
        cb()
      }
    },
    closeNotice() {
      this.dialogDetail = false
      this.noticeDetail = false
    }
  }
}
</script>

<style lang="scss" scoped>
.notice-manage {
  .form-col {
    padding: 16px 20px 6px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 10px;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        &::before {
          content: '';
          width: 4px;
          height: 14px;
          background: #1273ce;
          display: inline-block;
          margin-right: 4px;
        }
      }
      .tooltip {
        color: #313133;
        font-size: 14px;
        font-weight: 400;
        span {
          color: #559bdd;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
      .blue {
        color: #1273ce;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
      .red {
        color: #ff4645;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
  .custom-select {
    margin-left: 10px;
  }
}
.notice-foot-buttons {
  text-align: right;
  margin: 16px 20px 0 0;
}
.notice-title {
  color: #303133;
  font-weight: bold;
}
.notice-body-title {
  margin-top: 20px;
  text-align: center;
  color: #303133;
  font-weight: bold;
  font-size: 24px;
}
.notice-body-time {
  text-align: right;
  padding: 18px;
  color: #999;
  font-size: 16px;
}
</style>
<style lang="scss">
.notice-body-content {
  padding: 20px;
  color: #555;
  font-size: 16px;
  p {
    img {
      width: 100% !important;
      height: 100% !important;
    }
  }
}
</style>
