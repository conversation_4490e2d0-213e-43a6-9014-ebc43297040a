<template>
  <div class="notice-card" :class="{ read: info.status === 1 }">
    <div class="text">
      <div class="patient-info">
        <div class="name">
          {{ patient.patientName }}
          <span>{{ patient.dangerName }}异常</span>
        </div>
        <div class="record-time">
          <span class="label">危急值记录时间:</span>
          <span>{{ patient.reportTime | formatYear }}</span>
        </div>
      </div>
      <table class="templateTable">
        <tr>
          <td class="label">患者姓名:</td>
          <td>{{ patient.patientName }}</td>
          <td class="label">性别:</td>
          <td>{{ patient.sexName }}</td>
        </tr>
        <tr>
          <td class="label">年龄:</td>
          <td>{{ patient.age }}</td>
          <td class="label">病人ID:</td>
          <td>{{ patient.patientId }}</td>
        </tr>
        <tr>
          <td class="label">科室:</td>
          <td>{{ patient.deptName }}</td>
          <td class="label">床位号:</td>
          <td>{{ patient.bedNo }}</td>
        </tr>
        <tr>
          <td class="label">危急值编号:</td>
          <td>{{ patient.crisisValueCode }}</td>
        </tr>
        <template v-for="item in patient.items">
          <tr class="crisis-info">
            <td class="label">危急值:</td>
            <td class="danger" colspan="2">{{ item.result }}</td>
            <td class="label">参考值:</td>
            <td>{{ item.referenceRange }}</td>
          </tr>
          <tr class="crisis-info">
            <td class="label" colspan="2">危急值处理状态:</td>
            <td>
              {{ item.status == '1' ? '已处理' : '未处理' }}
            </td>
          </tr>
        </template>
        <tr>
          <td class="label">报告ID:</td>
          <td>{{ patient.reportNo }}</td>
          <td class="label">危急值描述:</td>
          <td>{{ patient.content }}</td>
        </tr>
        <tr>
          <td class="label">上报人:</td>
          <td>{{ patient.reportDoctorName }}</td>
          <td class="label">接受人:</td>
          <td>{{ patient.receiveDoctorName }}</td>
          <td class="label">处理人:</td>
          <td>{{ patient.operateName }}</td>
        </tr>
        <tr>
          <td class="label">上报时间:</td>
          <td colspan="2">
            {{ patient.reportTime }}
          </td>
          <td class="label">处理时间:</td>
          <td colspan="2">
            {{ patient.operateTime }}
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
// import { changeNoticeStatus } from '@/api'
import Moment from 'moment'
Moment.locale('zh-cn')
export default {
  filters: {
    formatYear(time) {
      return Moment(time).format('lll')
    }
  },
  props: {
    ind: {
      default: () => 0
    },
    info: {
      default: () => {}
    },
    open: {
      type: Boolean,
      default: () => false
    }
  },
  computed: {
    patient() {
      return this.info
    }
  },
  mounted() {},
  methods: {
    // changeStatus(info) {
    //   const { status, id } = info
    //   if (!status) {
    //     changeNoticeStatus(id).then(res => {
    //       if (res.result === 'success') {
    //         this.$message.success('已将此条消息状态更改为已读')
    //       }
    //     })
    //   }
    // }
  }
}
</script>

<style lang="scss" scoped>
.notice-card {
  margin: 0;
  padding: 0 20px;
  background: #fff;
  &.read {
    &,
    .name {
      color: #abbbd4;
      ::before {
        display: none;
      }
    }
  }
}
.patient-info {
  position: relative;
  display: flex;
  align-items: center;
  line-height: 24px;
  padding-left: 10px;
  margin-bottom: 16px;
  .name {
    font-size: 16px;
    color: #313133;
    font-weight: bold;
    position: relative;
    span {
      position: relative;
      color: #fe5151;
    }
    margin: 0 10px 0 0;
  }
  .record-time {
    font-size: 16px;
    .label {
      margin-right: 10px;
    }
  }
}
.templateTable {
  width: 100%;
  overflow: hidden;
  word-break: break-all;
  white-space: normal;
  line-height: 1.5;
  tr {
    td {
      width: 12.5%;
      font-size: 14px;
      line-height: 28px;
      border-right: none;
    }
    td.time {
      width: 20%;
    }
  }
  .label {
    font-size: 14px;
    color: #636466;
  }
  .danger {
    font-size: 18px;
    color: #ec7562;
  }
}

.text {
  position: relative;
  margin: 16px 0 0 0;
}

.btn {
  z-index: 9;
  clear: both;
  float: right;
  cursor: pointer;
  position: relative;
}
.btn::after {
  content: '';
  display: block;
  width: 34px;
  height: 34px;
}
.exp {
  visibility: hidden;
}
.exp:checked + .text > .btn::after {
  content: '';
  display: block;
  width: 34px;
  height: 34px;
}
// .exp:checked + .notice-card {
//   border:
// }
</style>
