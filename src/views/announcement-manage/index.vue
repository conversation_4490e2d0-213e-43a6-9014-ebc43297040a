<template>
  <div class="home-container notice-manage">
    <div class="form-col">
      <el-form size="small" :model="searchForm" inline>
        <el-form-item label="标题">
          <el-input v-model="searchForm.title" placeholder="请输入标题" clearable />
        </el-form-item>
        <el-form-item label="发布日期">
          <el-date-picker
            v-model="searchForm.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="reset">重 置</el-button>
          <el-button type="primary" @click="query">查 询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <div class="text">公告列表</div>
        <div class="tooltip" v-if="selectionList.length > 1">
          您当前共选中
          <span>{{ selectionList.length }}条</span>
          公告
        </div>
        <div>
          <el-button type="primary" size="small" @click="add">新 增</el-button>
          <el-button class="el-button--custom" size="small" @click="removes">
            批量删除
          </el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        ref="multipleTable"
        row-key="id"
        @selection-change="handleSelectionChange"
        border
        :header-cell-style="{
          'background': '#F2F4F7',
          'color': '#303133',
          'font-weight': 600
        }"
        tooltip-effect="dark"
        width="100%"
        highlight-current-row
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column label="标题" prop="title" />
        <el-table-column label="发布人" prop="createUser" />
        <el-table-column label="发布日期" prop="createTime" />
        <el-table-column label="操作">
          <template slot-scope="props">
            <span class="blue" @click="detail(props.row)">查看详情</span>
            <span class="red" @click="remove(props.row)">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageNum"
          background
          :page-sizes="[10, 20, 30, 40]"
          size="small"
          :page-size="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      :title="dialogType === 0 ? '新增公告' : '公告详情'"
      :visible.sync="isShowDialog"
      width="800px"
      :before-close="cancel"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form :model="innerForm" size="small" label-width="80px" ref="innerForm">
        <el-form-item
          label="标题:"
          prop="title"
          :rules="{ required: true, message: '请输入标题', trigger: 'blur' }"
        >
          <el-input v-model="innerForm.title" placeholder="请输入标题" clearable />
        </el-form-item>
        <el-form-item label="发布内容:" prop="content">
          <Tinymce
            v-if="isShowDialog"
            v-model="innerForm.content"
            :value="innerForm.content"
            :height="300"
          />
        </el-form-item>
        <el-form-item label="上传附件:">
          <el-upload
            ref="upload"
            name="file"
            action=""
            :before-remove="beforeRemove"
            :http-request="httpRequest"
            :file-list="fileList"
            :on-preview="onPreview"
            :before-upload="beforeUpload"
            accept=".pdf, .doc, .docx, .xls, .xlsx"
          >
            <el-button type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              *仅限上传pdf、.docs、.xlsx 文件格式，且大小不超过5M。
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item
          label="公告权限"
          prop="permission"
          :rules="[
            { required: true, message: '请选择公告权限', trigger: 'blur' },
            {
              validator: checkPermission
            }
          ]"
        >
          <el-radio-group v-model="innerForm.permission" @change="groupChange">
            <el-radio label="all">全院</el-radio>
            <el-radio label="dept">科室</el-radio>
          </el-radio-group>
          <el-select
            v-model="innerForm.deptId"
            :disabled="innerForm.permission === 'all'"
            multiple
            clearable
            collapse-tags
            class="custom-select"
          >
            <el-option
              v-for="item in deptList"
              :key="item.deptId"
              :label="item.deptName"
              :value="item.deptId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取 消</el-button>
        <el-button type="primary" @click="submit" size="small">发 布</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Tinymce from '@/components/Tinymce'
import {
  announcementList,
  announcementRegister,
  announcementUpdate,
  announcementRemove,
  announcementInfo,
  announcementUploadFile,
  announcementDownLoad,
  getDeptList
} from '@/api/announcement-manage'
import { getLoginName } from '@/utils/auth'
export default {
  components: {
    Tinymce
  },
  data() {
    return {
      searchForm: {
        title: '',
        time: ['', ''],
        pageSize: 10,
        pageNum: 1
      },
      total: 0,
      loading: false,
      tableData: [],
      selectionList: [],
      innerForm: {
        title: '',
        content: '',
        filePath: '',
        permission: 'all',
        deptId: []
      },
      isShowDialog: false,
      nowFile: '',
      dialogType: 0, // 0新增 1编辑
      fileList: [],
      deptList: []
    }
  },
  mounted() {
    this.axiosTable(1)
    this.getApi()
  },
  methods: {
    // 重置
    reset() {
      this.searchForm = this.$options.data().searchForm
      this.axiosTable(1)
    },
    // 查询
    query() {
      this.searchForm.pageNum = 1
      this.axiosTable(1)
    },
    // 表格默认请求
    axiosTable(page = 1) {
      this.loading = true
      if (!this.searchForm.time) this.searchForm.time = ['', '']
      let searchForm = {
        ...this.searchForm,
        startTime: this.searchForm.time[0],
        endTime: this.searchForm.time[1],
        pageNum: page
      }
      delete searchForm.time
      announcementList(searchForm)
        .then(res => {
          this.loading = false
          this.tableData = res.data
          this.total = res.total
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 新增
    add() {
      this.fileList = []
      this.dialogType = 0
      this.isShowDialog = true
      this.innerForm.content = ''
      this.$refs.upload.clearFiles()
    },
    // 表格选中
    handleSelectionChange(val) {
      this.selectionList = val
    },
    // sizeChange
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.searchForm.pageNum = page
      this.axiosTable(page)
    },
    // 查看详情
    detail(row) {
      this.dialogType = 1
      this.isShowDialog = true
      this.innerForm = {
        ...row,
        permission: row.scope === 'all' ? 'all' : 'dept',
        deptId: row.scope === 'all' ? [] : row.scope.split(',')
      }
      announcementInfo({ id: row.id }).then(res => {
        this.fileList = res.data.map(el => {
          return {
            ...el,
            name: el.fileName,
            url: el.filePath,
            fileId: el.id
          }
        })
      })
    },
    // 删除
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          announcementRemove({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 批量删除
    removes() {
      if (this.selectionList.length) {
        let ids = this.selectionList.map(el => el.id).join()
        this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            announcementRemove({
              id: ids
            }).then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.axiosTable(this.searchForm.pageNum)
                this.$refs['multipleTable'].clearSelection()
              } else {
                this.$message.error(res.message)
              }
            })
          })
          .catch(() => {})
      } else {
        this.$message.warning('请先勾选需要删除的选项')
      }
    },
    // 移除上传附件
    beforeRemove(file) {
      this.fileList.splice(
        this.fileList.findIndex(el => el.fileId === file.fileId),
        1
      )
    },
    // 文件上传拦截
    beforeUpload(file) {
      if (file.size > 1024 * 1024 * 5) {
        this.$message.warning('文件大小不能超过5M')
        return false
      }
      return true
    },
    // 上传拦截
    httpRequest(file) {
      this.fileList.splice(
        this.fileList.findIndex(e => e.fileName === file.file.name),
        1
      )
      const formData = new FormData()
      formData.append('file', file.file)
      announcementUploadFile(formData).then(res => {
        if (res.data.length) {
          res.data.map(el => {
            this.fileList.push({
              ...el,
              name: el.fileName,
              url: ''
            })
          })
          //
        }
      })
    },
    // 下载上传的文件
    onPreview(file) {
      announcementDownLoad({
        filePath: file.filePath
      }).then(res => {
        if (res.data) {
          let data = res.data
          var blob = new Blob([data], { type: 'application/zip' })
          let url = window.URL.createObjectURL(blob)
          if (window.navigator.msSaveBlob) {
            try {
              window.navigator.msSaveBlob(blob, file.filename)
            } catch (error) {
              console.log(error)
            }
          }
          const link = document.createElement('a')
          link.href = url
          link.download = file.filename
          link.click()
          URL.revokeObjectURL(url)
        }
      })
    },
    // 弹窗提交
    submit() {
      this.$refs['innerForm'].validate(value => {
        console.log(value)
        if (value) {
          let fileIds = ''
          if (this.fileList.length) {
            fileIds = this.fileList.map(el => el.fileId).join()
          }
          let form = {
            ...this.innerForm,
            fileIds,
            createUser: getLoginName(),
            scope:
              this.innerForm.permission === 'dept' ? this.innerForm.deptId.join() : 'all'
          }
          delete form.deptId
          delete form.permission
          if (this.dialogType === 0) {
            announcementRegister(form).then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.cancel()
                this.axiosTable(this.searchForm.pageNum)
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            announcementUpdate(form).then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.cancel()
                this.axiosTable(this.searchForm.pageNum)
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 弹框取消
    cancel() {
      this.innerForm = this.$options.data().innerForm
      this.$refs['innerForm'].resetFields()
      this.$refs.upload.clearFiles()
      this.isShowDialog = false
    },
    // 获取所有科室
    async getApi() {
      this.deptList = (await getDeptList()).data
    },
    // 公告权限切换
    groupChange(val) {
      if (val) this.innerForm.deptId = []
    },
    checkPermission(rule, value, cb) {
      if (this.innerForm.permission === 'dept' && !this.innerForm.deptId.length) {
        return cb(new Error('请选择科室'))
      } else {
        cb()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.notice-manage {
  .form-col {
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        &::before {
          content: '';
          width: 4px;
          height: 14px;
          background: #1273ce;
          display: inline-block;
          margin-right: 4px;
        }
      }
      .tooltip {
        color: #313133;
        font-size: 14px;
        font-weight: 400;
        span {
          color: #559bdd;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
      .blue {
        color: #1273ce;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
      .red {
        color: #ff4645;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
  .custom-select {
    margin-left: 10px;
  }
}
</style>
