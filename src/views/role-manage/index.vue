<template>
  <div class="home-container role-manage">
    <el-row class="container" :gutter="20">
      <el-col :span="6" class="left-container">
        <div class="main">
          <div class="header">
            <!-- <span class="title">阜阳第五人民医院</span> -->
            <el-button size="small" type="primary" @click="add">新增</el-button>
          </div>
          <div class="search">
            <el-autocomplete
              prefix-icon="el-icon-search"
              size="small"
              v-model="roleName"
              clearable
              :fetch-suggestions="querySearch"
              @select="handleSelect"
              @clear="clearSearch"
            />
          </div>
          <div class="list-container" v-if="listArr.length">
            <div
              class="list-item"
              v-for="(item, index) in listArr"
              :key="index"
              @click="choose(item, index)"
              :class="{ active: activeIndex === index }"
            >
              <div class="title">{{ item.name }}</div>
              <div class="oper">
                <img
                  src="@/assets/images/role-manage/edit.png"
                  alt=""
                  @click.stop="edit(item)"
                />
                <img
                  src="@/assets/images/role-manage/delete.png"
                  alt=""
                  @click.stop="remove(item)"
                />
              </div>
            </div>
          </div>
          <div v-else class="no-message">暂无数据</div>
        </div>
      </el-col>
      <el-col :span="18" class="right-container">
        <div class="main">
          <div class="header-info">
            <div class="title">{{ defaultData.name }}</div>
            <div class="id">
              <span class="label">ID：</span>
              <span class="text">{{ defaultData.id }}</span>
            </div>
            <div class="role-des">
              <span class="label">角色描述：</span>
              <span class="text">{{ defaultData.info }}</span>
            </div>
          </div>
          <el-row :gutter="20" class="role-detail">
            <el-col :span="9">
              <div class="apps-container scroll">
                <div class="title">平台应用菜单功能权限</div>
                <el-tree
                  ref="systemTree"
                  show-checkbox
                  :data="systemTree"
                  node-key="id"
                  :props="defaultProps"
                  default-expand-all
                  v-if="systemTree.length"
                />
                <div v-else class="no-message">暂无数据</div>
              </div>
            </el-col>
            <el-col :span="9">
              <div class="portal-container scroll">
                <div class="title">门户应用单点登录</div>
                <el-checkbox-group v-model="checkList" v-if="checkBoxList.length">
                  <el-checkbox
                    :label="item.appId"
                    v-for="(item, index) in checkBoxList"
                    :key="index"
                  >
                    {{ item.appName }}
                  </el-checkbox>
                </el-checkbox-group>
                <div class="no-message" v-else>暂无数据</div>
              </div>
            </el-col>
          </el-row>
          <div class="save">
            <el-button type="primary" size="small" @click="save">保 存</el-button>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      :title="dialogType === 0 ? '新增角色' : '编辑角色'"
      :visible.sync="isShowDialog"
      width="25%"
      :before-close="cancel"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="innerForm"
        size="small"
        label-width="120px"
        :rules="innerFormRules"
        ref="innerForm"
      >
        <el-form-item label="角色名称:" prop="name">
          <el-input v-model="innerForm.name" />
        </el-form-item>
        <el-form-item label="角色描述:" prop="info">
          <el-input v-model="innerForm.info" />
        </el-form-item>
        <el-form-item label="上级角色:" prop="parentId">
          <el-select v-model="innerForm.parentId">
            <el-option
              v-for="(item, index) in roleParentListArr"
              :key="index"
              :value="item.id"
              :label="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="显示排序:" prop="sort">
          <el-input v-model="innerForm.sort" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取 消</el-button>
        <el-button type="primary" @click="submit" size="small">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  roleList,
  roleRegister,
  roleUpdate,
  roleRemove,
  roleParentList,
  appList,
  roleMenuList,
  roleAppList,
  rolePermissionSave
} from '@/api/role-manage'
export default {
  data() {
    return {
      roleName: '',
      listArr: [],
      activeIndex: 0,
      defaultData: {},
      systemTree: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      hasRights: [], // 拥有的平台应用权限
      expandRights: [], // 平台应用权限需要展开的keys
      checkBoxList: [],
      checkList: [],
      isShowDialog: false,
      dialogType: 0,
      innerForm: {
        roleName: '',
        roleDes: '',
        viewSort: ''
      },
      innerFormRules: {
        name: { required: true, message: '请输入角色名称', trigger: 'blur' },
        info: { required: true, message: '请输入角色描述', trigger: 'blur' },
        parentId: {
          required: true,
          message: '请选择上级角色',
          trigger: 'blur'
        },
        sort: { required: true, message: '请输入显示排序', trigger: 'blur' }
      },
      restaurants: [],
      roleParentListArr: [],
      defaultCheckedKeys: [] // systemTree 拿到第一层的ID,第二层的ID 在保存的时候需要剔除
    }
  },
  async mounted() {
    let list = await this.getRoleList('')
    this.getRoleParentList()
  },
  watch: {
    listArr: {
      handler(n) {
        if (n.length) {
          this.getAppList()
        } else {
          this.systemTree = []
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 获取默认的角色列表
    getRoleList(roleName) {
      return new Promise((resolve, reject) => {
        roleList(roleName).then(res => {
          if (res.data.length) {
            this.listArr = res.data
            this.defaultData = this.listArr[0]
            this.restaurants = res.data.map(el => {
              return {
                ...el,
                value: el.name
              }
            })
            this.getRoleAppList(this.defaultData)
            // this.getRoleMenuList(this.defaultData)
          } else {
            this.listArr = []
            this.restaurants = []
            this.defaultData = {}
            this.checkBoxList = []
            this.systemTree.map(el => {
              el.children.map(item => {
                item.children = []
              })
            })
          }
          resolve(this.listArr)
        })
      })
    },
    // 获取上级角色
    getRoleParentList() {
      roleParentList().then(res => {
        this.roleParentListArr = res.data
      })
    },
    // 远程搜索
    querySearch(queryString, cb) {
      var restaurants = this.restaurants
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    // 对比
    createFilter(queryString) {
      return restaurant => {
        return restaurant.name.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    }, // input选中
    handleSelect(item) {
      this.getRoleList(item.name)
    },
    // 清空输入框
    clearSearch() {
      this.getRoleList()
    },
    // 编辑图标
    edit(row) {
      this.dialogType = 1
      this.isShowDialog = true
      this.innerForm = {
        ...row
      }
    },
    // 移除
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          roleRemove({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.getRoleList('')
              this.getRoleParentList()
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 新增
    add() {
      this.dialogType = 0
      this.isShowDialog = true
    },
    // 弹窗保存
    submit() {
      this.$refs['innerForm'].validate(value => {
        if (value) {
          if (this.dialogType === 0) {
            roleRegister(this.innerForm).then(res => {
              if (res.data) {
                this.$message.success(res.message)
                this.cancel()
                this.getRoleList()
                this.getRoleParentList()
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            roleUpdate(this.innerForm).then(res => {
              if (res.data) {
                this.$message.success(res.message)
                this.cancel()
                this.getRoleList()
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 弹窗关闭
    cancel() {
      this.innerForm = this.$options.data().innerForm
      this.$refs['innerForm'].resetFields()
      this.isShowDialog = false
    },
    // 左侧选中
    choose(item, index) {
      this.activeIndex = index
      this.defaultData = item
      this.checkList = []
      this.$refs['systemTree'].setCheckedKeys([])
      this.defaultCheckedKeys = []
      this.getRoleMenuList(item)
      this.getRoleAppList(item)
    },
    // 获取所有的APPlist
    getAppList() {
      this.systemTree = [
        {
          id: 0,
          label: '集成平台',
          children: []
        }
      ]

      appList({ pageNum: 1, pageSize: 1000 })
        .then(res => {
          if (res.data.length) {
            res.data.map(el => {
              if (el.appSign === '1') {
                this.systemTree[0].children.push({
                  ...el,
                  label: el.name,
                  children: []
                })
              } else {
                this.systemTree[1].children.push({
                  ...el,
                  label: el.name,
                  children: []
                })
              }
            })
          }
        })
        .then(() => {
          this.getRoleMenuList(this.defaultData)
        })
    },
    // 保存
    save() {
      let menuIds = this.$refs['systemTree'].getCheckedKeys()
      let parentIds = this.$refs['systemTree'].getHalfCheckedKeys()
      let newArr = Array.from(new Set([...menuIds, ...parentIds]))
      let tempIds = []
      if (menuIds) {
        newArr.map(el => {
          if (typeof el === 'string') {
            if (el.includes('only')) {
              tempIds.push(el.split('only')[0])
            }
          }
        })
        tempIds = tempIds.join()
      }
      let appIds = this.checkList.join()
      let roleId = this.defaultData.id
      if (this.checkBoxList && this.checkBoxList.length) {
        const containsAppSign = this.checkBoxList.some(el =>
          this.checkList.some(item => el.appId === item && el.appSign === '1')
        )
        if (containsAppSign) {
          if (tempIds && appIds && roleId) {
            this.setRolePermissionSave(tempIds,appIds,roleId)
          } else {
            this.$message.warning(
              '请选择需要绑定的角色，平台应用菜单功能权限，以及门户应用信息！'
            )
          }
        } else {
          if(tempIds){
            this.$message.warning(
              '请取消绑定平台应用菜单功能权限，第三方应用无需绑定！'
            )
          }else{
            this.setRolePermissionSave(tempIds,appIds,roleId)
          }
        }
      }
    },
    // 格式化返回数据
    formatMenu(arr) {
      const res = []
      arr.forEach(el => {
        delete el.image
        const temp = {
          ...el,
          label: el.menuName,
          id: el.menuId + 'only'
        }
        if (temp.children) {
          temp.children = this.formatMenu(temp.children)
          if (temp.checked === true && temp.type === '2') {
            this.defaultCheckedKeys.push(temp.id)
          }
        }
        res.push(temp)
      })
      this.$nextTick(() => {
        this.$refs['systemTree'].setCheckedKeys(this.defaultCheckedKeys)
      })
      return res
    },
    // 获取默认菜单列表
    getRoleMenuList(item) {
      roleMenuList({
        roleId: item.id
      }).then(res => {
        if (res.data) {
          let temp = res.data
          this.systemTree.map(el => {
            if (el.id === 0) {
              el.children.map(item => {
                for (const key in temp) {
                  if (item.id == key) {
                    item.children = []
                    item.children.push(...this.formatMenu(temp[key]))
                  }
                }
              })
            }
          })
        }
      })
    },
    // 获取右侧数据
    getRoleAppList(item) {
      roleAppList({ roleId: item.id }).then(res => {
        this.checkBoxList = res.data
        res.data.map(el => {
          if (el.checked) {
            this.checkList.push(el.appId)
          }
        })
      })
    },
    setRolePermissionSave(tempIds, appIds, roleId) {
      rolePermissionSave({ menuIds: tempIds, appIds, roleId }).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  .el-col {
    height: 100%;
    .main {
      background: #fff;
      height: 100%;
      border-radius: 4px;
      padding: 8px 12px;
    }
  }
  .left-container {
    .search {
      .el-autocomplete {
        width: 100%;
      }
    }
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        color: #313133;
        font-size: 16px;
        font-weight: 600;
      }
    }
    .search {
      margin-top: 10px;
    }
    .list-container {
      height: calc(100% - 96px);
      overflow-y: auto;
      font-size: 14px;
      font-weight: 400;
      margin-top: 16px;
      .list-item {
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 2px 0;
        padding: 0 12px;
        cursor: pointer;
        .oper {
          text-align: right;
          img {
            margin: 0px 5px;
          }
        }
      }

      .active {
        background: #d4eaff;
        color: #1273ce;
        font-weight: 500;
        border-radius: 4px;
      }
    }
  }
  .right-container {
    .main {
      padding: 17px 16px;
    }
    .header-info {
      display: flex;
      align-items: center;
      .title {
        color: #313133;
        font-size: 16px;
        font-weight: 600;
      }
      .id,
      .role-des {
        margin: 0px 10px;
        font-size: 14px;
        font-weight: 400;
        .label {
          color: #949699;
        }
        .text {
          color: #313133;
        }
      }
    }
    .role-detail {
      height: calc(100% - 153px);
      margin-top: 17px;
      .el-col {
        height: 100%;
        // .scroll {
        //   height: 100%;
        //   box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
        //   border-radius: 4px;
        //   overflow-y: auto;
        //   &::-webkit-scrollbar {
        //     width: 6px;
        //     height: 0px;
        //     margin-right: 2px;
        //   }

        //   &::-webkit-scrollbar-thumb {
        //     border-radius: 10px;
        //     box-shadow: inset 6px 0 0px #9ec1e3;
        //     background: rgba(199, 201, 204, 0.8);
        //   }
        // }
        .apps-container {
          padding: 16px 20px;
          border-top: 4px solid #218ef5;
          .title {
            font-size: 16px;
            font-weight: 600;
            color: #313133;
            text-align: center;
          }
          .el-tree {
            margin-top: 24px;
          }
        }
        .portal-container {
          padding: 16px 36px;
          border-top: 4px solid #febf54;
          .title {
            font-size: 16px;
            font-weight: 600;
            color: #313133;
            text-align: center;
          }
          .el-checkbox-group {
            display: flex;
            flex-wrap: wrap;
            flex-direction: column;
            ::v-deep.el-checkbox {
              height: 30px;
              line-height: 30px;
              .el-checkbox__label {
                color: #313133;
                font-weight: 500;
              }
              .el-checkbox__input.is-checked + .el-checkbox__label {
                color: #313133;
              }
            }
          }
        }
      }
    }
    .save {
      margin-top: 24px;
      text-align: center;
    }
  }
  .no-message {
    color: #313133;
    text-align: center;
    padding: 20px 0;
    font-weight: 500;
  }
}
</style>
