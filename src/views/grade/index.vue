<template>
  <div class="home-container yspf">
    <div class="form-col">
      <!-- <el-button type="primary" plain size="mini" @click="add">新增</el-button> -->
      <el-form size="small" :model="searchForm" inline>
        <el-form-item label="选择机构">
          <el-select
              v-model="searchForm.orgCode"
              clearable
              placeholder="请选择机构"
              filterable
              prop="doctorInfoOrgCode"
            >
              <el-option
                v-for="item in orgItems"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              ></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="searchForm.keyword" placeholder="请输入姓名查找" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="query">查 询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <div class="text">医生从业不良积分管理</div>
        <div>
          <el-button class="el-button--custom" size="small" @click="exportTable">
            导 出
          </el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        border
        :header-cell-style="{
          'background': '#F2F4F7',
          'color': '#303133',
          'font-weight': 600
        }"
        tooltip-effect="dark"
        width="100%"
        highlight-current-row
      >
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column label="姓名" prop="name" />
        <el-table-column label="手机号码" prop="phoneNumber" />
        <el-table-column label="身份证号" prop="identityNo" />
        <el-table-column label="机构名称" prop="orgName" />
        <el-table-column label="机构编码" prop="orgCode" />
        <el-table-column label="职业级别" prop="practiceLevel" />
        <el-table-column label="职业类别" prop="practiceType" />
        <el-table-column label="当前分数" prop="score" />
        <el-table-column label="更新时间" prop="lastUpdateTime" />
        <el-table-column label="操作" width="200">
          <template slot-scope="props">
            <span class="blue" @click="edit(props.row)">评分</span>
            <span class="blue" @click="showHistory(props.row)">评分历史</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageNum"
          background
          size="small"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      title="评分"
      :visible.sync="isShowDialog"
      width="700px"
      :before-close="cancel"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div class="edit-form-wrap">
        <el-form ref="form" :model="form" label-width="150px" size="small" :inline="true">
          <div class="doctor-info">
            <div class="info-item">
              <span class="info-label">医生姓名</span>
              <span class="info-value">{{ row.name }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">身份证号</span>
              <span class="info-value">{{ row.identityNo}}</span>
            </div>
            <div class="info-item">
              <span class="info-label">所属机构</span>
              <span class="info-value">{{row.orgName}}</span>
            </div>
            <div class="info-item">
              <span class="info-label">当前分数</span>
              <span class="info-value">{{row.score}}</span>
            </div>
            <div class="grade-switch">
              <el-radio-group v-model="radio1" size="small">
                <el-radio-button label="加分" ></el-radio-button>
                <el-radio-button label="减分"></el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div v-if="radio1=='加分'">
            <el-form-item label="加分">
              <el-input-number
                v-model="form.score"
                @change="handleChange"
                :min="1"
                :max="10"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="加分描述">
              <el-input type="textarea" v-model="form.reason"></el-input>
            </el-form-item>
          </div>

          <div v-if="radio1=='减分'">
            <el-form-item label="减分">
              <el-input-number
                v-model="form.score1"
                @change="handleChange1"
                :min="1"
                :max="10"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="减分描述">
              <el-input type="textarea" v-model="form.reason1"></el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="onSubmit" size="small">提 交</el-button>
        <el-button @click="cancel" size="small">重 置</el-button>
      </span>
    </el-dialog>
    <!-- 评分历史 -->
    <el-dialog
      title="评分历史"
      :visible.sync="isShowHistory"
      width="700px"
      :before-close="cancel1"
    >
      <div class="history-form-wrap">
        <div class="doctor-info">
          <div class="info-item">
              <span class="info-label">医生姓名</span>
              <span class="info-value">{{ row.name }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">身份证号</span>
              <span class="info-value">{{ row.identityNo}}</span>
            </div>
            <div class="info-item">
              <span class="info-label">所属机构</span>
              <span class="info-value">{{row.orgName}}</span>
            </div>
            <div class="info-item">
              <span class="info-label">当前分数</span>
              <span class="info-value">{{row.score}}</span>
            </div>
        </div>
        <div class="doctor-history" v-for="item in historyList" :key="item.id">
          <div class="history-date">{{ item.createTime }}</div>
          <div class="history-item">
            <div class="history-type">
              <span class="history-label">事件类型</span>
              <span class="history-value">{{ item.eventType }}</span>
              <span class="history-label">分数变化</span>
              <span class="history-value">{{ item.score }}</span>
            </div>
            <div class="history-reason">
              <span class="history-label">评分理由</span>
              <span class="history-value">{{item.reason}}</span>
            </div>
            <div class="history-marker">
              <span class="history-label">评分人</span>
              <span class="history-value">{{ item.createBy }}</span>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="isShowHistory=false" size="small">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { orgList } from '@/api/structure'
import { agencies, record ,score ,detailId,grade } from '@/api/records'
import { resetPwd } from '@/api/user-manage'
import moment from 'moment'
export default {
  data() {
    return {
      // ---健康卡---
      loading: false,
      isShowDialog: false,
      isShowHistory: false,
      tableData: [
      ],
      row: {},
      radio1: '加分',
      historyList: [],
      total: 0,
      pageSize: 10,
      pageNum: 1,
      depts: [],
      searchForm: {
        keyword: '',
        hospitalId: '',
        date: []
      },
      orgItems: [],
      form: {
        score: 1,
        reason: '',
        score1: 1,
        reason1: '',
      }
    }
  },
  mounted() {
    this.query()
  },
  created() {
    this.getAgencies()
    this.getOrgList()
  },
  methods: {
    onSubmit(row) {
      grade({
        doctorId: this.row.doctorId,
        eventType: this.radio1 == '加分' ? 1 : 0,
        score: this.radio1 == '加分' ? this.form.score :  this.form.score1,
        reason: this.radio1 == '加分' ? this.form.reason :  this.form.reason1,
      }).then(res => {
        this.isShowDialog = false
        this.query()
      })
      
    },
    handleChange() {

    },
    handleChange1() {

    },
    getOrgList() {
      orgList({
        pageSize: 999999999,
        pageNum: 1,
      }).then(res => {
        this.orgItems = res.data.list
      })
    },
    // 查询
    query() {
      this.pageNum = 1
      this.axiosTable()
    },
    // 表格默认请求
    axiosTable() {
      this.loading = true
      const data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        orgCode: this.searchForm.orgCode,
        keyword: this.searchForm.keyword,
      }
      score(data)
        .then(res => {
          this.tableData = res.data.list
          this.loading = false
          this.total = res.data.total
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 新增
    add() {
      this.isShowDialog = true
    },

    showHistory(row) {
      detailId(row.doctorId).then(res=>{
        console.log(res)
        res.data.historyList.forEach((item)=>{
          if (item.eventType == 1) {
            item.eventType = '加分'
            item.score = '+' + item.score
          } else {
            item.eventType = '减分'
            item.score = '-' + item.score
          }
        })
        this.historyList = res.data.historyList
        if (this.historyList.length == 0) {
          this.$message.warning('暂无数据')
          return 
        }
        this.row = row
        this.isShowHistory = true
      })

    },
    // sizeChange
    handleSizeChange(size) {
      this.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.pageNum = page
      this.axiosTable(page)
    },
    // 编辑
    edit(row) {
      this.row = row
      this.form = {
        score: 1,
        reason: '',
        score1: 1,
        reason1: '',
      }
      this.isShowDialog = true
    },
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          appRemove({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 导出
    exportTable() {
      const header = ['name', 'phoneNumber', 'identityNo', 'orgName', 'orgCode', 'practiceLevel','practiceType','score']
      const data = this.formatJson(header, this.tableData)
      import('@/utils/export2excel').then(excel => {
        excel.export_json_to_excel({
          header: ['姓名', '手机号', '身份证号', '机构名称', '机构编码', '职业级别','职业类别','当前分数'],
          data,
          filename: '医生从业不良积分管理',
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    // 弹窗提交
    submit() {
      this.$refs['innerForm'].validate(value => {
        if (value) {
        } else {
          return false
        }
      })
    },
    // 弹框取消
    cancel() {
      this.isShowDialog = false
    },
// 弹框取消
    cancel1() {
      this.isShowHistory = false
    },
    // 自定义上传图片
    httpRequest(file) {
      if (file.file.size > 10 * 1024) {
        this.$message.warning('上传图片大小不能超过10kb!')
      } else {
        let that = this
        const reader = new FileReader()
        reader.readAsDataURL(file.file)
        reader.onload = function (e) {
          let base64 = e.target.result.split(',')[1]
          let imageUrl = `data:${file.file.type};base64,${base64}`
          that.imageUrl = imageUrl
          that.innerForm.image = imageUrl
        }
      }
    },
    // 获取机构列表
    getAgencies() {
      agencies().then(res => {
        this.depts = res.data
      })
    },
    // 对需要导出的数据做处理
    formatJson(header, data) {
      return data.map(v => {
        if (v.appTag === 'BS') {
          v.appTag = '网页'
        } else {
          v.appTag = '客户端'
        }
        if (v.appSign === '1') {
          v.appSign = '平台应用'
        } else {
          v.appSign = '第三方应用'
        }
        return header.map(j => {
          return v[j]
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.yspf {
  .grade-switch {
    margin-left: 20px;
  }
  .form-col {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        &::before {
          content: '';
          width: 4px;
          height: 14px;
          background: #1273ce;
          display: inline-block;
          margin-right: 4px;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
      img {
        height: 36px;
        width: 36px;
      }
      .blue {
        color: #1273ce;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
      .red {
        color: #ff4645;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
::v-deep .el-dialog {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
  }
  .avatar {
    width: 60px;
    height: 60px;
    display: block;
  }
  .tips {
    color: #949699;
  }
}
.edit-form-wrap {
  position: relative;
  width: 100%;
  padding: 0 20px;
}
.form-title {
  padding: 10px;
  box-sizing: border-box;
  border-left: 4px solid #1273ce;
}
.doctor-info {
  position: relative;
  box-sizing: border-box;
  .info-item {
    padding: 12px 20px;
    .info-label {
      font-weight: bold;
      margin-right: 20px;
    }
  }
  padding-bottom: 20px;
}
.history-form-wrap {
  max-height: 500px;
  overflow-y: auto;
}
.doctor-history {
  position: relative;
  box-sizing: border-box;
  .history-date {
    padding: 0 20px;
    font-size: 18px;
    font-weight: bold;
  }
  .history-item {
    .history-label {
      font-weight: bold;
      margin-right: 20px;
    }
    .history-value {
      margin-right: 20px;
    }
    .history-type {
      padding: 12px 20px 0 20px;
    }
    .history-reason {
      padding: 12px 20px 0 20px;
    }
    .history-marker {
      padding: 12px 20px 0 20px;
    }
  }
  padding-bottom: 30px;
}
.form-textarea {
  padding: 10px 30px;
}
.multiple-institutions {
  padding: 0 20px;
  .multiple-col {
    margin: 0 5px;
  }
}
</style>
<style lang="scss">
.yspf {
  .el-dialog .el-dialog__body .el-form .el-input {
    width: 100%;
  }
}
</style>
