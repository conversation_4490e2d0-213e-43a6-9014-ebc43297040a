<template>
  <div class="home-container apps-manage">
    <div class="form-col">
      <el-form size="small" :model="searchForm" inline>
        <el-form-item>
          <el-select v-model="searchForm.depts1" clearable placeholder="选择科室">
            <el-option
              v-for="item in depts1"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.depts2" clearable placeholder="选择疾病">
            <el-option
              v-for="item in depts2"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.depts3" clearable placeholder="选择治疗手段">
            <el-option
              v-for="item in depts3"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.depts4" clearable placeholder="选择地区">
            <el-option
              v-for="item in depts4"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="query">查 询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <div class="text">专家库</div>
        <div>
          <el-button class="el-button--custom" size="small" @click="exportTable">
            导 出
          </el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        border
        :header-cell-style="{
          'background': '#F2F4F7',
          'color': '#303133',
          'font-weight': 600
        }"
        tooltip-effect="dark"
        width="100%"
        highlight-current-row
      >
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column label="姓名" prop="name" />
        <el-table-column label="手机号码" prop="phoneNumber" />
        <el-table-column label="身份证号" prop="identityNo" />
        <el-table-column label="机构名称" prop="orgName" />
        <el-table-column label="机构编码" prop="orgCode" />
        <el-table-column label="职业级别" prop="practiceLevel" />
        <el-table-column label="职业类别" prop="practiceType" />
        <el-table-column label="专家标签" prop="expertLabels" width="300" >
          <template slot-scope="props">
            <el-tag  v-for="ite in props.row.expertLabels" :key="ite.id">
                {{ ite.dictLabel }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageNum"
          background
          size="small"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { agencies, record ,dataList} from '@/api/records'
import moment from 'moment'
export default {
  data() {
    return {
      // ---健康卡---
      loading: false,
      isShowDialog: false,
      tableData: [
      ],
      total: 0,
      pageSize: 10,
      pageNum: 1,
      depts1: [],
      depts2: [],
      depts3: [],
      depts4: [],
      searchForm: {
        depts1: '',
        depts2: '',
        depts3: '',
        depts4: '',
      },
      editForm: {}
    }
  },
  mounted() {
    this.axiosTable()
  },
  created() {
    this.dataList()
  },
  filters: {
    // expertLabels(val) {
    //   let name = ''
    //   if (val.length>0) {
    //     val.forEach((item)=>{
    //       name+= item.dictLabel
    //     })
    //   }
    //   return name
    // }
  },
  methods: {
    // 查询
    query() {
      this.pageNum = 1
      this.axiosTable()
    },
    // 表格默认请求
    axiosTable() {
      this.loading = true
      let dictIds = []
      // searchForm: {
      //   depts1: '',
      //   depts2: '',
      //   depts3: '',
      //   depts4: '',
      // },
      if (this.searchForm.depts1) {
        dictIds.push(this.searchForm.depts1)
      }
      if (this.searchForm.depts2) {
        dictIds.push(this.searchForm.depts2)
      }
      if (this.searchForm.depts3) {
        dictIds.push(this.searchForm.depts3)
      }
      if (this.searchForm.depts4) {
        dictIds.push(this.searchForm.depts4)
      }
      const data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        dictIds: dictIds
      }
      record(data)
        .then(res => {
          this.tableData = res.data.list
          this.loading = false
          this.total = res.data.total
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 新增
    add() {
      this.isShowDialog = true
    },
    // sizeChange
    handleSizeChange(size) {
      this.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.pageNum = page
      this.axiosTable(page)
    },
    // 编辑
    edit(row) {
      this.isShowDialog = true
    },
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          appRemove({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 导出
    exportTable() {
      const header = ['name', 'phoneNumber', 'identityNo', 'orgName', 'orgCode','practiceLevel','practiceType']
      const data = this.formatJson(header, this.tableData)
      import('@/utils/export2excel').then(excel => {
        excel.export_json_to_excel({
          header: ['姓名', '手机号码', '身份证号码', '机构名称', '机构编码', '职业级别','职业类别'],
          data,
          filename: '专家库',
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    // 弹窗提交
    submit() {
      this.$refs['innerForm'].validate(value => {
        if (value) {
        } else {
          return false
        }
      })
    },
    // 弹框取消
    cancel() {
      this.isShowDialog = false
    },
    // 自定义上传图片
    httpRequest(file) {
      if (file.file.size > 10 * 1024) {
        this.$message.warning('上传图片大小不能超过10kb!')
      } else {
        let that = this
        const reader = new FileReader()
        reader.readAsDataURL(file.file)
        reader.onload = function (e) {
          let base64 = e.target.result.split(',')[1]
          let imageUrl = `data:${file.file.type};base64,${base64}`
          that.imageUrl = imageUrl
          that.innerForm.image = imageUrl
        }
      }
    },
    dataList() {
      dataList('sys_expert_dept').then(res => {
        this.depts1 = res.data
      })
      dataList('sys_expert_disease').then(res => {
        this.depts2 = res.data
      })
      dataList('sys_expert_treat').then(res => {
        this.depts3 = res.data
      })
      dataList('sys_expert_region').then(res => {
        this.depts4 = res.data
      })
    },

    // 对需要导出的数据做处理
    formatJson(header, data) {
      return data.map(v => {
        if (v.appTag === 'BS') {
          v.appTag = '网页'
        } else {
          v.appTag = '客户端'
        }
        if (v.appSign === '1') {
          v.appSign = '平台应用'
        } else {
          v.appSign = '第三方应用'
        }
        return header.map(j => {
          return v[j]
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-tag {
    margin-right: 5px;
    margin-bottom: 5px;
  }
}
.apps-manage {
  .form-col {
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        &::before {
          content: '';
          width: 4px;
          height: 14px;
          background: #1273ce;
          display: inline-block;
          margin-right: 4px;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
      img {
        height: 36px;
        width: 36px;
      }
      .blue {
        color: #1273ce;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
      .red {
        color: #ff4645;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
::v-deep .el-dialog {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
  }
  .avatar {
    width: 60px;
    height: 60px;
    display: block;
  }
  .tips {
    color: #949699;
  }
}
.edit-form-wrap {
  position: relative;
  width: 100%;
  padding: 0 20px;
}
</style>
