<template>
  <div class="home-container opinion-manage">
    <div class="form-col">
      <el-form size="small" :model="searchForm" inline>
        <el-form-item label="姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入姓名"
            clearable
          />
        </el-form-item>
        <el-form-item label="用户角色">
          <el-select
            v-model="searchForm.roleId"
            placeholder="请选择用户角色"
            clearable
          >
            <el-option
              v-for="(item, index) in roleListArr"
              :key="index"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="反馈日期">
          <el-date-picker
            v-model="searchForm.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="reset">重 置</el-button>
          <el-button type="primary" @click="query">查 询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <div class="tabs">
          <span
            v-for="(item, index) in [
              { effective: 1, title: '已解决' },
              { effective: 0, title: '未解决' }
            ]"
            :key="index"
            @click="changeEffective(item, index)"
            :class="{ active: activeIndex === index }"
          >
            {{ item.title }}</span
          >
        </div>
        <div class="tooltip" v-if="selectionList.length > 1">
          您当前共选中<span>{{ selectionList.length }}条</span>信息
        </div>
        <el-button
          class="el-button--custom"
          size="small"
          @click="solves"
          v-if="searchForm.effective === 0"
        >
          批量解决
        </el-button>
        <el-button
          class="el-button--custom"
          size="small"
          @click="removes"
          v-else
        >
          批量删除
        </el-button>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        ref="multipleTable"
        row-key="id"
        @selection-change="handleSelectionChange"
        border
        :header-cell-style="{
          background: '#F2F4F7',
          color: '#303133',
          'font-weight': 600
        }"
        tooltip-effect="dark"
        width="100%"
        highlight-current-row
      >
        <el-table-column type="selection" width="55" reserve-selection />
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column label="姓名" prop="name" />
        <el-table-column label="用户角色" prop="roleName" />
        <el-table-column label="工号" prop="providerId" />
        <el-table-column label="科室" prop="dept" />
        <el-table-column label="反馈描述" prop="content" />
        <el-table-column label="系统版本" prop="version" />
        <el-table-column label="反馈日期" prop="createTime" />
        <el-table-column label="操作">
          <template slot-scope="props">
            <span class="blue" @click="detail(props.row)">查看详情</span>
            <span
              class="blue"
              @click="solve(props.row)"
              v-if="props.row.effective === '0'"
              >解决</span
            >
            <span class="red" @click="remove(props.row)" v-else>删除</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageNum"
          background
          :page-sizes="[10, 20, 30, 40]"
          size="small"
          :page-size="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      title="意见反馈详情"
      :visible.sync="isShowDialog"
      width="30%"
      :before-close="cancel"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form :model="innerForm" size="small" inline>
        <div class="background">
          <div class="flex">
            <el-form-item label="反馈人:">
              <span>{{ innerForm.name }}</span>
            </el-form-item>
            <el-form-item label="反馈时间:">
              <span>{{ innerForm.createTime }}</span>
            </el-form-item>
          </div>
          <div class="flex">
            <el-form-item label="用户角色:">
              <span>{{ innerForm.roleName }}</span>
            </el-form-item>
            <el-form-item label="工号:">
              <span>{{ innerForm.providerId }}</span>
            </el-form-item>
            <el-form-item label="科室:">
              <span>{{ innerForm.dept }}</span>
            </el-form-item>
          </div>
        </div>
        <div class="flex">
          <el-form-item label="反馈内容:">
            <span>{{ innerForm.content }}</span>
          </el-form-item>
        </div>
        <div class="flex">
          <el-form-item label="图片:">
            <el-image
              :src="innerForm.screenShot"
              alt=""
              :preview-src-list="[innerForm.screenShot]"
            />
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取 消</el-button>
        <el-button
          type="primary"
          @click="solve(innerForm)"
          size="small"
          v-if="innerForm.effective === '0'"
          >解 决</el-button
        >
        <el-button type="primary" @click="remove(innerForm)" size="small" v-else
          >删 除</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { opinionList, opinionUpdate, opinionRemove } from '@/api/opinion-manage'
import { roleList } from '@/api/role-manage'
export default {
  data() {
    return {
      searchForm: {
        name: '',
        roleId: '',
        time: ['', ''],
        pageSize: 10,
        pageNum: 1,
        effective: 1 // 1 已解决 0 未解决
      },
      tableData: [],
      loading: false,
      total: 0,
      selectionList: [],
      activeIndex: 0,
      isShowDialog: false,
      innerForm: {
        name: '',
        createTime: '',
        roleName: '',
        providerId: '',
        dept: '',
        content: '',
        screenShot: ''
      },
      roleListArr: []
    }
  },
  mounted() {
    this.axiosTable(1)
    this.getRoleList()
  },
  methods: {
    // 重置
    reset() {
      this.searchForm = this.$options.data().searchForm
      this.axiosTable(1)
    },
    // 查询
    query() {
      this.searchForm.pageNum = 1

      this.axiosTable(1)
    },
    // 表格默认请求
    axiosTable(page = 1) {
      this.loading = true
      if (!this.searchForm.time) this.searchForm.time = ['', '']
      let searchForm = {
        ...this.searchForm,
        startTime: this.searchForm.time[0],
        endTime: this.searchForm.time[1],
        pageNum: page
      }
      delete searchForm.time
      opinionList(searchForm)
        .then((res) => {
          this.loading = false
          this.tableData = res.data
          this.total = res.total
        })
        .catch((e) => {
          this.loading = false
        })
    },
    // 切换解决类型
    changeEffective(item, index) {
      this.searchForm.effective = item.effective
      this.activeIndex = index
      this.query()
      this.$refs['multipleTable'].clearSelection()
    },
    // 表格选中
    handleSelectionChange(val) {
      this.selectionList = val
    },
    // sizeChange
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.searchForm.pageNum = page
      this.axiosTable(page)
    },
    // 查看详情
    detail(row) {
      this.isShowDialog = true
      this.innerForm = {
        ...row
      }
    },
    // 解决按钮
    solve(row) {
      opinionUpdate({ id: row.id }).then((res) => {
        if (res.code===200) {
          this.$message.success(res.message)
          this.axiosTable(this.searchForm.pageNum)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 批量解决
    solves() {
      if (this.selectionList.length) {
        let ids = this.selectionList.map((el) => el.id).join()
        opinionUpdate({ id: ids }).then((res) => {
          if (res.code===200) {
            this.$message.success(res.message)
            this.axiosTable(this.searchForm.pageNum)
            this.$refs['multipleTable'].clearSelection()
          } else {
            this.$message.error(res.message)
          }
        })
      } else {
        this.$message.warning('请先勾选要解决的内容')
      }
    },
    // 删除
    remove(row, type) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          opinionRemove({
            id: row.id
          }).then((res) => {
            if (res.code===200) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
              this.isShowDialog = false
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 批量删除
    removes() {
      if (this.selectionList.length) {
        let ids = this.selectionList.map((el) => el.id).join()
        this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            opinionRemove({
              id: ids
            }).then((res) => {
              if (res.code===200) {
                this.$message.success(res.message)
                this.axiosTable(this.searchForm.pageNum)
                this.$refs['multipleTable'].clearSelection()
              } else {
                this.$message.error(res.message)
              }
            })
          })
          .catch(() => {})
      } else {
        this.$message.warning('请先勾选需要删除的选项')
      }
    },
    // 弹框取消
    cancel() {
      this.innerForm = this.$options.data().innerForm
      this.isShowDialog = false
    },
    // 获取角色list
    getRoleList() {
      roleList().then((res) => {
        this.roleListArr = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.opinion-manage {
  .form-col {
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-shadow: inset 0px -1px 0px 0px #dcdee5;
      padding-bottom: 9px;
      .tabs {
        font-size: 16px;
        font-weight: 400;
        span {
          padding: 0 10px 15px 10px;
        }
      }
      .active {
        font-weight: 600;
        color: #1273ce;
        border-bottom: 2px solid #1273ce;
      }
      .tooltip {
        color: #313133;
        font-size: 14px;
        font-weight: 400;
        span {
          color: #559bdd;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
      .blue {
        color: #1273ce;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
      .red {
        color: #ff4645;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
  ::v-deep.el-dialog__body {
    padding-top: 6px;
  }
}
.background {
  background: #f5faff;
}
.flex {
  display: flex;
  justify-content: space-between;
  .el-form-item {
    display: flex;
    justify-content: space-around;
    ::v-deep .el-form-item__content {
      flex: 1;
    }
  }
}
</style>