<template>
  <div class="home-container update-records">
    <div class="form-col">
      <el-form size="small" :model="searchForm" inline>
        <el-form-item>
          <el-date-picker
            v-model="searchForm.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.hosptialId" clearable placeholder="请选择机构">
            <el-option
              v-for="item in depts"
              :key="item.hospitalId"
              :label="item.hospitalName"
              :value="item.hospitalId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item labe="搜索">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入关键字查找"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="query">查 询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <div class="text">更新列表</div>
        <div>
          <el-button class="el-button--custom" size="small" @click="exportTable">
            导 出
          </el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        border
        :header-cell-style="{
          'background': '#F2F4F7',
          'color': '#303133',
          'font-weight': 600
        }"
        tooltip-effect="dark"
        width="100%"
        highlight-current-row
      >
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column label="更新时间" prop="updateTime" />
        <el-table-column label="姓名" prop="name" />
        <el-table-column label="性别" prop="sex" />
        <el-table-column label="身份证号" prop="idNo" />
        <el-table-column label="手机号" prop="phone" />
        <el-table-column label="民族" prop="nation" />
        <el-table-column label="电子健康卡ID" prop="cardNo" />
        <el-table-column label="户籍地" prop="residentAddress" />
        <el-table-column label="申领地" prop="applyAddress" />
        <el-table-column label="操作">
          <template slot-scope="props">
            <span class="blue" @click="detail(props.row)">查看详情</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageNum"
          background
          size="small"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      title="更新详情"
      :visible.sync="isShowDialog"
      width="800px"
      :before-close="cancel"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-table :data="detailData" style="width: 100%" :row-class-name="setRowClass">
        <el-table-column prop="name" label="" width="180"></el-table-column>
        <el-table-column prop="before" label="变更前"></el-table-column>
        <el-table-column
          prop="after"
          :label="'变更后(' + updateTime + ')'"
        ></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { agencies, record, updateInfo } from '@/api/records'
import moment from 'moment'
export default {
  data() {
    return {
      // ---健康卡---
      loading: false,
      isShowDialog: false,
      tableData: [],
      total: 0,
      pageSize: 10,
      pageNum: 1,
      depts: [],
      searchForm: {
        keyword: '',
        hospitalId: '',
        date: []
      },
      detailData: {},
      updateTime: '' // 变更时间
    }
  },
  mounted() {
    this.axiosTable()
  },
  created() {
    this.getAgencies()
  },
  methods: {
    // 查询
    query() {
      this.pageNum = 1
      this.axiosTable()
    },
    // 表格默认请求
    axiosTable() {
      this.loading = true
      const startTime =
        this.searchForm.date && this.searchForm.date.length
          ? moment(this.searchForm.date[0]).format('YYYY-MM-DD HH:mm:ss')
          : ''
      const endTime =
        this.searchForm.date && this.searchForm.date.length
          ? moment(this.searchForm.date[1]).format('YYYY-MM-DD HH:mm:ss')
          : ''
      const data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        operateType: '2', //1 申领 2 更新 3 注销
        name: '',
        hosptialId: this.searchForm.hosptialId,
        keyword: this.searchForm.keyword,
        residentAddress: '',
        applyAddress: '',
        cardNo: '',
        idNo: '',
        startTime: startTime,
        endTime: endTime,
        isAll: 0,
        lastId: 0
      }
      record(data)
        .then(res => {
          this.tableData = res.data.list
          this.loading = false
          this.total = res.data.total
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 新增
    add() {
      this.dialogType = 0
      this.isShowDialog = true
    },
    // sizeChange
    handleSizeChange(size) {
      this.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.pageNum = page
      this.axiosTable(page)
    },
    // 编辑
    detail(row) {
      this.loading = true
      updateInfo({ id: row.id })
        .then(res => {
          if (res.code === 200) {
            this.detailData = this.formatDetail(res.data)
            this.updateTime = res.data.after.updateTime
            this.isShowDialog = true
          }
          this.loading = false
        })
        .catch(e => {
          this.loading = false
        })
    },
    formatDetail(data) {
      const items = [
        { key: 'name', name: '姓名' },
        { key: 'sex', name: '性别' },
        { key: 'idNo', name: '身份证号' },
        { key: 'birthday', name: '出生日期' },
        { key: 'phone', name: '手机号码' },
        { key: 'nation', name: '民族' },
        { key: 'residentAddress', name: '户籍地' },
        { key: 'applyAddress', name: '申领地址' }
      ]
      return items.map(item => {
        let obj = {}
        if (data.before.hasOwnProperty(item.key)) {
          obj.name = item.name
          obj.before = data.before[item.key]
          obj.after = data.after[item.key]
          obj.diff = obj.before === obj.after ? false : true
        }
        return obj
      })
    },
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          appRemove({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 导出
    exportTable() {
      const header = ['id', 'name', 'appTag', 'url', 'appSign', 'typeName']
      const data = this.formatJson(header, this.tableData)
      import('@/utils/export2excel').then(excel => {
        excel.export_json_to_excel({
          header: ['应用ID', '应用名称', '应用类型', '主页路径', '分类', '应用分组'],
          data,
          filename: '应用管理',
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    // 弹窗提交
    submit() {
      this.$refs['innerForm'].validate(value => {
        if (value) {
          if (this.dialogType === 0) {
            appRegister(this.innerForm).then(res => {
              if (res.data) {
                this.cancel()
                this.axiosTable(this.searchForm.pageNum)
                this.$message.success(res.message)
              } else {
                this.$message.error(res.message)
              }
            })
          } else {
            appUpdate(this.innerForm).then(res => {
              if (res.data) {
                this.cancel()
                this.axiosTable(this.searchForm.pageNum)
                this.$message.success(res.message)
              } else {
                this.$message.error(res.message)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 弹框取消
    cancel() {
      this.isShowDialog = false
    },
    // 自定义上传图片
    httpRequest(file) {
      if (file.file.size > 10 * 1024) {
        this.$message.warning('上传图片大小不能超过10kb!')
      } else {
        let that = this
        const reader = new FileReader()
        reader.readAsDataURL(file.file)
        reader.onload = function (e) {
          let base64 = e.target.result.split(',')[1]
          let imageUrl = `data:${file.file.type};base64,${base64}`
          that.imageUrl = imageUrl
          that.innerForm.image = imageUrl
        }
      }
    },
    // 获取机构列表
    getAgencies() {
      agencies().then(res => {
        this.depts = res.data
      })
    },
    // 对需要导出的数据做处理
    formatJson(header, data) {
      return data.map(v => {
        if (v.appTag === 'BS') {
          v.appTag = '网页'
        } else {
          v.appTag = '客户端'
        }
        if (v.appSign === '1') {
          v.appSign = '平台应用'
        } else {
          v.appSign = '第三方应用'
        }
        return header.map(j => {
          return v[j]
        })
      })
    },
    setRowClass({ row }) {
      if (row.diff) {
        return 'diff'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.update-records {
  .form-col {
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        &::before {
          content: '';
          width: 4px;
          height: 14px;
          background: #1273ce;
          display: inline-block;
          margin-right: 4px;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
      img {
        height: 36px;
        width: 36px;
      }
      .blue {
        color: #1273ce;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
      .red {
        color: #ff4645;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
::v-deep .el-dialog {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
  }
  .avatar {
    width: 60px;
    height: 60px;
    display: block;
  }
  .tips {
    color: #949699;
  }
}
</style>
<style>
.update-records .el-table .el-table__row.diff {
  color: #f5580f;
}
</style>
