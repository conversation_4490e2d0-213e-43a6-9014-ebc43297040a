<template>
  <div class="home-container user-manage">
    <div class="form-col">
      <el-form size="small" :model="searchForm" inline>
        <el-form-item label="用户名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入用户名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="科室">
          <el-input
            v-model="searchForm.deptName"
            placeholder="请输入用户名称"
            clearable
          />
        </el-form-item>
        <el-form-item label="工号">
          <el-input
            v-model="searchForm.userName"
            placeholder="请输入工号"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="reset">重 置</el-button>
          <el-button type="primary" @click="query">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row class="user-role" :gutter="20">
      <el-col :span="16">
        <div class="table-col">
          <div class="title">
            <div class="text">用户列表</div>
            <div class="tooltip" v-if="selectionList.length > 1">
              您当前共选中<span>{{ selectionList.length }}个</span>用户
            </div>
            <el-button
              class="el-button--custom"
              size="small"
              @click="importXLS"
            >
              导 入
            </el-button>
          </div>
          <el-table
            :data="tableData"
            v-loading="loading"
            size="small"
            ref="multipleTable"
            row-key="id"
            @selection-change="handleSelectionChange"
            border
            :header-cell-style="{
              background: '#F2F4F7',
              color: '#303133',
              'font-weight': 600
            }"
            tooltip-effect="dark"
            width="100%"
            highlight-current-row
            @row-click="rowClick"
            :current-row-key="currentRowKey"
          >
            <el-table-column type="selection" width="55" reserve-selection />
            <el-table-column type="index" width="50" label="序号" />
            <el-table-column label="账号状态">
              <template slot-scope="props">
                <span v-if="props.row.isLocked === '1'" class="is-lock"
                  >已锁定</span
                >
                <span v-else>正常</span>
              </template>
            </el-table-column>
            <el-table-column label="工号" prop="userName" />
            <el-table-column label="姓名" prop="name" />
            <el-table-column label="所属科室" prop="deptName" />
            <el-table-column label="机构名称" prop="jobCategoryName" />
            <el-table-column label="数据权限" prop="">
              <template slot-scope="props">
                {{
                  props.row.dataScope === 'all'
                    ? '全科室'
                    : props.row.dataScope === ''
                    ? ''
                    : '本科室'
                }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template slot-scope="props">
                <span
                  class="blue"
                  v-if="props.row.isLocked === '1'"
                  @click.stop="isLock(props.row)"
                  >解锁</span
                >
                <span class="blue" @click.stop="edit(props.row)">编辑</span>
                <span class="blue" @click.stop="passwordSetting(props.row)"
                  >密码管理</span
                >
                <span class="red" @click.stop="remove(props.row)">删除</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="searchForm.pageNum"
              background
              :page-sizes="[10, 20, 30, 40]"
              size="small"
              :page-size="searchForm.pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            >
            </el-pagination>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="role">
          <div class="title">
            {{ checkedName + '角色管理' }}
          </div>
          <div class="tree-container">
            <el-tree
              ref="systemTree"
              show-checkbox
              :data="systemTree"
              class="scroll"
              node-key="id"
              :props="defaultProps"
              default-expand-all
            />
          </div>
          <div class="save">
            <el-button size="small" type="primary" @click="save"
              >保 存</el-button
            >
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      title="编辑用户数据权限信息"
      :visible.sync="editUserRoleInfoDialog"
      width="30%"
      :before-close="closeUserRoleInfoDialog"
    >
      <el-form :model="innerForm" size="small" label-width="120px">
        <el-form-item label="工号">
          <el-input v-model="innerForm.userName" disabled />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="innerForm.name" disabled />
        </el-form-item>
        <el-form-item label="所属科室">
          <el-select v-model="innerForm.deptName" disabled>
            <el-option
              v-for="(item, index) in 10"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="数据权限">
          <el-select v-model="innerForm.dataScope">
            <el-option label="全科室" value="all" />
            <el-option label="本科室" value="dept" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeUserRoleInfoDialog" size="small"
          >取 消</el-button
        >
        <el-button type="primary" @click="editUserSave" size="small"
          >保 存</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="从XLS导入数据"
      :visible.sync="importDataDialog"
      width="25%"
      :before-close="closeImportDataDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <div class="setps">
        <div class="title">步骤:</div>
        <p>
          1.请先下载模板
          <el-button class="green" size="small" @click="downloadTemplate"
            >下载模板</el-button
          >
        </p>
        <p class="test">
          <span> 2.按照模板整理数据</span>
          <el-upload
            action=""
            ref="upload"
            :on-remove="handleRemove"
            multiple
            accept=".xls,.xlsx"
            show-file-list
            :http-request="httpRequest"
          >
            <el-button class="blue" size="small">选择文件</el-button>
          </el-upload>
        </p>
        <p>
          3.开始导入（如果主键不存在则新增数据，存在则更新数据）
          <el-button class="orgin" size="small" @click="startUpload"
            >开始导入</el-button
          >
        </p>
      </div>
    </el-dialog>
    <el-dialog
      title="密码管理"
      :visible.sync="passwordDialog"
      width="700px"
      :before-close="closePasswordDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="passwordForm"
        :rules="passwordRules"
        size="small"
        label-width="120px"
        ref="passwordForm"
      >
        <el-form-item label="当前密码：" prop="password">
          <el-input
            :key="passwordType"
            ref="password"
            v-model="passwordForm.password"
            :type="passwordType"
            placeholder="请输入密码"
            name="password"
            tabindex="2"
            auto-complete="on"
            :disabled="disabledPwd"
          />
          <span class="show-pwd" @click="showPwd">
            <svg-icon
              :icon-class="
                passwordType === 'password'
                  ? 'is-show-password'
                  : 'show-password'
              "
            />
          </span>
        </el-form-item>
        <el-form-item
          label="新密码："
          prop="newPassword"
          v-if="isShowEditPassword"
        >
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            name="newPassword"
            tabindex="2"
            auto-complete="on"
          />
        </el-form-item>
        <el-form-item
          label="确认新密码："
          prop="confirmPassword"
          v-if="isShowEditPassword"
        >
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            name="confirmPassword"
            tabindex="2"
            auto-complete="on"
          />
        </el-form-item>
        <el-form-item>
          <div v-if="!isShowEditPassword">
            <el-button class="el-button--custom" @click="resetPassword"
              >一键重置密码</el-button
            >
            <el-button type="primary" @click="changePassword"
              >修改密码</el-button
            >
          </div>
          <div v-else>
            <el-button @click="closePasswordDialog">取消</el-button>
            <el-button type="primary" @click="savePassword">保存</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  userList,
  userUpdate,
  userRemove,
  userRoleSave,
  downloadTemplate,
  importExcel,
  deBlocking,
  resetPwd,
  updatePwd,
  queryPwd
} from '@/api/user-manage'
import { roleList } from '@/api/role-manage'
import { validatorPwd } from '@/utils/validatorPwd.js'
import { encrypt, decrypt } from '@/utils/crypto-request'
import { getLoginUserName, getWindowName } from '@/utils/auth'
export default {
  data() {
    const validateNewPassword = (rule, value, callback) => {
      if (value === this.passwordForm.password) {
        callback('新密码不可与旧密码一致!')
      } else {
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback('两次输入密码不一致!')
      } else {
        callback()
      }
    }
    const validatorPassword = (rule, value, callback) => {
      callback(validatorPwd(rule, value, callback))
    }
    return {
      searchForm: {
        name: '',
        deptName: '',
        userName: '',
        pageSize: 10,
        pageNum: 1
      },
      tableData: [],
      loading: false,
      total: 0,
      checkedName: '',
      systemTree: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      hasRights: [], // 拥有的平台应用权限
      expandRights: [], // 平台应用权限需要展开的keys
      selectionList: [], // 表格勾选的数据
      editUserRoleInfoDialog: false,
      innerForm: {
        userName: '',
        name: '',
        deptName: '',
        dataScope: ''
      },
      importDataDialog: false,
      fileList: [],
      formData: null,
      passwordDialog: false,
      passwordForm: {
        password: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        password: [
          { required: false, trigger: 'blur', message: '当前密码不能为空' }
        ],
        newPassword: [
          { required: true, trigger: 'blur', message: '请输入新密码' },
          {
            validator: validateNewPassword,
            trigger: 'blur'
          },
          {
            validator: validatorPassword,
            trigger: 'blur'
          }
        ],
        confirmPassword: [
          { required: true, trigger: 'blur', message: '请确认新密码' },
          {
            validator: validateConfirmPassword,
            trigger: 'blur'
          },
          {
            validator: validatorPassword,
            trigger: 'blur'
          }
        ]
      },
      passwordType: 'password',
      isShowEditPassword: false,
      tempRow: {},
      disabledPwd: false,
      currentRowKey:''
    }
  },
  mounted() {
    this.axiosTable(1)
    this.getRoleList()
  },
  methods: {
    // 重置
    reset() {
      this.searchForm = this.$options.data().searchForm
      this.axiosTable(1)
    },
    // 查询
    query() {
      this.searchForm.pageNum = 1
      this.axiosTable(1)
    },
    // 表格默认请求
    axiosTable(page = 1) {
      this.loading = true
      userList({
        ...this.searchForm,
        pageNum: page
      })
        .then((res) => {
          this.loading = false
          this.tableData = res.data
          this.total = res.total
          if (res.data.length) {
            this.rowClick(res.data[0])
          }
        })
        .catch((e) => {
          this.loading = false
        })
    },
    // 表格选中
    handleSelectionChange(val) {
      this.selectionList = val
      if (val.length >= 2) {
        this.checkedName = '多个用户'
        this.$refs['systemTree'].setCheckedKeys([])
      } else {
        if (val.length) {
          this.rowClick(val[0])
        } else {
          this.$refs['systemTree'].setCheckedKeys([])
        }
      }
    },
    // 编辑
    edit(row) {
      this.editUserRoleInfoDialog = true
      this.innerForm = {
        ...row
      }
    },
    // 删除
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          userRemove({
            id: row.id,
            userName: row.userName
          }).then((res) => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // sizeChage
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.searchForm.pageNum = page
      this.axiosTable(page)
    },
    // 保存
    save() {
      let userIds = this.selectionList.map((el) => el.id).join()
      let roleIds = this.$refs['systemTree'].getCheckedKeys().join()
      if (userIds) {
        userRoleSave({
          userIds,
          roleIds
        }).then((res) => {
          if (res.code === 200) {
            this.$message.success(res.message)
            this.$refs['systemTree'].setCheckedKeys([])
            this.$refs.multipleTable.clearSelection()
            this.axiosTable(this.searchForm.pageNum)
          }
        })
      } else {
        this.$message.warning('请选择需要绑定的用户，以及要绑定的角色')
      }
    },
    // 关闭编辑用户数据权限信息
    closeUserRoleInfoDialog() {
      this.editUserRoleInfoDialog = false
      this.innerForm = this.$options.data().innerForm
    },
    // 保存编辑用户数据权限信息
    editUserSave() {
      userUpdate(this.innerForm).then((res) => {
        if (res.data) {
          this.$message.success(res.message)
          this.axiosTable(this.searchForm.pageNum)
          this.closeUserRoleInfoDialog()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 导入按钮
    importXLS() {
      this.importDataDialog = true
    },
    // 关闭导入弹窗
    closeImportDataDialog() {
      this.importDataDialog = false
      this.$refs['upload'].clearFiles()
    },
    // 文件列表移除文件时的钩子
    handleRemove(file, fileList) {
      console.log(file, fileList)
    },
    // 自定义上传
    httpRequest(file) {
      this.formData = new FormData()
      this.formData.append('file', file.file)
    },
    // 开始上传
    startUpload() {
      importExcel(this.formData).then((res) => {
        if (res.code === 200) {
          this.$message.success(res.data)
          this.closeImportDataDialog()
          this.axiosTable(this.searchForm.pageNum)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 角色列表
    getRoleList() {
      this.systemTree = []
      roleList().then((res) => {
        res.data = res.data.map((el) => {
          return {
            ...el,
            label: el.name,
            children: []
          }
        })
        this.systemTree.push(...res.data)
      })
    },
    // table 行单击
    rowClick(row) {
      this.checkedName = row.name
      let ids = ''
      if (row.roleIds) {
        ids = row.roleIds.split(',')
      } else {
        ids = []
      }
      this.currentRowKey = row.id;
      this.$refs['systemTree'].setCheckedKeys(ids)
    },
    // 下载模板
    downloadTemplate() {
      downloadTemplate().then((res) => {
        if (res.data) {
          let data = res.data
          var blob = new Blob([data], { type: data.type })
          let url = window.URL.createObjectURL(blob)
          if (window.navigator.msSaveBlob) {
            try {
              window.navigator.msSaveBlob(blob, '模板.xlsx')
            } catch (error) {
              console.log(error)
            }
          }
          const link = document.createElement('a')
          link.href = url
          link.download = '模板.xlsx'
          link.click()
          URL.revokeObjectURL(url)
        }
      })
    },
    // 解锁 解除锁定状态
    isLock(row) {
      // this.$message.success('当前用户已解锁成功!')
      deBlocking({ id: row.id, userName: row.userName }).then((res) => {
        if (res.code === 200) {
          this.$message.success('当前用户已解锁成功！')
          this.axiosTable(this.searchForm.pageNum)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 密码管理弹窗
    passwordSetting(row) {
      this.passwordDialog = true
      this.tempRow = row
      this.getRowPwd(row.userName)
    },
    // 获取当前行的密码
    getRowPwd(username) {
      queryPwd({ username }).then((res) => {
        if (res.code === 200) {
          this.passwordForm.password = decrypt(res.data, 'YJZL@2022@GM57sN')
          this.disabledPwd = true
        } else {
          this.$message.error('获取密码失败！请手动输入')
          this.disabledPwd = false
        }
      })
    },
    // 关闭密码弹窗
    closePasswordDialog() {
      this.passwordForm = this.$options.data().passwordForm
      this.isShowEditPassword = false
      this.passwordDialog = false
      this.$refs['passwordForm'].resetFields()
    },
    // 控制是否显示密码
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
    },
    // 重置密码
    resetPassword() {
      this.$refs['passwordForm'].validate((valid) => {
        if (valid) {
          let passwordForm = {
            username: this.tempRow.userName,
            oldPassword: encrypt(this.passwordForm.password, 'YJZL@2022@GM57sN')
          }
          resetPwd(passwordForm).then((res) => {
            if (res.code === 200) {
              this.$message.success('当前账号重置密码成功！')
              const { loginUserName } = getWindowName()
              if (
                this.tempRow.userName === (getLoginUserName() || loginUserName)
              ) {
                this.$store.dispatch('user/logout').then(() => {
                  this.$router.push('/login')
                })
              } else {
                this.closePasswordDialog()
              }
            } else {
              this.$message.warning('重置密码失败！')
            }
          })
        } else {
          return false
        }
      })
    },
    // 修改密码
    changePassword() {
      this.isShowEditPassword = true
      // this.disabledPwd = false
      // this.passwordForm.password = ''
      this.passwordType = 'password'
    },
    // 保存
    savePassword() {
      this.$refs['passwordForm'].validate((valid) => {
        if (valid) {
          let passwordForm = {
            username: this.tempRow.userName,
            oldPassword: encrypt(
              this.passwordForm.password,
              'YJZL@2022@GM57sN'
            ),
            newPassword1: encrypt(
              this.passwordForm.newPassword,
              'YJZL@2022@GM57sN'
            ),
            newPassword2: encrypt(
              this.passwordForm.confirmPassword,
              'YJZL@2022@GM57sN'
            )
          }
          updatePwd(passwordForm).then((res) => {
            if (res.code === 200) {
              this.$message.success('保存密码成功！')
              const { loginUserName } = getWindowName()
              if (
                this.tempRow.userName === (getLoginUserName() || loginUserName)
              ) {
                this.$store.dispatch('user/logout').then(() => {
                  this.$router.push('/login')
                })
              } else {
                this.closePasswordDialog()
              }
            } else {
              this.$message.warning('保存密码失败！')
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.user-manage {
  .form-col {
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
    }
  }
  .user-role {
    height: calc(100% - 65px - 10px);
    margin-top: 10px;
    .el-col {
      height: 100%;
      .table-col {
        height: 100%;
        background: #fff;
        padding: 12px 20px;
        .title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .text {
            &::before {
              content: '';
              width: 4px;
              height: 14px;
              background: #1273ce;
              display: inline-block;
              margin-right: 4px;
            }
          }
          .tooltip {
            color: #313133;
            font-size: 14px;
            font-weight: 400;
            span {
              color: #559bdd;
            }
          }
        }
        .el-table ::v-deep {
          margin-top: 10px;

          .current-row {
            background: #d4eaff !important;
          }
          .blue {
            color: #1273ce;
            font-size: 14px;
            cursor: pointer;
            padding-left: 10px;
            &:first-child {
              padding-left: 0;
            }
          }
          .red {
            color: #ff4645;
            font-size: 14px;
            margin-left: 10px;
            cursor: pointer;
          }
          .is-lock {
            position: relative;
            padding-left: 15px;
            &::before {
              content: '';
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background: #ff4645;
              position: absolute;
              left: 0;
              top: 2px;
            }
          }
        }
        .pagination {
          text-align: right;
          margin-top: 10px;
        }
      }
      .role {
        background: #fff;
        height: 100%;
        padding: 18px 16px;
        .title {
          color: #313133;
          font-size: 16px;
          font-weight: 600;
        }
        .tree-container {
          height: calc(100% - 150px);
          margin-top: 17px;
          padding: 0 14px;
        }
        .scroll {
          height: 100%;
          box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
          border-radius: 4px;
          border-top: 4px solid #218ef5;
          padding: 32px 24px;
        }
        .save {
          margin-top: 24px;
          text-align: center;
        }
      }
    }
  }
  ::v-deep .el-dialog {
    .setps {
      color: #303133;
      font-size: 14px;
      font-weight: 400;
      p {
        padding: 0;
        margin: 0;
        // height: 45px;
        line-height: 45px;
        .el-button {
          border-radius: 2px;
          margin-left: 22px;
          color: #fff;
        }
        .green {
          background: #009688;
          border-color: #009688;
        }
        .blue {
          background: #2d8cf0;
          border-color: #2d8cf0;
        }
        .orgin {
          background: #f38b2f;
          border-color: #f38b2f;
        }
      }
      .test {
        display: flex;
      }
    }
  }
  .show-pwd {
    margin-left: 10px;
  }
}
</style>