<template>
  <div class="home-container apps-manage">
    <div class="form-col">
      <div>  
        <el-button type="primary" plain size="small" @click="add">新增</el-button>
        <el-button class="el-button--custom" size="small" @click="exportMb">
          模版导出
        </el-button>
        <el-button class="el-button--custom" size="small" @click="UploadMb">
          模版上传
        </el-button>
      </div>

      <el-form size="small" :model="searchForm" inline>
        <el-form-item>
          <el-select v-model="searchForm.typeIds" multiple clearable placeholder="机构类别">
            <el-option
              v-for="item in depts"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="searchForm.districtDictIds" multiple clearable placeholder="所属区县">
            <el-option
              v-for="item in baseAreas"
              :key="item.id"
              :label="item.dictLabel"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item labe="搜索">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入机构名称"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="query">查 询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <div class="text">机构列表</div>
        <div>
          <el-button class="el-button--custom" size="small" @click="exportTable">
            导 出
          </el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        border
        :header-cell-style="{
          'background': '#F2F4F7',
          'color': '#303133',
          'font-weight': 600
        }"
        tooltip-effect="dark"
        width="100%"
      >
        <el-table-column type="index" width="50" label="序号" />
        <el-table-column label="机构ID" prop="code" />
        <el-table-column label="机构名称" prop="name" />
        <el-table-column label="所属区县" prop="district" />
        <el-table-column label="所属乡镇" prop="county" />
        <el-table-column label="添加时间" prop="createTime" />
        <el-table-column label="是否启用" prop="enabled">
          <template slot-scope="props">
            <span>{{ props.row.enabled === 0 ? '否' : '是' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="props">
            <span class="blue" @click="remove(props.row)">删除</span>
            <span class="blue" @click="edit(props.row)">编辑</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="pageNum"
          background
          size="small"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      title="模版上传"
      :visible.sync="showDialogMb"
      width="20%"
      class="el-dialog1"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="fileList=[]"
    >
     <el-upload
        class="upload-demo"
        drag
        :auto-upload="false"
        accept=".doc, .docx, .xls, .xlsx, .csv"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        :on-change="handleChange"
        action="action"
        :limit="1"
        :file-list="fileList">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">只能上传模版文件</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="small">取 消</el-button>
        <el-button @click="handleUpload" size="small" type="primary">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="isEdit ? '编辑机构信息' : '新增机构信息'"
      :visible.sync="isShowDialog"
      width="700px"
      :before-close="cancel"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    > 
      <div class="edit-form-wrap">
        <el-form ref="ruleForm" :model="form" :rules="rules" label-width="150px" size="small">
          <el-form-item label="机构ID" prop="code">
            <el-input v-model="form.code"></el-input>
          </el-form-item>
          <el-form-item label="机构名称" prop="name">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="机构类别" prop="typeId">
            <el-select v-model="form.typeId" clearable placeholder="机构类别">
              <el-option
                v-for="item in depts"
                :key="item.id"
                :label="item.dictLabel"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="联系人">
            <el-input v-model="form.contactName"></el-input>
          </el-form-item>
          <el-form-item label="联系人职务">
            <el-input v-model="form.contactTitle"></el-input>
          </el-form-item>
          <el-form-item label="联系人电话">
            <el-input v-model="form.contactPhone"></el-input>
          </el-form-item>
          <el-form-item label="机构所属区域" prop="districtDictId">
            <el-select v-model="form.districtDictId" placeholder="请选择活动区域">
              <el-option
                v-for="item in baseAreas"
                :key="item.id"
                :label="item.dictLabel"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="机构所属县镇">
            <el-input v-model="form.county"></el-input>
          </el-form-item>
          <el-form-item label="机构地址">
            <el-input v-model="form.address"></el-input>
          </el-form-item>
          <el-form-item label="是否启用">
            <el-select v-model="form.enabled" placeholder="请选择是否启用">
              <el-option label="是" :value="1"></el-option>
              <el-option label="否" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="form.remark"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit" size="small">提 交</el-button>
        <el-button @click="cancel" size="small">重 置</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken,getWindowName } from '@/utils/auth'
const loginInfo = getWindowName()
const { appId } = loginInfo
import axios from 'axios'
import { orgList, orgDelete, orgSaveOrUpdate,downloadT } from '@/api/structure'
import { getAllDataList } from '@/api/common'
import moment from 'moment'
import { Upload } from 'element-ui'
export default {
  data() {
    return {
      // ---健康卡---
      loading: false,
      isShowDialog: false,
      tableData: [],
      total: 0,
      pageSize: 10,
      pageNum: 1,
      depts: [],
      baseAreas: [],
      searchForm: {
        keyword: '',
        districtDictIds: [],
        typeIds: []
      },
      fileList: [],
      rules: {
        code: [
          { required: true, message: "请输机构id", trigger: "blur" },
        ],
        name: [
          { required: true, message: "请输入机构名称", trigger: "blur" },
        ],
        typeId: [
          { required: true, message: "请选择机构类别", trigger: "change" },
        ],
        districtDictId: [
          { required: true, message: "请选择机构所属区域", trigger: "change" },
        ]
      },
      form: {
        id: '',
        code: '',
        name: '',
        typeId: '',
        contactName: '',
        contactTitle: '',
        contactPhone: '',
        districtDictId: '',
        county: '',
        address: '',
        enabled: 0,
        remark: ''
      },
      showDialogMb: false,
      url: '',
      isEdit: false // 是否编辑
    }
  },
  mounted() {
    if (process.env.NODE_ENV == 'development') {
      this.url = '/medical'
    } else {
      this.url = '/medical'
    }
  },
  created() {
    this.init()
  },
  methods: {
    UploadMb(){
      this.showDialogMb = true
    },
    handleUpload() {
      console.log(this.fileList)
      if (this.fileList.length == 0) {
        this.$message({
          message: '上传文件不能为空！',
          type: 'warning'
        });
      } else {
        let url = this.url + "/org/importExcel"
        let formDate = new FormData()
        formDate.append('file',this.fileList[0].raw)
        axios.post(url,formDate,{
            headers: {
              'saToken': getToken(), // 设置请求头，确保服务器正确解析 FormData
              'appId': appId
            }}).then((res)=>{
          console.log(res)
          if (res.data.code == 200) {
            this.$message({
              message: '上传成功',
              type: 'success'
            });
            this.fileList = []
            this.query()
            this.showDialogMb = false
          } else {
            this.$message({
              message: res.data.data,
              type: 'error'
            });
          }
        })
      }
    },
        /**
     * data: 下载文件
     * fileName: 文件名
     * type: 下载文件类型
     */
    downloadHandler(data, fileName, type) {
        // 匹配任意文件类型：type : "application/octet-stream"
        const blob = new Blob([data],  { type: type });
        console.log(blob)
        const downloadElement = document.createElement('a');
        const href = window.URL.createObjectURL(blob);
        downloadElement.href = href;
        downloadElement.download = fileName;
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);
        window.URL.revokeObjectURL(href);
    },
    exportMb() {
      let url = this.url + "/org/download/template"
      axios.post(url,{},{ responseType: 'arraybuffer',
            headers: {
              'saToken': getToken(), // 设置请求头，确保服务器正确解析 FormData
              'appId': appId
            }}).then((res)=>{

        if (res.status == 200) {
          this.$message({
            message: '模版下载成功！',
            type: 'success'
          });
        }
        this.downloadHandler(res.data,'模版.xlsx','application/vnd.ms-excel')
      })
    },
    handleChange(file, fileList) {
      this.fileList = fileList
      console.log(fileList)
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    // 新增编辑节点弹窗关闭
    handleClose() {
      this.showDialogMb = false
    },
    // 初始化获取下拉列表数据
    async init() {
      await this.getDepts()
      await this.getBaseArea()
      await this.query()
    },
    // 查询
    query() {
      this.pageNum = 1
      this.axiosTable()
    },
    // 表格默认请求
    axiosTable() {
      this.loading = true
      const data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        districtDictIds: this.searchForm.districtDictIds,
        typeIds: this.searchForm.typeIds,
        keyword: this.searchForm.keyword
      }
      orgList(data)
        .then(res => {
          this.tableData = res.data.list
          this.loading = false
          this.total = res.data.total
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 新增
    add() {
      this.isEdit = false
      this.isShowDialog = true
    },
    // sizeChange
    handleSizeChange(size) {
      this.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    handleCurrentChange(page) {
      this.pageNum = page
      this.axiosTable(page)
    },
    // 编辑
    edit(row) {
      this.isEdit = true
      this.form = row
      this.isShowDialog = true
    },
    remove(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          orgDelete({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    // 导出
    exportTable() {
      const header = ['id', 'name', 'appTag', 'url', 'appSign', 'typeName']
      const data = this.formatJson(header, this.tableData)
      import('@/utils/export2excel').then(excel => {
        excel.export_json_to_excel({
          header: ['应用ID', '应用名称', '应用类型', '主页路径', '分类', '应用分组'],
          data,
          filename: '应用管理',
          autoWidth: true,
          bookType: 'xlsx'
        })
      })
    },
    submit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          orgSaveOrUpdate(this.form)
            .then(res => {
              if (res.code === 200) {
                this.$message.success(res.message)
                this.query()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch(() => {})
            this.isShowDialog = false
            this.isEdit = false
            this.reset()
        }
      })

      // this.$refs['innerForm'].validate(value => {
      //   if (value) {
      //   } else {
      //     return false
      //   }
      // })
    },
    // 弹框取消
    cancel() {
      this.form = this.$options.data().form
      this.isShowDialog = false
    },
    // 自定义上传图片
    httpRequest(file) {
      if (file.file.size > 10 * 1024) {
        this.$message.warning('上传图片大小不能超过10kb!')
      } else {
        let that = this
        const reader = new FileReader()
        reader.readAsDataURL(file.file)
        reader.onload = function (e) {
          let base64 = e.target.result.split(',')[1]
          let imageUrl = `data:${file.file.type};base64,${base64}`
          that.imageUrl = imageUrl
          that.innerForm.image = imageUrl
        }
      }
    },
    // 获取机构下拉列表
    getDepts() {
      const data = { dictType: 'sys_org_type' }
      getAllDataList(data).then(res => {
        this.depts = res.data
      })
    },
    // 获取机构下拉列表
    getBaseArea() {
      const data = { dictType: 'sys_base_area' }
      getAllDataList(data).then(res => {
        this.baseAreas = res.data
      })
    },
    // 对需要导出的数据做处理
    formatJson(header, data) {
      return data.map(v => {
        if (v.appTag === 'BS') {
          v.appTag = '网页'
        } else {
          v.appTag = '客户端'
        }
        if (v.appSign === '1') {
          v.appSign = '平台应用'
        } else {
          v.appSign = '第三方应用'
        }
        return header.map(j => {
          return v[j]
        })
      })
    },
    reset() {
      this.total = 0
      this.pageSize = 10
      this.pageNum = 1
      this.form = this.$options.data().form
    }
  }
}
</script>

<style lang="scss" scoped>
.apps-manage {
  ::v-deep .el-dialog1 .el-dialog{
      width: 400px !important;
      .pciminchen {
        .titlename {
          margin-right: 10px;
        }
        .el-select {
          width: 280px;
        }
      }
    }
  .form-col {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    margin-top: 10px;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        &::before {
          content: '';
          width: 4px;
          height: 14px;
          background: #1273ce;
          display: inline-block;
          margin-right: 4px;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
      img {
        height: 36px;
        width: 36px;
      }
      .blue {
        color: #1273ce;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
      .red {
        color: #ff4645;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
::v-deep .el-dialog {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
  }
  .avatar {
    width: 60px;
    height: 60px;
    display: block;
  }
  .tips {
    color: #949699;
  }
}
.edit-form-wrap {
  position: relative;
  width: 100%;
  padding: 0 20px;
}
</style>
