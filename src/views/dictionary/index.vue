<template>
  <div class="home-container apps-manage">
    <div class="table-part">
      <div class="form-col">
        <el-button type="primary" plain size="mini" @click="addType()">新增</el-button>
        <el-form size="small" :model="searchTypeForm" inline>
          <el-form-item>
            <el-select
              v-model="searchTypeForm.hosptialId"
              clearable
              placeholder="字典类型"
            >
              <el-option
                v-for="item in depts"
                :key="item.hospitalId"
                :label="item.hospitalName"
                :value="item.hospitalId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item labe="字典名称搜索">
            <el-input
              v-model="searchTypeForm.keyword"
              placeholder="请输入关键字查找"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="queryType">查 询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table-col">
        <div class="header">
          <div class="text">字典类型</div>
          <div>
            <el-button class="el-button--custom" size="small" @click="exportTable">
              导 出
            </el-button>
          </div>
        </div>
        <el-table
          :data="typeTable"
          v-loading="loading"
          highlight-current-row
          @current-change="setTypeRowActive"
          size="small"
          border
          :header-cell-style="{
            'background': '#F2F4F7',
            'color': '#303133',
            'font-weight': 600
          }"
          tooltip-effect="dark"
          width="100%"
        >
          <el-table-column type="index" width="50" label="序号" />
          <el-table-column label="字典名称" prop="dictName" />
          <el-table-column label="字典类型" prop="dictType" />
          <el-table-column label="状态" prop="status">
            <template slot-scope="props">
              <span>{{ props.row.status === 0 ? '正常' : '停用' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注" prop="remark" />
          <el-table-column label="创建时间" prop="createTime" />
          <el-table-column label="操作">
            <template slot-scope="props">
              <span class="blue" @click="removeType(props.row)">删除</span>
              <span class="blue" @click="editType(props.row)">编辑</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="handleTypeSizeChange"
            @current-change="handleTypeCurrentChange"
            :current-page.sync="typePageNum"
            background
            size="small"
            :page-size="typePageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="typeTotal"
          ></el-pagination>
        </div>
      </div>
    </div>
    <div class="table-part">
      <div class="form-col">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="addData()"
          :disabled="!activeDictType"
        >
          新增
        </el-button>
        <el-form size="small" :model="searchDataForm" inline>
          <el-form-item>
            <el-input
              v-model="searchDataForm.hosptialId"
              placeholder="请输入字典标签"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="searchDataForm.hosptialId"
              placeholder="请输入字典键值"
            ></el-input>
          </el-form-item>
          <el-form-item labe="搜索">
            <el-input
              v-model="searchDataForm.keyword"
              placeholder="请输入关键字查找"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="queryData">查 询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="table-col">
        <div class="header">
          <div class="text">字典数据</div>
          <div>
            <el-button class="el-button--custom" size="small" @click="exportTable1">
              导 出
            </el-button>
          </div>
        </div>
        <el-table
          :data="dataTable"
          v-loading="loading"
          size="small"
          border
          :header-cell-style="{
            'background': '#F2F4F7',
            'color': '#303133',
            'font-weight': 600
          }"
          tooltip-effect="dark"
          width="100%"
        >
          <el-table-column type="index" width="50" label="序号" />
          <el-table-column label="字典标签" prop="dictLabel" />
          <el-table-column label="字典键值" prop="dictValue" />
          <el-table-column label="字典排序" prop="dictSort" />
          <el-table-column label="状态" prop="status">
            <template slot-scope="props">
              <span>{{ props.row.status === 0 ? '正常' : '停用' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" />
          <el-table-column label="操作">
            <template slot-scope="props">
              <span class="blue" @click="removeData(props.row)">删除</span>
              <span class="blue" @click="editData(props.row)">编辑</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @size-change="handleDataSizeChange"
            @current-change="handleDataCurrentChange"
            :current-page.sync="dataPageNum"
            background
            size="small"
            :page-size="dataPageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="dataTotal"
          ></el-pagination>
        </div>
      </div>
    </div>
    <!-- 字典类型内容弹窗 -->
    <el-dialog
      :title="isEditType ? '编辑类型' : '新增类型'"
      :visible.sync="isShowTypeDialog"
      width="700px"
    >
      <div class="edit-form-wrap">
        <el-form ref="form" :model="typeForm" label-width="150px">
          <el-form-item label="字典名称">
            <el-input v-model="typeForm.dictName"></el-input>
          </el-form-item>
          <el-form-item label="字典类型">
            <el-input v-model="typeForm.dictType"></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="typeForm.status" placeholder="请选择是否启用">
              <el-option label="停用" :value="1"></el-option>
              <el-option label="正常" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="typeForm.remark"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="typeSubmit" size="small">提 交</el-button>
        <el-button @click="cancel" size="small">取消</el-button>
      </span>
    </el-dialog>
    <!-- 字典数据弹窗 -->
    <el-dialog
      :title="isEditData ? '编辑数据内容' : '新增数据内容'"
      :visible.sync="isShowDataDialog"
      width="700px"
    >
      <div class="edit-form-wrap">
        <el-form ref="form" :model="dataForm" label-width="150px">
          <el-form-item label="字典标签">
            <el-input v-model="dataForm.dictLabel"></el-input>
          </el-form-item>
          <el-form-item label="字典键值">
            <el-input v-model="dataForm.dictValue"></el-input>
          </el-form-item>
          <el-form-item label="字典排序">
            <el-input v-model="dataForm.dictSort"></el-input>
          </el-form-item>
          <el-form-item label="字典状态">
            <el-select v-model="dataForm.status" placeholder="请选择是否启用">
              <el-option label="停用" :value="1"></el-option>
              <el-option label="正常" :value="0"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dataSubmit" size="small">提 交</el-button>
        <el-button @click="cancel" size="small">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  dictTypeList,
  dictTypeSaveOrUpdate,
  dictDataList,
  dictDataSaveOrUpdate,
  dictTypeDelete,
  dictDataDelete
} from '@/api/dictionary'
import moment from 'moment'
import XLSX from "xlsx-js-style"
export default {
  data() {
    return {
      // ---健康卡---
      loading: false,
      isShowTypeDialog: false,
      isShowDataDialog: false,
      typeTable: [],
      dataTable: [],
      typeTotal: 0,
      typePageSize: 10,
      typePageNum: 1,
      dataTotal: 0,
      dataPageSize: 10,
      dataPageNum: 1,
      depts: [],
      searchTypeForm: {
        dictName: '',
        dictType: ''
      },
      searchDataForm: {
        keyword: '',
        hospitalId: '',
        date: []
      },
      typeForm: {
        id: '',
        dictName: '',
        dictType: '',
        status: '',
        remark: ''
      },
      dataForm: {
        id: '',
        dictLabel: '',
        dictValue: '',
        dictSort: '',
        status: ''
      },
      isEditType: false,
      isEditData: false,
      activeDictType: '' // 选中的字典类型
    }
  },
  mounted() {},
  created() {
    this.init()
  },
  methods: {
    // 查询
    queryType() {
      this.typePageNum = 1
      this.axiosType()
    },
    // 查询
    queryData() {
      this.dataPageNum = 1
      const data = {
        pageNum: this.dataPageNum,
        pageSize: this.dataPageSize,
        dictType: this.activeDictType
      }
      dictDataList(data)
        .then(res => {
          this.dataTable = res.data.list
          this.dataTotal = res.data.total
        })
        .catch(e => {})
    },
    // 表格默认请求
    axiosType() {
      this.loading = true
      const data = {
        pageNum: this.typePageNum,
        pageSize: this.typePageSize,
        dictName: this.searchTypeForm.dictName,
        dictType: this.searchTypeForm.dictType
      }
      dictTypeList(data)
        .then(res => {
          this.typeTable = res.data.list
          this.loading = false
          this.typeTotal = res.data.total
        })
        .catch(e => {
          this.loading = false
        })
    },
    // 新增类型
    addType() {
      this.typeForm = this.$options.data().typeForm
      this.isShowTypeDialog = true
    },
    // 新增数据
    addData() {
      this.dataForm = this.$options.data().dataForm
      this.isShowDataDialog = true
    },
    // sizeChange
    handleTypeSizeChange(size) {
      this.typePageSize = size
      this.axiosType(1)
    },
    // 翻页
    handleTypeCurrentChange(page) {
      this.typePageNum = page
      this.axiosType(page)
    },
    // sizeChange
    handleDataSizeChange(size) {
      this.dataPageSize = size
      this.axiosData(1)
    },
    // 翻页
    handleDataCurrentChange(page) {
      this.dataPageNum = page
      this.axiosData(page)
    },
    // 编辑类型
    editType(row) {
      this.typeForm = row
      this.isShowTypeDialog = true
    },
    // 编辑数据
    editData(row) {
      this.dataForm = row
      this.isShowDataDialog = true
    },
    // 弹框取消
    cancel() {
      this.dataForm = this.$options.data().dataForm
      this.isShowTypeDialog = false
      this.isShowDataDialog = false
    },
    removeType(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          dictTypeDelete({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.queryType()
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    removeData(row) {
      this.$confirm('是否确定删除, 删除后不可恢复?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          dictDataDelete({
            id: row.id
          }).then(res => {
            if (res.code === 200) {
              this.$message.success(res.message)
              this.queryData()
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {})
    },
    exportTable () {
      if (this.typeTable.length == 0) {
        return 
      }
      let excelData = [
        ['字典类型列表', null, null, null], // 标题
        ['字典名称', '字典类型', '状态', '备注', '创建时间'], // 表头
      ]
      let arr = JSON.parse(JSON.stringify(this.typeTable))
      arr.forEach((item)=>{
        if (item.status == 0) {
          item.statusName = '正常'
        } else {
          item.statusName = '停用'
        }
      })
      // 需要导出的数据
      arr.forEach((item)=>{
        excelData.push([item.dictName,item.dictType,item.statusName,item.remark,item.createTime])
      })
      const wb = XLSX.utils.book_new();//创建新工作簿
      const ws = XLSX.utils.aoa_to_sheet(excelData);//aoa_to_sheet将 JS 数据数组的数组转换为工作表。
      // 设置标题行单元格合并
      // s即start, e即end, r即row, c即column
      let mergerarr = []
      ws['!merges'] = mergerarr;
      // 设置宽度
      // !cols 设置列宽
      // cols 为一个对象数组，依次表示每一列的宽度。
      ws["!cols"] = [
        { wpx: 200, },
        { wpx: 120, },
        { wpx: 220, },
        { wpx: 200, },
        { wpx: 200, },
      ];
      // !rows 设置行高
      // rows 为一个对象数组，依次表示每一行的高度
      const rows = [
        { hpx: 20 },
        { hpx: 16 },
        { hpx: 18 },
        { hpx: 28 },
        { hpx: 20 },
      ]
      ws['!rows'] = rows; // 添加到sheet中
      let borderAll = { //单元格外侧框线
        top: {
          style: 'thin',//BORDER_STYLE 是用来设置边框样式的一个字符串，
        },
        bottom: {
          style: 'thin'
        },
        left: {
          style: 'thin'
        },
        right: {
          style: 'thin'
        }
      };
      // 设置每一个单元格
      for (let key in ws) {
        if (ws[key] instanceof Object) {
          if (key === 'B2') {//给B2这个单元格单独设置样式和背景颜色
            ws[key].s = {
              border: borderAll,
              alignment: {
                horizontal: 'center', //水平居中对齐
                vertical: 'center',//垂直居中
                wrapText: 1,//自动换行
              },
              font: {
                sz: 12,//单元格中字体的样式与颜色设置
                color: {
                  // rgb: 'FF0187FA'
                }
              },
              bold: false,//是否加粗
              numFmt: 0,
              // fill 颜色填充属性
              fill: {
                // fgColor: { rgb: '87CEEB' },
              },
            }
          } else {
            ws[key].s = {
              border: borderAll,
              alignment: {
                horizontal: 'center', //水平居中对齐
                vertical: 'center',//垂直居中
                wrapText: 1,//自动换行
              },
              font: {
                sz: 12,//单元格中字体的样式与颜色设置
                color: {
                  // rgb: 'FF0187FA'
                }
              },
              bold: false,//是否加粗
              numFmt: 0,
              // fill 颜色填充属性
              fill: {
                fgColor: { rgb: 'FFFFFF' },
              },
            }
          }
        }
      };
      XLSX.utils.book_append_sheet(wb, ws, "字典类型列表");//将上面创建好的ws页添加到wb工作簿中表格内名字为readmedemo
      XLSX.writeFile(wb, "字典类型列表.xlsx");////调用导出api
      this.$message({
          message: '导出成功！',
          type: 'success'
      });
    },
    exportTable1 () {
      if (this.dataTable.length == 0) {
        return 
      }
      let excelData = [
        ['字典数据列表', null, null, null], // 标题
        ['字典标签', '字典键值', '字典排序', '状态', '创建时间'], // 表头
      ]
      let arr = JSON.parse(JSON.stringify(this.dataTable))
      arr.forEach((item)=>{
        if (item.status == 0) {
          item.statusName = '正常'
        } else {
          item.statusName = '停用'
        }
      })
      // 需要导出的数据
      arr.forEach((item)=>{
        excelData.push([item.dictLabel,item.dictValue,item.dictSort,item.statusName,item.createTime])
      })
      const wb = XLSX.utils.book_new();//创建新工作簿
      const ws = XLSX.utils.aoa_to_sheet(excelData);//aoa_to_sheet将 JS 数据数组的数组转换为工作表。
      // 设置标题行单元格合并
      // s即start, e即end, r即row, c即column
      let mergerarr = []
      ws['!merges'] = mergerarr;
      // 设置宽度
      // !cols 设置列宽
      // cols 为一个对象数组，依次表示每一列的宽度。
      ws["!cols"] = [
        { wpx: 200, },
        { wpx: 120, },
        { wpx: 220, },
        { wpx: 200, },
        { wpx: 200, },
      ];
      // !rows 设置行高
      // rows 为一个对象数组，依次表示每一行的高度
      const rows = [
        { hpx: 20 },
        { hpx: 16 },
        { hpx: 18 },
        { hpx: 28 },
        { hpx: 20 },
      ]
      ws['!rows'] = rows; // 添加到sheet中
      let borderAll = { //单元格外侧框线
        top: {
          style: 'thin',//BORDER_STYLE 是用来设置边框样式的一个字符串，
        },
        bottom: {
          style: 'thin'
        },
        left: {
          style: 'thin'
        },
        right: {
          style: 'thin'
        }
      };
      // 设置每一个单元格
      for (let key in ws) {
        if (ws[key] instanceof Object) {
          if (key === 'B2') {//给B2这个单元格单独设置样式和背景颜色
            ws[key].s = {
              border: borderAll,
              alignment: {
                horizontal: 'center', //水平居中对齐
                vertical: 'center',//垂直居中
                wrapText: 1,//自动换行
              },
              font: {
                sz: 12,//单元格中字体的样式与颜色设置
                color: {
                  // rgb: 'FF0187FA'
                }
              },
              bold: false,//是否加粗
              numFmt: 0,
              // fill 颜色填充属性
              fill: {
                // fgColor: { rgb: '87CEEB' },
              },
            }
          } else {
            ws[key].s = {
              border: borderAll,
              alignment: {
                horizontal: 'center', //水平居中对齐
                vertical: 'center',//垂直居中
                wrapText: 1,//自动换行
              },
              font: {
                sz: 12,//单元格中字体的样式与颜色设置
                color: {
                  // rgb: 'FF0187FA'
                }
              },
              bold: false,//是否加粗
              numFmt: 0,
              // fill 颜色填充属性
              fill: {
                fgColor: { rgb: 'FFFFFF' },
              },
            }
          }
        }
      };
      XLSX.utils.book_append_sheet(wb, ws, "字典数据列表");//将上面创建好的ws页添加到wb工作簿中表格内名字为readmedemo
      XLSX.writeFile(wb, "字典数据列表.xlsx");////调用导出api
      this.$message({
          message: '导出成功！',
          type: 'success'
      });
    },
    // 导出
    // exportTable() {
    //   const header = ['id', 'name', 'appTag', 'url', 'appSign', 'typeName']
    //   const data = this.formatJson(header, this.typeTable)
    //   import('@/utils/export2excel').then(excel => {
    //     excel.export_json_to_excel({
    //       header: ['应用ID', '应用名称', '应用类型', '主页路径', '分类', '应用分组'],
    //       data,
    //       filename: '应用管理',
    //       autoWidth: true,
    //       bookType: 'xlsx'
    //     })
    //   })
    // },
    // 类型提交
    typeSubmit() {
      dictTypeSaveOrUpdate(this.typeForm)
        .then(res => {
          if (res.code === 200) {
            this.$message.success(res.message)
            this.queryType()
            this.resetType()
            this.isShowTypeDialog = false
          } else {
            this.$message.error(res.message)
          }
        })
        .catch(e => {})
    },
    // 数据提交
    dataSubmit() {
      const data = {
        id: this.dataForm.id,
        dictType: this.activeDictType,
        dictLabel: this.dataForm.dictLabel,
        dictValue: this.dataForm.dictValue,
        dictSort: this.dataForm.dictSort,
        status: this.dataForm.status
      }
      dictDataSaveOrUpdate(data)
        .then(res => {
          if (res.code === 200) {
            this.$message.success(res.message)
            this.queryData()
          } else {
            this.$message.error(res.message)
          }
        })
        .catch(e => {})
      this.resetData()
      this.isShowDataDialog = false
    },
    resetType() {
      this.typeTotal = 0
      this.typePageSize = 10
      this.typePageNum = 1
      this.typeForm = this.$options.data().typeForm
      this.dataTable = []
      this.activeDictType = ''
    },
    resetData() {
      this.dataTotal = 0
      this.dataPageSize = 10
      this.typePageNum = 1
      this.dataForm = this.$options.data().dataForm
    },
    // 初始化
    async init() {
      await this.queryType()
    },
    setTypeRowActive(data) {
      this.dataPageNum = 1
      this.activeDictType = data.dictType
      this.queryData()
    }
  }
}
</script>

<style lang="scss" scoped>
.apps-manage {
  .form-col {
    display: flex;
    justify-content: space-between;
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
      .el-date-editor {
        width: 240px;
      }
    }
  }
  .table-col {
    height: 100%;
    background: #fff;
    padding: 10px 20px;
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .text {
        &::before {
          content: '';
          width: 4px;
          height: 14px;
          background: #1273ce;
          display: inline-block;
          margin-right: 4px;
        }
      }
    }
    .el-table ::v-deep {
      margin-top: 16px;
      .current-row {
        background: #d4eaff !important;
      }
      img {
        height: 36px;
        width: 36px;
      }
      .blue {
        color: #1273ce;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
      .red {
        color: #ff4645;
        font-size: 14px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
}
::v-deep .el-dialog {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
  }
  .avatar {
    width: 60px;
    height: 60px;
    display: block;
  }
  .tips {
    color: #949699;
  }
}
.edit-form-wrap {
  position: relative;
  width: 100%;
  padding: 0 20px;
}
.table-part {
  margin-bottom: 20px;
}
</style>
