<template>
  <div class="home-container desensitization">
    <div class="form-col">
      <el-form :model="searchForm" inline size="small" label-width="90px">
        <el-form-item label="数据名称">
          <el-input v-model.trim="searchForm.dtName" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" clearable>
            <el-option label="有效" value="1" />
            <el-option label="无效" value="0" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button size="small" @click="reset">重置</el-button>
          <el-button size="small" type="primary" @click="query">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-col">
      <div class="header">
        <el-col :span="12" class="title">数据列表</el-col>
        <el-col :span="12" class="select">
          <el-button size="small" type="primary" @click="addData"
            >新增数据</el-button
          >
        </el-col>
      </div>
      <el-table
        :data="tableData"
        v-loading="loading"
        size="small"
        :header-cell-style="{
          background: '#F2F4F7',
          color: '#303133'
        }"
        tooltip-effect="dark"
        width="100%"
        border
      >
        <!-- <el-table-column label="编号"  type="index" width="60" /> -->
        <el-table-column label="数据名称" prop="dtName" show-overflow-tooltip />
        <el-table-column
          label="脱敏方式"
          prop="dtMethod"
          show-overflow-tooltip
        />
        <el-table-column
          label="脱敏规则"
          prop="rangeName"
          show-overflow-tooltip
        >
          <template slot-scope="props">
            <!-- <span>{{
							props.row.isFrontHide !== '0'
								? `前${props.row.frontNum}位脱敏`
								: props.row.isMiddleHide !== '0'
								? `中间${props.row.middleStartNum} - ${props.row.middleEndNum}位脱敏`
								: props.row.isRearHide !== '0'
								? `后${props.row.rearNum}位脱敏`
								: '不脱敏'
						}}</span> -->
            <span
              v-if="
                props.row.isFrontHide === '0' &&
                props.row.isMiddleHide === '0' &&
                props.row.isRearHide === '0'
              "
            >
              不脱敏
            </span>
            <span v-else
              >{{
                props.row.isFrontHide !== '0'
                  ? `前${props.row.frontNum}位脱敏`
                  : ''
              }}
              {{
                props.row.isMiddleHide !== '0'
                  ? `中间${props.row.middleStartNum} - ${props.row.middleEndNum}位脱敏`
                  : ''
              }}
              {{
                props.row.isRearHide !== '0'
                  ? `后${props.row.rearNum}位脱敏`
                  : ''
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="更新时间"
          prop="modifyTime"
          show-overflow-tooltip
        />
        <el-table-column
          label="更新用户名称"
          prop="modifyName"
          show-overflow-tooltip
        />
        <el-table-column
          label="状态是否有效"
          show-overflow-tooltip
        >
          <template slot-scope="props">
            <!-- <el-switch
              active-color="#1273CE"
              v-model="props.row.status"
              class="switch"
              :active-value="1"
              inactive-color="#F2F4F7"
              :inactive-value="0"
              active-text="是"
              inactive-text="否"
              @change="changeSwitch($event, props.row)"
            /> -->
            <span>
              {{ props.row.status === 1 ? '有效' : '无效' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          show-overflow-tooltip
          fixed="right"
          width="160"
        >
          <template slot-scope="props">
            <el-button
              type="text"
              size="small"
              class="blue"
              @click="edit(props.row)"
              >编辑</el-button
            >
            <el-button
              type="text"
              size="small"
              class="blue"
              @click="verification(props.row)"
              >验证</el-button
            >
            <el-button
              type="text"
              size="small"
              class="red"
              @click="remove(props.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="sizeChange"
          @current-change="currentPage"
          :current-page.sync="searchForm.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="searchForm.pageSize"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      :title="addEditDialogTitle"
      :visible.sync="showAddEditDialog"
      width="30%"
      @closed="handleClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        :model="addEditForm"
        size="small"
        ref="addEditForm"
        :rules="addEditFormRules"
        label-width="120px"
      >
        <el-form-item label="数据名称:" prop="dtName">
          <el-input
            v-model="addEditForm.dtName"
            :disabled="addEditDialogTitle === '编辑脱敏数据'"
          />
          <!-- <el-select
            v-model="addEditForm.dtField"
            clearable
            value-key="dtCode"
            :disabled="addEditDialogTitle === '编辑脱敏数据'"
          >
            <el-option
              :value="item"
              :label="item.dtName"
              v-for="(item, index) in selectData"
              :key="index"
            />
          </el-select> -->
        </el-form-item>
        <el-form-item
          :label="index === 0 ? '数据识别信息:' : ''"
          v-for="(item, index) in addEditForm.fieldConfigList"
          :key="index"
          :prop="'fieldConfigList.' + index + '.dtColumn'"
          :rules="[
            {
              required: true,
              message: '请输入数据识别信息',
              trigger: 'blur'
            },
            {
              validator: (rule, value, callback) =>
                checkIsRepeat(rule, value, callback),
              trigger: 'change'
            }
          ]"
        >
          <el-input v-model="item.dtColumn" />
          <span class="form-btns">
            <el-button
              @click.prevent="addFormItem"
              class="blue"
              icon="el-icon-plus"
            ></el-button>
            <el-button
              v-if="index !== 0"
              @click.prevent="removeFormItem(item)"
              class="red"
              icon="el-icon-minus"
            ></el-button
          ></span>
        </el-form-item>
        <el-form-item label="脱敏方式:">
          <span>*</span>
        </el-form-item>
        <el-form-item label="脱敏规则:" prop="frontNum">
          <el-form-item class="custom-form-item">
            <span>前</span>
            <el-input v-model.number="addEditForm.frontNum" clearable />
            <span>位进行</span>
            <el-select v-model="addEditForm.isFrontHide">
              <el-option value="1" label="脱敏" />
              <el-option value="0" label="不脱敏" />
            </el-select>
          </el-form-item>
          <el-form-item class="custom-form-item">
            <span>中</span>
            <el-input
              v-model.number="addEditForm.middleStartNum"
              class="custom-input"
            />
            <span>-</span>
            <el-input
              v-model.number="addEditForm.middleEndNum"
              class="custom-input"
            />
            <span>位进行</span>
            <el-select v-model="addEditForm.isMiddleHide">
              <el-option value="1" label="脱敏" />
              <el-option value="0" label="不脱敏" />
            </el-select>
          </el-form-item>
          <el-form-item class="custom-form-item">
            <span>后</span>
            <el-input v-model.number="addEditForm.rearNum" clearable />
            <span>位进行</span>
            <el-select v-model="addEditForm.isRearHide">
              <el-option value="1" label="脱敏" />
              <el-option value="0" label="不脱敏" />
            </el-select>
          </el-form-item>
        </el-form-item>
        <el-form-item label="状态:">
          <el-radio-group v-model="addEditForm.status">
            <el-radio :label="1" border>有效</el-radio>
            <el-radio :label="0" border>无效</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" size="small">取 消</el-button>
        <el-button type="primary" @click="desensitizationSave" size="small"
          >保 存</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="验证测试"
      :visible.sync="showVerificationDialog"
      width="30%"
      @closed="handleCloseVerification"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        :model="verificationForm"
        size="small"
        class="verificationForm"
        ref="verificationForm"
        label-width="90px"
        :rules="verificationFormRules"
      >
        <el-form-item label="测试数据:" prop="text">
          <el-col :span="20"
            ><el-input v-model.trim="verificationForm.text" clearable
          /></el-col>
          <el-col :span="4"
            ><el-button @click="checkTest">测 试</el-button></el-col
          >
        </el-form-item>
        <el-form-item label="脱敏结果:">
          <el-input v-model.trim="desensitizationResults" clearable />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseVerification" size="small"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getFieldList,
  desensitizeParamsConfigList,
  desensitizeParamsConfigSave,
  desensitizeParamsConfigDelete,
  desensitizeParamsConfigTest
} from '@/api/desensitization'
import {getWindowName} from '@/utils/auth'
export default {
  data() {
    const customValidator = (rules, value, callback) => {
      let {
        frontNum,
        isFrontHide,
        middleStartNum,
        middleEndNum,
        isMiddleHide,
        rearNum,
        isRearHide
      } = this.addEditForm
      if (isFrontHide === '0' && isMiddleHide === '0' && isRearHide === '0') {
        callback()
      } else if (
        isFrontHide === '0' &&
        isMiddleHide === '0' &&
        isRearHide === '1'
      ) {
        if (rearNum) {
          callback()
        } else {
          callback(new Error('请填写脱敏位数'))
        }
      } else if (
        isFrontHide === '0' &&
        isMiddleHide === '1' &&
        isRearHide === '1'
      ) {
        if (rearNum && middleStartNum && middleEndNum) {
          callback()
        } else {
          callback(new Error('请填写脱敏位数'))
        }
      } else if (
        isFrontHide === '1' &&
        isMiddleHide === '1' &&
        isRearHide === '1'
      ) {
        if (frontNum && middleStartNum && middleEndNum && rearNum) {
          callback()
        } else {
          callback(new Error('请填写脱敏位数'))
        }
      } else if (
        isFrontHide === '1' &&
        isMiddleHide === '0' &&
        isRearHide === '0'
      ) {
        if (frontNum) {
          callback()
        } else {
          callback(new Error('请填写脱敏位数'))
        }
      } else if (
        isFrontHide === '1' &&
        isMiddleHide === '1' &&
        isRearHide === '0'
      ) {
        if (frontNum && middleStartNum && middleEndNum) {
          callback()
        } else {
          callback(new Error('请填写脱敏位数'))
        }
      } else if (
        isFrontHide === '0' &&
        isMiddleHide === '1' &&
        isRearHide === '0'
      ) {
        if (middleStartNum && middleEndNum && rearNum) {
          callback()
        } else {
          callback(new Error('请填写脱敏位数'))
        }
      } else if (
        isFrontHide === '1' &&
        isMiddleHide === '0' &&
        isRearHide === '1'
      ) {
        if (frontNum && rearNum) {
          callback()
        } else {
          callback(new Error('请填写脱敏位数'))
        }
      } else {
        callback('请选择并填写脱敏规则')
      }
    }
    return {
      searchForm: {
        dtName: '',
        status: '',
        pageNum: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      loading: false,
      showAddEditDialog: false,
      addEditForm: {
        frontNum: '',
        isFrontHide: '',
        middleStartNum: '',
        middleEndNum: '',
        isMiddleHide: '',
        rearNum: '',
        isRearHide: '',
        status: 1,
        dtName: '',
        dtMethod: '*',
        regExp: '',
        remark: '',
        fieldConfigList: [
          {
            dtColumn: ''
          }
        ]
      },
      addEditFormRules: {
        dtName: {
          required: true,
          message: '请输入数据名称',
          tiagger: 'blur'
        },
        frontNum: [
          {
            validator: customValidator,
            required: true
          }
        ]
      },
      addEditDialogTitle: '',
      showVerificationDialog: false,
      verificationForm: {
        text: ''
      },
      selectData: [],
      desensitizationResults: '',
      verificationFormRules: {
        text: [{ required: true, message: '请输入测试数据', tiagger: 'blur' }]
      },
      deepRowData: {}
    }
  },
  mounted() {
    this.axiosTable(1)
    // this.getApi()
  },
  methods: {
    // 默认表格请求
    axiosTable(page = 1) {
      this.loading = true
      desensitizeParamsConfigList({
        ...this.searchForm,
        pageNum: page
      })
        .then((res) => {
          this.loading = false
          this.tableData = res.data
          this.total = res.total
        })
        .catch(() => {
          this.loading = false
        })
    },
    reset() {
      this.searchForm = this.$options.data().searchForm
      this.axiosTable(1)
    },
    query() {
      this.searchForm.pageNum = 1
      this.axiosTable(1)
    },
    // 新增数据
    addData() {
      this.showAddEditDialog = true
      this.addEditDialogTitle = '新增脱敏数据'
    },
    // 编辑
    edit(row) {
      this.showAddEditDialog = true
      this.addEditDialogTitle = '编辑脱敏数据'
      // 由于数据双向绑定的原因，在弹窗表单删除后会把表格的数据删除，则需要把row数据 深拷贝一份复制到addEditForm
      this.addEditForm = this.deepClone(row)
      delete this.addEditForm.createTime
    },
    // 验证
    verification(row) {
      this.showVerificationDialog = true
      this.verificationForm = {
        ...row,
        text: ''
      }
    },
    checkTest() {
      this.$refs['verificationForm'].validate((valid) => {
        if (valid) {
          desensitizeParamsConfigTest({
            text: this.verificationForm.text,
            id: this.verificationForm.id
          }).then((res) => {
            if (res.success) {
              this.desensitizationResults = res.data
            } else {
              this.desensitizationResults = res.data
            }
          })
        } else {
          return false
        }
      })
    },
    // 删除
    remove(row) {
      this.$confirm('您确定要删除此条规则吗?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          desensitizeParamsConfigDelete({
            id: row.id
          }).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        })
        .catch(() => {
          // this.$message({
          // 	type: 'info',
          // 	message: '已取消删除！'
          // })
        })
    },
    // 每页条数变化
    sizeChange(size) {
      this.searchForm.pageNum = 1
      this.searchForm.pageSize = size
      this.axiosTable(1)
    },
    // 翻页
    currentPage(page) {
      this.searchForm.pageNum = page
      this.axiosTable(page)
    },
    desensitizationSave() {
      this.$refs['addEditForm'].validate((valid) => {
        if (valid) {
          let {loginName} = getWindowName()
          let addEditForm = {
            ...this.addEditForm,
            modifyName:loginName
          }
          desensitizeParamsConfigSave(addEditForm).then((res) => {
            if (res.success) {
              this.$message.success(res.message)
              this.handleClose()
              this.axiosTable(this.searchForm.pageNum)
            } else {
              this.$message.error(res.message)
            }
          })
        } else {
          return false
        }
      })
    },
    // 关闭新增 编辑 弹窗
    handleClose() {
      this.$refs['addEditForm'].resetFields()
      this.showAddEditDialog = false
      this.addEditForm = this.$options.data().addEditForm
    },
    // 验证测试 关闭
    handleCloseVerification() {
      this.$refs['verificationForm'].resetFields()
      this.showVerificationDialog = false
      this.verificationForm = this.$options.data().verificationForm
      this.desensitizationResults = ''
    },
    getApi() {
      getFieldList().then((res) => {
        this.selectData = res.data
      })
    },
    changeSwitch(val, row) {
      desensitizeParamsConfigSave({
        ...row,
        status: val
      }).then((res) => {
        if (res.success) {
          this.$message.success(res.message)
          this.axiosTable(this.searchForm.pageNum)
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 表单新增
    addFormItem() {
      this.addEditForm.fieldConfigList.push({
        dtColumn: ''
      })
    },
    // 表单移除
    removeFormItem(item) {
      var index = this.addEditForm.fieldConfigList.indexOf(item)
      if (index !== -1) {
        this.addEditForm.fieldConfigList.splice(index, 1)
      }
    },
    // 校验数据识别信息 不能重复
    checkIsRepeat(rule, value, callback) {
      const newArr = this.addEditForm.fieldConfigList.map((el) => el.dtColumn)
      const isFlag = newArr.some(
        (el, index, arr) => arr.findIndex((item) => item === el) !== index
      )
      if (isFlag) {
        callback('输入数据识别信息不能重复')
      } else {
        callback()
      }
    },
    // 深拷贝对象
    deepClone(obj, hash = new WeakMap()) {
      if (obj === null) return obj //如果是null，则不需要做任何的操作，返回即可
      if (obj instanceof Date) return new Date(obj)
      if (obj instanceof RegExp) return new RegExp(obj)
      if (typeof obj !== 'object') return obj //如果是函数直接返回即可，没必要进行拷贝
      //上面是将不是object的直接返回出去
      if (hash.get(obj)) return hash.get(obj) //这里主要是剪枝的作用
      let cloneObj = new obj.constructor()
      //obj,constructor指向的是自己，相当于重新生成了一份新的自己给新的对象，这就实现了两个对象的属性拥有不同的地址
      hash.set(obj, cloneObj) //以键值对的方式存放,这也保证了唯一性
      for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
          //如果某个属性A下面还有属性B，则进入属性B，属性B处理完以后A就算处理完了，继续指向for in循环
          cloneObj[key] = this.deepClone(obj[key], hash)
        }
      }
      return cloneObj
    }
  }
}
</script>

<style lang="scss" scoped>
.desensitization {
  .form-col {
    padding: 16px 20px;
    background: #fff;
    .el-form-item {
      margin-bottom: 0;
    }
  }
  .table-col {
    padding: 20px;
    margin-top: 16px;
    background: #fff;
    min-height: calc(100% - 100px);
    .header {
      height: 32px;
      .title {
        color: #313133;
        font-size: 16px;
        font-weight: 600;
        line-height: 32px;
        width: 50%;
        text-align: left;
        &::before {
          content: '';
          display: inline-block;
          height: 14px;
          width: 4px;
          background: #1273ce;
          margin-right: 5px;
        }
      }
      .select {
        text-align: right;
      }
    }
    .el-table {
      margin-top: 10px;
      .blue {
        color: #1273ce;
      }
      .green,
      .yellow {
        &::before {
          content: '';
          display: inline-block;
          height: 8px;
          width: 8px;
          border-radius: 50%;
        }
      }
      .green {
        &::before {
          background: #6dc33c;
        }
      }
      .red {
        color: #ff4645;
        font-size: 14px;
      }
      .switch ::v-deep {
        .el-switch__label {
          position: absolute;
          display: none;
          color: #fff !important;
        }
        /*打开时文字位置设置*/
        .el-switch__label--right {
          z-index: 1;
        }
        /* 调整打开时文字的显示位子 */
        .el-switch__label--right span {
          margin-left: -10px;
        }
        /*关闭时文字位置设置*/
        .el-switch__label--left {
          z-index: 1;
        }
        /* 调整关闭时文字的显示位子 */
        .el-switch__label--left span {
          margin-left: 10px;
        }
        /*显示文字*/
        .el-switch__label.is-active {
          display: block;
        }
        .el-switch__core,
        .el-switch__label {
          width: 50px !important;
          margin: 0;
        }
      }
      .el-button {
        padding: 0;
      }
    }
    .pagination {
      text-align: right;
      margin-top: 10px;
    }
  }
  ::v-deep.el-dialog {
    .form-btns {
      padding-left: 10px;
      .blue {
        border-color: #196fc6;
        color: #196fc6;
      }
      .red {
        border-color: #ff7574;
        color: #ff7574;
      }
    }
    .custom-form-item {
      .el-input {
        width: 105px;
        margin: 0 10px;
      }
      .custom-input {
        width: 50px;
        padding-left: 5px;
        padding-right: 5px;
        margin: 0 5px;
        ::v-deep.el-input__inner {
          padding: 0 5px;
          // margin-left: 5px;
          text-align: center;
        }
      }
      .el-select {
        width: 94px;
        margin: 0 10px;
      }
      margin-bottom: 15px;
    }
    .el-radio-group ::v-deep {
      .el-radio {
        margin-right: 5px;
        margin-left: 5px;
      }
      .el-radio.is-bordered.is-checked {
        border-color: #1273ce;
        .el-radio__input.is-checked .el-radio__inner {
          border-color: #1273ce;
          background: #1273ce;
        }
        .el-radio__label {
          color: #1273ce;
        }
      }
    }
  }
  .verificationForm ::v-deep {
    padding-right: 20px;
    .el-col {
      &:last-child {
        padding-left: 10px;
        .el-button {
          border-color: #1273ce;
          color: #1273ce;
        }
      }
    }
  }
}
</style>