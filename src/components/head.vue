<template>
  <div class="header">
    <el-col :span="12" class="left-info">
      <!-- <img src="../../assets/images/hospital-logo.png" alt="" /> -->
      <!-- <span class="hospital-name">包头市全民健康信息平台</span> -->
      <!-- <span class="hospital-name">玉山县黄家驷医院（玉山县人民医院）</span> -->
      <!-- <span class="hospital-name">淮北矿工总院</span> -->
      <span class="hospital-name">
        <img :src="appLogo" alt="" v-if="appLogo" />
      </span>
      <span class="p_l_r">|</span>
      <span class="system-name">{{ systemName }}</span>
      <!-- <span class="system-name">全景电子病历患者视图 <span class="version">V1.0.6</span></span> -->
    </el-col>
    <el-col :span="12" class="right-info">
      <!-- <span class="ip">IP：{{ userInfo.ip }}</span> -->
      <span class="username">欢迎您！{{ userInfo.userName }}</span>
      <span class="time">{{ userInfo.formatTime }}</span>
      <span @click="logout" class="logout">
        <svg-icon iconClass="logout"></svg-icon>
        退出
      </span>
    </el-col>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import moment from 'moment'
import { getWindowName } from '@/utils/auth'
moment.locale('zh-cn')
function format(date) {
  let dateNow = moment(date).format('ll')
  let week = moment(date).format('dddd')
  return dateNow + ' ' + week
}
export default {
  components: {
    Breadcrumb
  },
  computed: {
    ...mapGetters(['avatar', 'systemName', 'appLogo'])
  },
  data() {
    return {
      // userInfo 应为后台返回
      userInfo: {
        ip: '***************',
        userName: this.$store.state.user.name,
        formatTime: format(new Date())
      }
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      // this.$router.push(`/login?redirect=${this.$route.fullPath}`)
      window.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  height: 56px;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background: #032e7e;
  color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0 24px;
  .left-info {
    img,
    span {
      display: inline-block;
    }
    img {
      height: 40px;
      vertical-align: middle;
      // margin-top: 18px;
    }
    span {
      // height: 56px;
      line-height: 56px;
      color: #fff;
      // vertical-align: bottom;
    }
    .hospital-name {
      font-size: 20px;
      padding-left: 12px;
    }
    .p_l_r {
      padding: 0 16px;
    }
    .system-name {
      font-size: 16px;
      font-weight: 600;
      font-family: PingFangSC-Semibold, PingFang SC;
      .version {
        padding-left: 15px;
      }
    }
  }
  .right-info {
    color: #fff;
    text-align: right;
    span {
      display: inline-block;
      height: 56px;
      line-height: 56px;
    }
    .time,
    .username,
    .ip {
      margin-right: 32px;
    }
    .logout {
      cursor: pointer;
      user-select: none;
    }
  }
}
</style>
