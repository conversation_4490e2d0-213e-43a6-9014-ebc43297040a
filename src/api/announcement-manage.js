import request from '@/utils/request'
/**
 * 查询表格
 * @param {title:'',startTime:'',endTime:'',pageNum:1,pageSize:10} data 
 * @returns 
 */
export function announcementList(data) {
  return request({
    url: '/announcement/list',
    method: 'post',
    data
  })
}
/**
 * 新增
 * @param {title:'',content:'',fileIds:'文件ID 多个用,分割',createUser:10} data 
 * @returns 
 */
export function announcementRegister(data) {
  return request({
    url: '/announcement/register',
    method: 'post',
    data
  })
}
/**
 * 编辑
 * @param {title:'',content:'',fileIds:'文件ID 多个用,分割',createUser:'',id:''} data 
 * @returns 
 */
export function announcementUpdate(data) {
  return request({
    url: '/announcement/update',
    method: 'post',
    data
  })
}
/**
 * 删除
 * @param {id:''} data 
 * @returns 
 */
export function announcementRemove(data) {
  return request({
    url: '/announcement/remove',
    method: 'post',
    data
  })
}
/**
 * 详情
 * @param {id:''} data 
 * @returns 
 */
export function announcementInfo(data) {
  return request({
    url: '/announcement/info',
    method: 'post',
    data
  })
}
/**
 * 文件上传
 * @param {file:'文件路径'} data 
 * @returns 
 */
export function announcementUploadFile(data) {
  return request({
    url: '/announcement/upload-file',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  })
}
/**
 * 文件下载
 * @param {filePath:''} data 
 * @returns 
 */
export function announcementDownLoad(data) {
  return request({
    url: '/announcement/download',
    method: 'post',
    responseType:'blob',
    data
  })
}
/**
 * 获取科室列表
 * @param {} data 
 * @returns 
 */
export function getDeptList() {
  return request({
    url: 'user/deptList',
    method: 'get',
  })
}