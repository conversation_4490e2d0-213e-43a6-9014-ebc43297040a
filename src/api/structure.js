import request from '@/utils/request'

/**
 * 机构列表
 * @param
 * @returns
 */
export function orgList(data) {
  return request({
    url: '/org/list',
    method: 'post',
    data
  })
}

/**
 * 删除机构
 * @param
 * @returns
 */
export function orgDelete(params) {
  return request({
    url: '/org/delete',
    method: 'delete',
    params
  })
}

/**
 * 新增或编辑机构
 * @param
 * @returns
 */
export function orgSaveOrUpdate(data) {
  return request({
    url: '/org/saveOrUpdate',
    method: 'post',
    data
  })
}
/**
 * 模版导出
 * @param
 * @returns
 */
export function downloadT(data) {
  return request({
    url: '/org/download/template',
    method: 'post',
    data
  })
}
