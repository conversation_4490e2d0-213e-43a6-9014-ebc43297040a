import request from '@/utils/request'
/**
 * 修改应用的系统名称
 * @param {appId:'',systemName:'1'} data 
 * @returns 
 */
export function updateName(data) {
  return request({
    url: '/app/updateName',
    method: 'post',
    data
  })
}
/**
 * 上传医院logo
 * @param {casual:'',sign:'1'} data 
 * @returns 
 */
export function addSign(data) {
  return request({
    url: '/app/addSign',
    method: 'post',
    data
  })
}
/**
 * 根据appId查询上次保存的内容
 * @param {appId:''} data 
 * @returns 
 */
export function selectById(params) {
  return request({
    url: '/app/selectById',
    method: 'get',
    params
  })
}
/**
 * 根据appId查询上次保存的内容
 * @param {appId:''} data 
 * @returns 
 */
export function getAppAll() {
  return request({
    url: '/app/getAppAll',
    method: 'get',
  })
}
