import request from '@/utils/request'
/**
 * 查询表格
 * @param {effective:'是否解决   1:已解决  0:未解决',name:'',roleId:'',startTime:'',endTime:'',pageNum:'1',pageSize:'10'} data 
 * @returns 
 */
export function opinionList(data) {
  return request({
    url: '/opinion/list',
    method: 'post',
    data
  })
}
/**
 * 解决
 * @param {id:''} data 
 * @returns 
 */
export function opinionUpdate(data) {
  return request({
    url: '/opinion/update-effective',
    method: 'post',
    data
  })
}
/**
 * 删除
 * @param {id:''} data 
 * @returns 
 */
export function opinionRemove(data) {
  return request({
    url: '/opinion/remove',
    method: 'post',
    data
  })
}