import request from '@/utils/request'

/**
 * 字典类型
 * @param
 * @returns
 */
export function dictTypeList(data) {
  return request({
    url: '/dict/typeList',
    method: 'post',
    data
  })
}

/**
 * 删除字典类型
 * @param
 * @returns
 */
export function dictTypeDelete(params) {
  return request({
    url: '/dict/type/delete',
    method: 'delete',
    params
  })
}

/**
 * 删除字典数据
 * @param
 * @returns
 */
export function dictDataDelete(params) {
  return request({
    url: '/dict/data/delete',
    method: 'delete',
    params
  })
}

/**
 * 新增或编辑字典类型
 * @param
 * @returns
 */
export function dictTypeSaveOrUpdate(data) {
  return request({
    url: '/dict/type/saveOrUpdate',
    method: 'post',
    data
  })
}

/**
 * 新增或编辑字典数据
 * @param
 * @returns
 */
export function dictDataSaveOrUpdate(data) {
  return request({
    url: '/dict/data/saveOrUpdate',
    method: 'post',
    data
  })
}

/**
 * 获取字典数据
 * @param
 * @returns
 */
export function dictDataList(data) {
  return request({
    url: '/dict/dataList',
    method: 'post',
    data
  })
}
