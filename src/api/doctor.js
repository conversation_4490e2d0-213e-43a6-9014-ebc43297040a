import request from '@/utils/request'

/**
 * 查询表格
 * @param
 * @returns
 */
export function doctorList(data) {
  return request({
    url: '/doctor/list',
    method: 'post',
    data
  })
}

/**
 * 新增医生
 * @param
 * @returns
 */
export function doctorAdd(data) {
  return request({
    url: '/doctor/add',
    method: 'post',
    data
  })
}

/**
 * 编辑医生
 * @param
 * @returns
 */
export function doctorUpdate(data) {
  return request({
    url: '/doctor/update',
    method: 'post',
    data
  })
}

/**
 * 删除医生
 * @param
 * @returns
 */
export function doctorDelete(params) {
  return request({
    url: '/doctor/delete',
    method: 'delete',
    params
  })
}

/**
 * 医生详情
 * @param
 * @returns
 */
export function doctorDetail(params) {
  return request({
    url: '/doctor/detail',
    method: 'get',
    params
  })
}
