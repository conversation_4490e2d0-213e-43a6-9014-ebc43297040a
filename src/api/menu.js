import request from '@/utils/request'
import {getToken,getWindowName} from '@/utils/auth'
const {saToken}  = getWindowName
/**
 * 应用菜单
 * @param {*} data 
 * @returns 
 */
 export function appsMenuList(params) {
  return request({
    url: '/auth/menu/list',
    method: 'get',
    // headers:{
    //   username:username
    // },
    params,
    headers:{
      saToken:getToken() || saToken
    },
    baseURL:'/show-portal'
  })
}