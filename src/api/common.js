import request from '@/utils/request'
import { getRoleId } from '@/utils/auth'
/**
 * 查询表格
 * @param
 * @returns
 */
export function getAllDataList(params) {
  return request({
    url: '/dict/dataList',
    method: 'get',
    params
  })
}
/**
 * 第一步渲染角色列表
 * @param {roleId:'当前登录的角色ID',roleName:''} data
 * @returns
 */
export function roleList(roleName) {
  const data = {
    roleId: getRoleId(),
    roleName: roleName
  }
  return request({
    url: '/role/list',
    method: 'post',
    data,
    baseURL: '/show-portal/back'
  })
}

/**
 * 文件上传
 *
 *
 */
export function fileUpload(data) {
  var sBoundary = '---------------------------' + Date.now().toString(16)
  return request({
    url: '/file/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data; boundary=' + sBoundary
    }
  })
}
