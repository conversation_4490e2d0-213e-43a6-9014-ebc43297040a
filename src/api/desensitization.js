import request from '@/utils/request'

/**
 * 列表
 * @param {pageNum:"1",pageSize:"",name:'',status:''} data
 * @returns
 */
export function desensitizeParamsConfigList(data) {
	return request({
		url: 'desensitize/list',
		method: 'post',
		data
	})
}
/**
 * 新增编辑
 * @param {dtCode:"1",dtMethod:"",dtName:'',rangeCode:'',rangeName:''} data
 * @returns
 */
export function desensitizeParamsConfigSave(data) {
	const { id } = data
	return request({
		url: `desensitize/${!id ? 'register' : 'update'}`,
		method: 'post',
		data
	})
}
// 删除
export function desensitizeParamsConfigDelete(data) {
	return request({
		url: 'desensitize/remove',
		method: 'post',
		data
	})
}
// 获取字典
export function getFieldList() {
	return request({
		url: 'basicMgr/desensitizeParamsConfig/getFieldList',
		method: 'get',
	})
}
// 验证
export function desensitizeParamsConfigTest(data) {
	return request({
		url: 'desensitize/verify',
		method: 'post',
		data
	})
}
