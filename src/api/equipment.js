import request from '@/utils/request'

/**
 * 设备列表
 * @param
 * @returns
 */
export function equipmentList(data) {
  return request({
    url: '/equipment/list',
    method: 'post',
    data
  })
}

/**
 * 删除字典类型
 * @param
 * @returns
 */
export function equipmentDelete(params) {
  return request({
    url: '/equipment/delete',
    method: 'delete',
    params
  })
}

/**
 * 新增或编辑设备
 * @param
 * @returns
 */
export function equipmentSaveOrUpdate(data) {
  return request({
    url: '/equipment/saveOrUpdate',
    method: 'post',
    data
  })
}

/**
 * 获取设备详情
 * @param
 * @returns
 */
export function equipmentDetail(params) {
  return request({
    url: '/equipment/detail',
    method: 'get',
    params
  })
}
