import request from '@/utils/request-back'
/**
 * 查询表格
 * @param {title:'',startTime:'',endTime:'',pageNum:1,pageSize:10} data
 * @returns
 */
export function noticeList(data) {
  return request({
    url: '/notice/list',
    method: 'post',
    data
  })
}
/**
 * 新增
 * @param {title:'',content:'',fileIds:'文件ID 多个用,分割',createUser:10} data
 * @returns
 */
export function noticeRegister(data) {
  return request({
    url: '/notice/register',
    method: 'post',
    data
  })
}
export function noticeUpdate(data) {
  return request({
    url: '/notice/update',
    method: 'post',
    data
  })
}
/**
 * 编辑
 * @param {title:'',content:'',fileIds:'文件ID 多个用,分割',createUser:'',id:''} data
 * @returns
 */
export function announcementUpdate(data) {
  return request({
    url: '/announcement/update',
    method: 'post',
    data
  })
}
/**
 * 删除
 * @param {id:''} data
 * @returns
 */
export function noticeRemove(data) {
  return request({
    url: '/notice/remove',
    method: 'post',
    data
  })
}
/**
 * 详情
 * @param {id:''} data
 * @returns
 */
export function noticeInfo(data) {
  return request({
    url: '/notice/info',
    method: 'post',
    data
  })
}
export function getUserList() {}
/**
 * 文件上传
 * @param {file:'文件路径'} data
 * @returns
 */
export function announcementUploadFile(data) {
  return request({
    url: '/announcement/upload-file',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  })
}
/**
 * 文件下载
 * @param {filePath:''} data
 * @returns
 */
export function announcementDownLoad(data) {
  return request({
    url: '/announcement/download',
    method: 'post',
    responseType: 'blob',
    data
  })
}
/**
 * 获取科室列表
 * @param {} data
 * @returns
 */
export function getDeptList() {
  return request({
    url: 'user/deptList',
    method: 'get'
  })
}
/**
 * 获取所有用户列表
 * @param {} data
 * @returns
 */
export function getAllUserList() {
  return request({
    url: 'user/getAll',
    method: 'get'
  })
}
/**
 * 获取所有科室和用户列表
 * @param {} data
 * @returns
 */
export function getDeptUserList() {
  return request({
    url: 'user/deptWithUserList',
    method: 'get'
  })
}
