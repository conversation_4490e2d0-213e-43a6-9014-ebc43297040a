import request from '@/utils/request'
import { getRoleId } from '@/utils/auth'
/**
 * 第一步渲染角色列表
 * @param {roleId:'当前登录的角色ID',roleName:''} data 
 * @returns 
 */
export function roleList(roleName) {
  const data = {
    roleId: getRoleId(),
    roleName: roleName
  }
  return request({
    url: '/role/list',
    method: 'post',
    data
  })
}
/**
 * 上级角色
 * @param {roleId:'当前登录的角色ID'} data 
 * @returns 
 */
export function roleParentList() {
  const data = {
    roleId: getRoleId(),
  }
  return request({
    url: '/role/parentList',
    method: 'post',
    data
  })
}
/**
 * 角色新增
 * @param {name:'角色名称',parentId:'角色父ID 0最顶级',info:'描述',sort:'排序'} data 
 * @returns 
 */
export function roleRegister(data) {
  return request({
    url: '/role/register',
    method: 'post',
    data
  })
}
/**
 * 角色修改
 * @param {name:'角色名称',parentId:'角色父ID 0最顶级',info:'描述',sort:'排序',id:'角色ID'} data 
 * @returns 
 */
export function roleUpdate(data) {
  return request({
    url: '/role/update',
    method: 'post',
    data
  })
}
/**
 * 角色删除
 * @param {id:'角色ID'} data 
 * @returns 
 */
export function roleRemove(data) {
  return request({
    url: '/role/remove',
    method: 'post',
    data
  })
}
/**
 * 获取应用列表
 * @param {pageNum:'1',pageSize:'100'} data 
 * @returns 
 */
export function appList(data) {
  return request({
    url: '/app/getAll',
    method: 'get',
    data
  })
}
/**
 * 角色菜单查询
 * @param {roleId:''} data 
 * @returns 
 */
export function roleMenuList(data) {
  return request({
    url: '/role/menuList',
    method: 'post',
    data
  })
}
/**
 * 角色应用查询
 * @param {roleId:''} data 
 * @returns 
 */
export function roleAppList(data) {
  return request({
    url: '/role/appList',
    method: 'post',
    data
  })
}
/**
 * 角色菜单应用保存
 * @param {roleId:''} data 
 * @returns 
 */
export function rolePermissionSave(data) {
  return request({
    url: '/role/permission-save',
    method: 'post',
    data
  })
}

