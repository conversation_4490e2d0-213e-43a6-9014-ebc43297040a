import request from '@/utils/request'

/**
 * 查询表格
 * @param {name:'用户姓名',deptName:'科室名称',pageNum:'1',pageSize:10} data
 * @returns
 */
export function userList(data) {
  return request({
    url: '/user/list',
    method: 'post',
    data
  })
}
/**
 * 编辑
 * @param {id:'',dataScope:'数据权限'} data
 * @returns
 */
export function userUpdate(data) {
  return request({
    url: '/user/update',
    method: 'post',
    data
  })
}
/**
 * 删除
 * @param {id:'',userName:'工号'} data
 * @returns
 */
export function userRemove(data) {
  return request({
    url: '/user/remove',
    method: 'post',
    data
  })
}
/**
 * 删除
 * @param {userIds:'选中用户ID',roleIds:'选中角色ID'} data
 * @returns
 */
export function userRoleSave(data) {
  return request({
    url: '/user/role-save',
    method: 'post',
    data
  })
}
/**
 * 模板下载
 * @param {} data
 * @returns
 */
export function downloadTemplate(data) {
  return request({
    url: '/user/download/template',
    method: 'get',
    responseType: 'blob',
    data
  })
}
/**
 * 用户导入上传
 * @param {} data
 * @returns
 */
export function importExcel(data) {
  return request({
    url: '/user/importExcel',
    method: 'post',
    data
  })
}
/**
 * 用户解锁
 * @param {id:'用户ID',userName:'工号'} data
 * @returns
 */
export function deBlocking(data) {
  return request({
    url: '/user/deBlocking',
    method: 'post',
    data
  })
}
/**
 * 一键重置密码
 * @param {username:'',oldPassword:''} data
 * @returns
 */
export function resetPwd(data) {
  return request({
    url: 'user/reset/pwd',
    method: 'post',
    data,
    baseURL: '/show-portal/back'
  })
}
/**
 * 修改密码
 * @param {username:'',oldPassword:'',newPassword1:'',newPassword2:''} data
 * @returns
 */
export function updatePwd(data) {
  return request({
    url: 'user/update/pwd',
    method: 'post',
    data,
    baseURL: '/show-portal/back'
  })
}
/**
 * 带入密码
 * @param {username:'',oldPassword:'',newPassword1:'',newPassword2:''} data
 * @returns
 */
export function queryPwd(data) {
  return request({
    url: 'user/query/pwd',
    method: 'post',
    data,
    baseURL: '/show-portal/back'
  })
}
