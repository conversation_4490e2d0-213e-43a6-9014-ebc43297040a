import request from '@/utils/request'
/**
 * 菜单列表
 * @param {appId:''} data 
 * @returns 
 */
export function menuList(data) {
  return request({
    url: '/menu/list',
    method: 'post',
    data
  })
}
/**
 * 菜单新增
 * @param {name:'',parentId:'',appId:'',icon:'',url:'',permission:'',sort:'',type:''} data 
 * @returns 
 */
export function menuRegister(data) {
  return request({
    url: '/menu/register',
    method: 'post',
    data
  })
}
/**
 * 菜单更新
 * @param {id:''',name:'',parentId:'',appId:'',icon:'',url:'',permission:'',sort:'',type:''} data 
 * @returns 
 */
export function menuUpdate(data) {
  return request({
    url: '/menu/update',
    method: 'post',
    data
  })
}
/**
 * 菜单删除
 * @param {id:''} data 
 * @returns 
 */
export function menuRemove(data) {
  return request({
    url: '/menu/remove',
    method: 'post',
    data
  })
}
/**
 * 左侧列表
 * @param {pageSize:10,pageNum:1} data 
 * @returns 
 */
export function appGetAll(data) {
  return request({
    url: '/app/getAll',
    method: 'get',
    data
  })
}
