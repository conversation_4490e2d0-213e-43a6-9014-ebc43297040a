import request from '@/utils/request'
import { getToken, getWindowName } from '@/utils/auth'
const { saToken } = getWindowName

/**
 * 查询机构
 * @param data
 * @returns
 */
export function agencies() {
  return request({
    url: '/common/agencies',
    method: 'get',
    headers: {
      saToken: getToken() || saToken
    }
  })
}
/**
 * 查询机构
 * @param data
 * @returns
 */
export function dataList(dictType) {
  return request({
    url: '/dict/dataList?dictType=' + dictType,
    method: 'get',
    headers: {
      saToken: getToken() || saToken
    }
  })
}

/**
 * 查询机构
 * @param data
 * @returns
 */
export function detailId(id) {
  return request({
    url: '/score/detail?doctorId=' + id,
    method: 'get',
    headers: {
      saToken: getToken() || saToken
    }
  })
}


/**
 * 查询表格
 * @param data
 * @returns
 */
export function record(data) {
  return request({
    url: '/expert/list',
    method: 'post',
    data
  })
}

/**
 * 打分
 * @param data
 * @returns
 */
export function grade(data) {
  return request({
    url: '/score/grade',
    method: 'post',
    data
  })
}

/**
 * 查询表格
 * @param data
 * @returns
 */
export function score(data) {
  return request({
    url: '/score/list',
    method: 'post',
    data
  })
}


/**
 * 更新详情
 * @param data
 * @returns
 */
export function updateInfo(params) {
  return request({
    url: '/eHealthCard/updateInfo',
    method: 'get',
    params,
    headers: {
      saToken: getToken() || saToken
    }
  })
}
