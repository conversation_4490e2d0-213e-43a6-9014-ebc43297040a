import Vue from 'vue'
import Router from 'vue-router'
import Header from '@/components/head.vue'
import HomeView from '../components/head.vue'
import { Message } from 'element-ui'
Vue.use(Router)

/* Layout */
import Layout from '@/layout'
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/notFound',
    component: () => import('@/views/notFound'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  }
]

export const asyncRoutes = [
  {
    path: '/',
    component: Header,
    component: Layout,
    redirect: '/structure',
    name: 'Structure',
    children: [
      {
        path: 'structure',
        component: () => import('@/views/structure/index.vue'),
        meta: {
          title: '机构维护',
          affix: true,
          icon: 'home'
        }
      }
    ]
  },
  {
    path: '/doctor',
    component: Layout,
    name: 'Doctor',
    children: [
      {
        path: 'index',
        component: () => import('@/views/doctor/index.vue'),
        meta: {
          title: '卫生技术人员信息维护',
          affix: true,
          icon: 'appAdmin'
        }
      }
    ]
  },
  {
    path: '/dictionary',
    component: Layout,
    name: 'Dictionary',
    children: [
      {
        path: 'index',
        component: () => import('@/views/dictionary/index.vue'),
        meta: {
          title: '数据字典',
          affix: true,
          icon: 'appTableAdmin'
        }
      }
    ]
  },
  {
    path: '/equipment',
    component: Layout,
    name: 'Equipment',
    children: [
      {
        path: 'index',
        component: () => import('@/views/equipment/index.vue'),
        meta: {
          title: '医疗设备管理',
          affix: true,
          icon: 'appAdmin'
        }
      }
    ]
  },
  {
    path: '/expert',
    component: Layout,
    name: 'Expert',
    children: [
      {
        path: 'index',
        component: () => import('@/views/expert/index.vue'),
        meta: {
          title: '专家库分类管理',
          affix: true,
          icon: 'appTableAdmin'
        }
      }
    ]
  },
  {
    path: '/grade',
    component: Layout,
    name: 'Grade',
    children: [
      {
        path: 'index',
        component: () => import('@/views/grade/index.vue'),
        meta: {
          title: '医生从业不良积分管理',
          affix: true,
          icon: 'appAdmin'
        }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

// TODO 此处为搭配权限使用完整的动态路由 需搭配 let newRoutes = formatRoute(recursion(menuList)) 使用 在集成平台管理->应用菜单管理找到对应的应用添加名称
export function formatRoute(routers) {
  const res = []
  routers.map((route, idx) => {
    if (route.children) {
      route.children = formatRoute(route.children)
    }
    // 一级目录
    if (route.type === '1' && route.parentId === 0) {
      route.component = Layout
      route.redirect = route.children[0] ? route.children[0].path : '/'
      // 为目录，二级目录
    } else if (route.type === '1' && route.parentId !== 0) {
      route.component = importComponent(route.requriePath)
      // 为菜单，不是一级菜单
    } else if (route.type === '2' && route.parentId !== 0) {
      route.component = importComponent(route.requriePath)
      // 菜单，一级菜单
    } else if (route.type === '2' && route.parentId === 0) {
      route.component = Layout
      route.redirect = route.path
      delete route.parentId
      delete route.type
      route.children.push({
        name: 'Index' + idx,
        path: route.path,
        meta: {
          title: route.label
        },
        component: importComponent(route.requriePath)
      })
    }

    res.push(route)
  })
  return res
}
// 动态引入菜单，以及没有菜单时的提示
export function importComponent(component) {
  return function (resolve) {
    return require([`@/views/${component}/index`], resolve).catch(e => {
      Message.warning(`请检查应用菜单${component},暂无当前页面信息`)
      require([`@/views/notFound.vue`], resolve)
    })
  }
}

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
