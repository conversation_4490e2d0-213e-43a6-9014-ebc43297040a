import Cookies from 'js-cookie'
import { configSetting } from '../../public/config'
const { tokenKey } = configSetting
const TokenKey = tokenKey

export function getToken() {
  return Cookies.get(TokenKey)
  // return getWindowName().saToken
}

export function setToken(token) {
  // return Cookies.set(TokenKey, token)
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

const roleIdKey = 'roleId'
export function getRoleId() {
  return Cookies.get(roleIdKey)
}
export function setRoleId(roleId) {
  return Cookies.set(roleIdKey, roleId)
}

export function getAppId() {
  return Cookies.get('show-permission')
}
export function setAppId(appId) {
  return Cookies.set('show-permission', appId)
}

export function getLoginName() {
  return Cookies.get('loginName')
}
export function setLoginName(loginName) {
  return Cookies.set('loginName', loginName)
}

export function getLoginUserName() {
  return Cookies.get('loginUserName')
}
export function setLoginUserName(loginUserName) {
  return Cookies.set('loginUserName', loginUserName)
}
export function getWindowName() {
  if (window.name) {
    let windowName = JSON.parse(window.name)
    setAppId(windowName.appId)
    setLoginName(windowName.loginName)
    setRoleId(windowName.roleId)
    setToken(windowName.saToken)
    setLoginUserName(windowName.loginUserName)
    return windowName
  } else {
    return {
      roleId: getRoleId(),
      appId: getAppId(),
      loginName: getLoginName(),
      saToken: getToken(),
      loginUserName: getLoginUserName()
    }
  }
}
