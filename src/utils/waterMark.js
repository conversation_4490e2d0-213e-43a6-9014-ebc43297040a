let waterMark = {}

// 判断接受值是否是对象
const isObject = data => {
  return Object.prototype.toString.call(data) === '[object Object]'
}

let setWatermark = (text, sourceBody) => {
  let id = '1.23452384164.123412416'

  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id))
  }

  let can = document.createElement('canvas')
  can.width = 300
  can.height = 200

  let cans = can.getContext('2d')
  cans.rotate(((text.rotate || -20) * Math.PI) / 180)
  cans.font = text.font || '18px Vedana'
  cans.fillStyle = text.fillStyle || 'rgba(0, 0, 0, .17)'
  cans.textAlign = 'left'
  cans.textBaseline = 'Middle'
  if (isObject(text)) {
    //对象类型
    //自行修改传入对象的属性名-->firstLine、secondLine
    cans.fillText(text.firstLine, can.width / 20, can.height)
    cans.fillText(text.secondLine, can.width / 20, can.height + 20)
  } else {
    //字符串类型
    cans.fillText(text, can.width / 20, can.height)
  }

  let water_div = document.createElement('div')
  water_div.id = id
  water_div.style.pointerEvents = 'none'
  water_div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat'
  if (sourceBody) {
    water_div.style.width = '100%'
    water_div.style.height = '100%'
    sourceBody.appendChild(water_div)
  } else {
    water_div.style.top = '3px'
    water_div.style.left = '0px'
    water_div.style.position = 'fixed'
    water_div.style.zIndex = '100000'
    water_div.style.width = document.documentElement.clientWidth + 'px'
    water_div.style.height = document.documentElement.clientHeight + 'px'
    document.body.appendChild(water_div)
  }

  return id
}

/**
 *  该方法只允许调用一次
 *  @param:
 *  @text == 水印内容,字符串或对象类型
 *  @text属性名 == firstLine 和 secondLine font fillStylerotate
 *  @sourceBody == 水印添加在哪里，不传就是body
 * */
let timer = null
waterMark.set = (text, sourceBody) => {
  let id = setWatermark(text, sourceBody)
  timer = setInterval(() => {
    if (document.getElementById(id) === null) {
      id = setWatermark(text, sourceBody)
    }
  }, 2000)
  window.onresize = () => {
    setWatermark(text, sourceBody)
  }
}
waterMark.remove = () => {
  let id = '1.23452384164.123412416'
  clearInterval(timer)
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id))
  }
}

export default waterMark
