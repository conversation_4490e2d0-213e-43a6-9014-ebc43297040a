import CryptoJS from 'crypto-js'
// 密钥 YJZL@2022@GM57sN
/**
  * @param {*需要加密的字符串 注：对象转化为json字符串再加密} word
  */
 // 加密
export function encrypt(word, keyStr) {
  if(Object.prototype.toString.call(word)==='[object Object]'){
    word = JSON.stringify(word)
  }
	var key = CryptoJS.enc.Utf8.parse(keyStr)
	var srcs = CryptoJS.enc.Utf8.parse(word)
	var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 }) // 加密模式为ECB，补码方式为PKCS5Padding（也就是PKCS7）
	return encrypted.toString()
  
}
/**
 * @param {*aes加密需要的key值，这个key值后端同学会告诉你} keyStrF
 */
// 解密
export function decrypt(word, keyStr) {
	var key = CryptoJS.enc.Utf8.parse(keyStr)
	var decrypt = CryptoJS.AES.decrypt(word, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 })
	return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}