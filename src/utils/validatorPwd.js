/**
 * 
 * @param {*} rule 校验
 * @param {*} value 密码
 * @param {*} callback 返回函数
 */
 export function validatorPwd(rule, value, callback) {
  if (value.length < 8) {
    return callback('密码中必须包含字母、数字和特殊字符,特殊符号仅包括英文字符的@#.%,并且最少8位！')
  } else {
    if (/([0-9a-zA-Z])\1{1,}/.test(value)) {
      return callback('您输入有相同的字符！')
    } else if (!/^(?![\d]+$)(?![a-zA-Z]+$)(?![^\da-zA-Z]+$).{1,}$/.test(value)) {
      return callback('您输入的密码不符合规范，数字+字母/数字+特殊字符/字母+特殊字符！')
    } else if (!checkIsContinuity(value)) {
      return callback('您输入的密码有连续递增或递减！')
    }
    else {
      return callback()
    }
  }
}
/**
 * 校验密码有没有连续递增
 * @param {*} value 密码 
 * @returns 
 */
function checkIsContinuity(value) {
  var arr = value.split('');
  var flag = true;
  for (var i = 1; i < arr.length - 1; i++) {
    var firstIndex = arr[i - 1].charCodeAt();
    var secondIndex = arr[i].charCodeAt();
    var thirdIndex = arr[i + 1].charCodeAt();
    thirdIndex - secondIndex == 1;
    secondIndex - firstIndex == 1;
    if (((thirdIndex - secondIndex == 1) && (secondIndex - firstIndex == 1)) || ((thirdIndex - secondIndex == -1) && (secondIndex - firstIndex == -1))) {
      flag = false;
    }
  }
  return flag;
}
